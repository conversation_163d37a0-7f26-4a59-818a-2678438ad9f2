"""
Check Excel file structure to understand the data format
"""

import pandas as pd
import os

def check_excel_structure():
    """Check the structure of the Excel files"""
    
    filename = 'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx'
    
    if not os.path.exists(filename):
        print(f"File not found: {filename}")
        return
    
    try:
        # Check sheet names
        xl = pd.ExcelFile(filename)
        print(f"Sheet names: {xl.sheet_names}")
        
        # Check each sheet
        for sheet in xl.sheet_names:
            print(f"\n=== Sheet: {sheet} ===")
            df = pd.read_excel(filename, sheet_name=sheet)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            if len(df) > 0:
                print("Sample data:")
                print(df.head())
                
                # Check for indicator columns
                indicator_cols = [col for col in df.columns if any(ind in col for ind in ['PGO', 'CCI', 'SMI', 'BIAS', 'CG'])]
                if indicator_cols:
                    print(f"Found indicator columns: {indicator_cols}")
                else:
                    print("No indicator columns found")
            
    except Exception as e:
        print(f"Error reading file: {str(e)}")

if __name__ == "__main__":
    check_excel_structure()
