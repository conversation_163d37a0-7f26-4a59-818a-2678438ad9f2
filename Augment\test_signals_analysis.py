"""
Test Signals Analysis - Debug and Fix

Test script to debug why no signals are available for analysis and fix all errors.
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_realistic_mock_data(interval_minutes=1, start_time="09:15", end_time="15:30"):
    """Create realistic mock market data with price movements"""
    
    start_dt = datetime.strptime(f"2025-06-30 {start_time}", "%Y-%m-%d %H:%M")
    end_dt = datetime.strptime(f"2025-06-30 {end_time}", "%Y-%m-%d %H:%M")
    
    times = []
    current = start_dt
    while current <= end_dt:
        times.append(current)
        current += timedelta(minutes=interval_minutes)
    
    # Create realistic price data with trends and volatility
    import random
    random.seed(42)  # For reproducible results
    
    base_price = 100.0
    data = []
    
    for i, time in enumerate(times):
        # Add some trend and random movement
        trend = 0.01 * i  # Slight upward trend
        noise = random.uniform(-0.5, 0.5)  # Random noise
        
        open_price = base_price + trend + noise
        high_price = open_price + random.uniform(0.1, 0.8)
        low_price = open_price - random.uniform(0.1, 0.6)
        close_price = open_price + random.uniform(-0.3, 0.4)
        volume = random.randint(1000, 5000)
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
        
        base_price = close_price  # Next candle starts from previous close
    
    df = pd.DataFrame(data)
    df.set_index('time', inplace=True)
    df.reset_index(inplace=True)
    
    return df

def test_backtester_signals():
    """Test if the backtester can generate signals"""
    
    logger.info("Testing backtester signal generation...")
    
    try:
        # Import backtester
        smart_backtester = __import__('smart_vectorized_backtester copy')
        SmartVectorizedBacktester = smart_backtester.SmartVectorizedBacktester
        
        # Create a test backtester instance
        backtester = SmartVectorizedBacktester(
            ticker="ACC",
            exchange="NSE",
            start="09:15",
            end="15:30",
            date="30-06-2025",
            tokenid="1465"  # ACC token
        )
        
        logger.info("✅ Backtester instance created successfully")
        
        # Check if run_smart_vectorized_backtest method exists
        if hasattr(backtester, 'run_smart_vectorized_backtest'):
            logger.info("✅ run_smart_vectorized_backtest method found")
            
            # Try to run it (this might fail due to API, but we can catch the error)
            try:
                signals = backtester.run_smart_vectorized_backtest()
                logger.info(f"✅ Backtester ran successfully, signals: {len(signals) if signals else 0}")
                return signals
            except Exception as e:
                logger.warning(f"⚠️ Backtester execution failed (expected due to API): {e}")
                return None
        else:
            logger.error("❌ run_smart_vectorized_backtest method not found")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error testing backtester: {e}")
        return None

def test_signals_analysis_with_mock_data():
    """Test signals analysis with mock data and fallback logic"""
    
    logger.info("Testing signals analysis with mock data...")
    
    try:
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Create realistic mock data
        mock_data = create_realistic_mock_data(interval_minutes=1)
        logger.info(f"Created mock data: {len(mock_data)} candles")
        logger.info(f"Price range: {mock_data['Close'].min():.2f} to {mock_data['Close'].max():.2f}")
        
        # Test signals analysis
        result = analyzer._analyze_signals_with_backtester(
            ticker="ACC",
            exchange="NSE",
            date="30-06-2025",
            market_data=mock_data,
            method="extension",
            categories=["volatility"],
            include_history=True,
            interval="1"
        )
        
        logger.info(f"Signals analysis result: {result}")
        
        if 'error' in result:
            logger.error(f"❌ Signals analysis failed: {result['error']}")
            return False
        else:
            logger.info(f"✅ Signals analysis successful:")
            logger.info(f"   Total signals: {result.get('total_signals', 0)}")
            logger.info(f"   Interval used: {result.get('interval_used', 'Unknown')}")
            logger.info(f"   Data points: {result.get('data_points', 0)}")
            
            # Check if we have actual signal results
            signals = result.get('signals', {})
            if signals:
                logger.info(f"   Signal times: {list(signals.keys())[:5]}...")
                return True
            else:
                logger.warning("⚠️ No signal results found")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error in signals analysis test: {e}")
        return False

def test_fallback_signal_generation():
    """Test the fallback signal generation logic"""
    
    logger.info("Testing fallback signal generation...")
    
    try:
        # Create mock data
        mock_data = create_realistic_mock_data(interval_minutes=1)
        
        # Test fallback logic manually
        signal_times = []
        if len(mock_data) > 0:
            # Use every 30th candle as signal times for analysis
            step = max(1, len(mock_data) // 10)  # Get ~10 analysis points
            for i in range(0, len(mock_data), step):
                time_str = mock_data.iloc[i]['time'].strftime('%H:%M')
                signal_times.append(time_str)
        
        logger.info(f"Generated {len(signal_times)} fallback signal times: {signal_times}")
        
        if signal_times:
            logger.info("✅ Fallback signal generation working")
            return signal_times
        else:
            logger.error("❌ Fallback signal generation failed")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error in fallback signal generation: {e}")
        return []

def test_time_matching_logic():
    """Test the time matching logic in signals analysis"""
    
    logger.info("Testing time matching logic...")
    
    try:
        # Create mock data
        mock_data = create_realistic_mock_data(interval_minutes=1)
        
        # Test signal times
        test_signal_times = ["09:30", "10:15", "12:00", "14:30"]
        
        for signal_time in test_signal_times:
            logger.info(f"Testing signal time: {signal_time}")
            
            # Find exact match
            signal_index = None
            for i, row in mock_data.iterrows():
                if row['time'].strftime('%H:%M') == signal_time:
                    signal_index = mock_data.index.get_loc(i)
                    logger.info(f"  ✅ Exact match found at index {signal_index}")
                    break
            
            if signal_index is None:
                # Find closest match
                time_diffs = []
                for i, row in mock_data.iterrows():
                    time_str = row['time'].strftime('%H:%M')
                    try:
                        signal_dt = datetime.strptime(signal_time, '%H:%M').time()
                        data_dt = datetime.strptime(time_str, '%H:%M').time()
                        diff = abs((datetime.combine(datetime.today(), signal_dt) - 
                                  datetime.combine(datetime.today(), data_dt)).total_seconds())
                        time_diffs.append((i, diff))
                    except:
                        continue
                
                if time_diffs:
                    closest_idx, min_diff = min(time_diffs, key=lambda x: x[1])
                    signal_index = mock_data.index.get_loc(closest_idx)
                    closest_time = mock_data.iloc[signal_index]['time'].strftime('%H:%M')
                    logger.info(f"  ✅ Closest match found at index {signal_index}, time {closest_time} (diff: {min_diff}s)")
                else:
                    logger.warning(f"  ❌ No match found for {signal_time}")
        
        logger.info("✅ Time matching logic test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in time matching test: {e}")
        return False

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Signals Analysis Debug and Fix...")
    
    # Test 1: Backtester signals
    logger.info("\n" + "="*60)
    logger.info("TEST 1: BACKTESTER SIGNAL GENERATION")
    logger.info("="*60)
    signals = test_backtester_signals()
    
    # Test 2: Fallback signal generation
    logger.info("\n" + "="*60)
    logger.info("TEST 2: FALLBACK SIGNAL GENERATION")
    logger.info("="*60)
    fallback_signals = test_fallback_signal_generation()
    
    # Test 3: Time matching logic
    logger.info("\n" + "="*60)
    logger.info("TEST 3: TIME MATCHING LOGIC")
    logger.info("="*60)
    time_matching_ok = test_time_matching_logic()
    
    # Test 4: Full signals analysis
    logger.info("\n" + "="*60)
    logger.info("TEST 4: FULL SIGNALS ANALYSIS")
    logger.info("="*60)
    analysis_ok = test_signals_analysis_with_mock_data()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    if analysis_ok:
        logger.info("✅ Signals analysis is working correctly!")
    else:
        logger.info("❌ Signals analysis needs fixes:")
        if not fallback_signals:
            logger.info("  - Fallback signal generation failed")
        if not time_matching_ok:
            logger.info("  - Time matching logic failed")
        logger.info("  - Check the logs above for specific issues")
    
    logger.info("Debug completed!")

if __name__ == "__main__":
    main()
