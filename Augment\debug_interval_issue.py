"""
Debug Interval Issue

Test script to debug why the interval parameter is not working correctly.
"""

import sys
import os
import argparse
import logging

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cli_parsing():
    """Test CLI argument parsing"""
    
    logger.info("Testing CLI argument parsing...")
    
    # Import the CLI parser
    from integrated_technical_analyzer import create_cli_parser
    
    parser = create_cli_parser()
    
    # Test command line arguments
    test_args = [
        '--mode', 'analysis',
        '--analysis-type', 'signals',
        '--ticker', 'ACC',
        '--exchange', 'NSE',
        '--date', '30-06-2025',
        '--method', 'extension',
        '--categories', 'volatility',
        '--interval', '1'
    ]
    
    args = parser.parse_args(test_args)
    
    logger.info(f"Parsed arguments:")
    logger.info(f"  mode: {args.mode}")
    logger.info(f"  analysis_type: {args.analysis_type}")
    logger.info(f"  ticker: {args.ticker}")
    logger.info(f"  exchange: {args.exchange}")
    logger.info(f"  date: {args.date}")
    logger.info(f"  method: {args.method}")
    logger.info(f"  categories: {args.categories}")
    logger.info(f"  interval: {args.interval} (type: {type(args.interval)})")
    
    return args

def test_interval_processing():
    """Test how interval is processed in the analyzer"""
    
    logger.info("Testing interval processing...")
    
    # Test different interval values
    test_intervals = ['1', '5', '15', '30', '60']
    
    for interval in test_intervals:
        logger.info(f"Testing interval: {interval}")
        
        # Test conversion to int
        try:
            interval_int = int(interval)
            logger.info(f"  Converted to int: {interval_int}")
        except ValueError as e:
            logger.error(f"  Error converting to int: {e}")
        
        # Test validation
        valid_intervals = ["1", "3", "5", "10", "15", "30", "60", "120", "240"]
        if interval in valid_intervals:
            logger.info(f"  ✅ Valid interval")
        else:
            logger.warning(f"  ❌ Invalid interval")

def test_market_data_method():
    """Test the get_market_data method signature"""
    
    logger.info("Testing get_market_data method...")
    
    try:
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Check method signature
        import inspect
        sig = inspect.signature(analyzer.get_market_data)
        logger.info(f"get_market_data signature: {sig}")
        
        # Check if interval parameter exists
        params = sig.parameters
        if 'interval' in params:
            interval_param = params['interval']
            logger.info(f"  interval parameter: {interval_param}")
            logger.info(f"  default value: {interval_param.default}")
        else:
            logger.error("  ❌ interval parameter not found!")
        
    except Exception as e:
        logger.error(f"Error testing get_market_data method: {e}")

def test_analyze_with_market_data_method():
    """Test the analyze_with_market_data method signature"""
    
    logger.info("Testing analyze_with_market_data method...")
    
    try:
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Check method signature
        import inspect
        sig = inspect.signature(analyzer.analyze_with_market_data)
        logger.info(f"analyze_with_market_data signature: {sig}")
        
        # Check if interval parameter exists
        params = sig.parameters
        if 'interval' in params:
            interval_param = params['interval']
            logger.info(f"  interval parameter: {interval_param}")
            logger.info(f"  default value: {interval_param.default}")
        else:
            logger.error("  ❌ interval parameter not found!")
        
    except Exception as e:
        logger.error(f"Error testing analyze_with_market_data method: {e}")

def test_progressive_calculator():
    """Test the progressive calculator interval handling"""
    
    logger.info("Testing progressive calculator...")
    
    try:
        from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator
        
        calculator = CompleteProgressiveIndicatorsCalculator()
        
        # Check method signature
        import inspect
        sig = inspect.signature(calculator.calculate_all_indicators_progressive)
        logger.info(f"calculate_all_indicators_progressive signature: {sig}")
        
        # Check parameters
        params = sig.parameters
        for param_name, param in params.items():
            logger.info(f"  {param_name}: {param}")
        
    except Exception as e:
        logger.error(f"Error testing progressive calculator: {e}")

def main():
    """Main debug function"""
    
    logger.info("🚀 Starting Interval Issue Debug...")
    
    # Test 1: CLI parsing
    logger.info("\n" + "="*60)
    logger.info("TEST 1: CLI ARGUMENT PARSING")
    logger.info("="*60)
    args = test_cli_parsing()
    
    # Test 2: Interval processing
    logger.info("\n" + "="*60)
    logger.info("TEST 2: INTERVAL PROCESSING")
    logger.info("="*60)
    test_interval_processing()
    
    # Test 3: Method signatures
    logger.info("\n" + "="*60)
    logger.info("TEST 3: METHOD SIGNATURES")
    logger.info("="*60)
    test_market_data_method()
    test_analyze_with_market_data_method()
    
    # Test 4: Progressive calculator
    logger.info("\n" + "="*60)
    logger.info("TEST 4: PROGRESSIVE CALCULATOR")
    logger.info("="*60)
    test_progressive_calculator()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("DEBUG SUMMARY")
    logger.info("="*60)
    logger.info("Check the logs above to identify where the interval parameter is being lost or overridden.")
    logger.info("The issue is likely in one of these areas:")
    logger.info("1. CLI argument parsing")
    logger.info("2. Method parameter passing")
    logger.info("3. Progressive calculator interval handling")
    logger.info("4. Default value override")
    
    logger.info("Debug completed!")

if __name__ == "__main__":
    main()
