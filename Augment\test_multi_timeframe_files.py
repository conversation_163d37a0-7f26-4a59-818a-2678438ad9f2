"""
Test Multi-Timeframe Files

Quick test to check which files have the Time_Series_Indicators sheet
and can be used for multi-timeframe analysis.
"""

import pandas as pd
import os
from datetime import datetime

def test_excel_files():
    """Test all Natural Gas Excel files"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Find all Natural Gas files
    ng_files = []
    for file in os.listdir(current_dir):
        if 'NATURALGAS26AUG25' in file and file.endswith('.xlsx') and not file.startswith('~$'):
            ng_files.append(file)
    
    print(f"Found {len(ng_files)} Natural Gas files:")
    
    working_files = {}
    
    for file in ng_files:
        print(f"\n📁 Testing: {file}")
        try:
            filepath = os.path.join(current_dir, file)
            excel_file = pd.ExcelFile(filepath)
            sheets = excel_file.sheet_names
            print(f"   📋 Sheets: {sheets}")
            
            if 'Time_Series_Indicators' in sheets:
                # Try to load the data
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                print(f"   ✅ Time_Series_Indicators sheet found: {df.shape}")
                
                # Check if it has indicator data
                if df.shape[1] > 50:  # Should have many indicators
                    working_files[file] = {
                        'shape': df.shape,
                        'sheets': sheets,
                        'indicators': df.shape[1]
                    }
                    print(f"   🎯 USABLE for analysis")
                else:
                    print(f"   ⚠️ Too few indicators: {df.shape[1]}")
            else:
                print(f"   ❌ No Time_Series_Indicators sheet")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n🎯 SUMMARY: {len(working_files)} usable files found")
    for file, info in working_files.items():
        print(f"   ✅ {file}: {info['indicators']} indicators, {info['shape'][0]} time points")
    
    return working_files

def create_manual_multi_timeframe_analysis():
    """Create multi-timeframe analysis using existing files"""
    working_files = test_excel_files()
    
    if len(working_files) < 2:
        print("\n❌ Need at least 2 files for multi-timeframe analysis")
        return
    
    print(f"\n🔄 CREATING MULTI-TIMEFRAME ANALYSIS")
    print("=" * 60)
    
    # Use the available files as different timeframes
    timeframe_data = {}
    file_list = list(working_files.keys())
    
    # Assign timeframes to files (we'll treat them as different intervals)
    timeframe_names = ['1min', '5min', '15min', '30min']
    
    for i, file in enumerate(file_list[:4]):  # Use up to 4 files
        timeframe = timeframe_names[i] if i < len(timeframe_names) else f'tf{i+1}'
        
        try:
            filepath = os.path.join(os.path.dirname(os.path.abspath(__file__)), file)
            df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
            
            # Transpose if needed
            if len(df.columns) > len(df):
                df_transposed = df.set_index(df.columns[0]).T
            else:
                first_col = df.iloc[:, 0]
                if any(':' in str(val) for val in first_col.head(10)):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    df_transposed = df
            
            # Clean data
            for col in df_transposed.columns:
                df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
            
            timeframe_data[timeframe] = df_transposed
            print(f"✅ Loaded {timeframe}: {df_transposed.shape} from {file}")
            
        except Exception as e:
            print(f"❌ Error loading {file}: {str(e)}")
    
    if len(timeframe_data) >= 2:
        print(f"\n🎯 SUCCESS: {len(timeframe_data)} timeframes loaded for analysis")
        
        # Analyze key indicators across timeframes
        key_indicators = [
            'SQUEEZE_SQZ_OFF', 'SQUEEZE_PRO_SQZPRO_OFF',
            'RSI_14', 'MACD_12_26_9_MACD_12_26_9', 'ADX_14_ADX_14',
            'SUPERTREND_7_3.0_SUPERTd_7_3.0', 'BBANDS_5_2_BBB_5_2.0'
        ]
        
        confluence_results = {}
        
        for indicator in key_indicators:
            confluence_info = {
                'timeframes_with_signal': [],
                'signal_strength': {},
                'confluence_score': 0.0
            }
            
            total_timeframes = 0
            timeframes_with_signal = 0
            
            for timeframe, df in timeframe_data.items():
                if indicator in df.columns:
                    total_timeframes += 1
                    values = df[indicator].dropna()
                    
                    if len(values) > 0:
                        latest_value = values.iloc[-1]
                        
                        # Determine if there's a signal
                        has_signal = False
                        signal_strength = 0.0
                        
                        if 'SQUEEZE' in indicator and 'OFF' in indicator:
                            has_signal = latest_value == 1
                            signal_strength = 1.0 if has_signal else 0.0
                        elif 'RSI' in indicator:
                            if latest_value > 70 or latest_value < 30:
                                has_signal = True
                                signal_strength = abs(latest_value - 50) / 50
                        elif 'MACD' in indicator:
                            if abs(latest_value) > values.std():
                                has_signal = True
                                signal_strength = abs(latest_value) / values.std()
                        elif 'ADX' in indicator:
                            if latest_value > 25:
                                has_signal = True
                                signal_strength = min(latest_value / 50, 1.0)
                        
                        if has_signal:
                            timeframes_with_signal += 1
                            confluence_info['timeframes_with_signal'].append(timeframe)
                            confluence_info['signal_strength'][timeframe] = signal_strength
            
            # Calculate confluence score
            if total_timeframes > 0:
                confluence_info['confluence_score'] = timeframes_with_signal / total_timeframes
                confluence_results[indicator] = confluence_info
        
        # Print confluence analysis
        print(f"\n📊 CONFLUENCE ANALYSIS RESULTS:")
        print("=" * 50)
        
        for indicator, info in confluence_results.items():
            score = info['confluence_score']
            timeframes = ', '.join(info['timeframes_with_signal'])
            print(f"{indicator:<35} | Score: {score:.2f} | Timeframes: {timeframes}")
        
        # Calculate overall confluence
        overall_score = sum(info['confluence_score'] for info in confluence_results.values()) / len(confluence_results)
        
        print(f"\n🎯 OVERALL CONFLUENCE SCORE: {overall_score:.2f}")
        
        if overall_score > 0.6:
            print("🟢 HIGH CONFLUENCE - Strong multi-timeframe alignment")
        elif overall_score > 0.3:
            print("🟡 MODERATE CONFLUENCE - Some timeframe alignment")
        else:
            print("🔴 LOW CONFLUENCE - Limited timeframe alignment")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results = {
            'timeframes_analyzed': list(timeframe_data.keys()),
            'confluence_analysis': confluence_results,
            'overall_confluence_score': overall_score,
            'files_used': {tf: file for tf, file in zip(timeframe_data.keys(), file_list)},
            'timestamp': timestamp
        }
        
        import json
        results_filename = f"manual_multi_timeframe_analysis_{timestamp}.json"
        with open(results_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {results_filename}")
        
        return results
    else:
        print(f"❌ Only {len(timeframe_data)} timeframes loaded - need at least 2")
        return None

if __name__ == "__main__":
    print("🧪 Testing Multi-Timeframe Files")
    print("=" * 50)
    
    results = create_manual_multi_timeframe_analysis()
    
    if results:
        print("\n✅ Multi-timeframe analysis completed successfully!")
    else:
        print("\n❌ Multi-timeframe analysis failed.")
