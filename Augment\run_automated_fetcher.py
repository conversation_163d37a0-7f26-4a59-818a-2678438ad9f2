#!/usr/bin/env python3
"""
🔄 STANDALONE AUTOMATED TECHNICAL DATA FETCHER
Run this script to fetch technical data for multiple dates and intervals
"""

import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_ai_ml_threshold_optimizer import EnhancedAIMLThresholdOptimizer

def main():
    """Main entry point for standalone automated fetcher"""
    
    print("🔄 STANDALONE AUTOMATED TECHNICAL DATA FETCHER")
    print("=" * 60)
    print("📊 Fetch technical data for multiple dates and intervals")
    print("⏱️ Supports: 1, 3, 5, 15, 30, 60 minute intervals")
    print("📁 Organized output with proper file naming")
    print("🚫 Automatically excludes weekends")
    print("🔧 Customizable parameters")
    print("=" * 60)
    
    try:
        # Initialize the optimizer with automated fetcher
        optimizer = EnhancedAIMLThresholdOptimizer()
        
        # Run the automated fetcher
        print("\n🚀 Starting Automated Technical Data Fetcher...")
        output_folder = optimizer.run_automated_technical_fetcher()
        
        if output_folder:
            print(f"\n✅ Data fetching completed successfully!")
            print(f"📁 All files saved in: {output_folder}")
            print(f"📄 Check FETCH_SUMMARY.md for detailed results")
            
            # Ask if user wants to run optimization
            run_optimization = input("\n🤖 Run AI/ML threshold optimization on fetched data? (y/n): ").strip().lower()
            if run_optimization == 'y':
                # Find Excel files in the output folder
                excel_files = list(output_folder.glob("*.xlsx"))
                if excel_files:
                    print(f"\n📊 Found {len(excel_files)} Excel files")
                    
                    # Use the most recent file
                    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
                    print(f"📊 Using latest file: {latest_file.name}")
                    
                    # Run optimization
                    print("\n🤖 Starting AI/ML threshold optimization...")
                    results = optimizer.comprehensive_threshold_optimization(
                        ticker="FETCHED_DATA",
                        excel_file_path=str(latest_file)
                    )
                    
                    if results.get('optimization_successful', False):
                        print("✅ AI/ML optimization completed successfully!")
                        print(f"📊 Optimized {len(results.get('optimized_thresholds', {}))} indicators")
                    else:
                        print("⚠️ AI/ML optimization completed with issues")
                else:
                    print("⚠️ No Excel files found in output folder")
        else:
            print("❌ Data fetching was cancelled or failed")
            
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"❌ Error in automated fetcher: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🕒 Process finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
