"""
Demo script for Advanced Professional Signal Analyzer
Tests PGO-style reversal detection and time-based signal tracking
"""

from advanced_professional_signal_analyzer import AdvancedProfessionalSignalAnalyzer

def run_advanced_demo():
    """Run demo with advanced professional signal analysis"""
    
    # Create analyzer
    analyzer = AdvancedProfessionalSignalAnalyzer()
    
    # Predefined inputs for demo
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '01-07-2025',
        'timeframes': ['1', '5', '15', '30', '45'],
        'indicators': [
            'PGO_14',  # Your example indicator
            'CCI_14',
            'SMI_5_20_5_SMIo_5_20_5_100.0',
            'ACCBANDS_10_ACCBU_10',
            'BIAS_26',
            'CG_10',
            'QQE_14_QQE_14_5_4.236_RSIMA',
            'SMI_5_20_5_SMI_5_20_5_100.0'
        ],
        'validate_signals': True
    }
    
    print("🚀 DEMO: Advanced Professional Signal Analysis")
    print("=" * 80)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Timeframes: {', '.join(inputs['timeframes'])} minutes")
    print(f"🔍 Professional Indicators: {len(inputs['indicators'])}")
    
    print("\n🎯 ADVANCED FEATURES BEING TESTED:")
    print("   ⏰ Time-based signal generation and confirmation tracking")
    print("   🔄 PGO-style reversal detection (-2 → -3.5 → -2.7 = BUY)")
    print("   📊 Data-driven threshold optimization")
    print("   🎯 Professional timeframe-adjusted signal values")
    print("   📈 Hierarchical signal confirmations")
    print("   🎪 Entry point detection with risk/reward")
    
    # Load timeframe data
    timeframe_data = analyzer._load_timeframe_data(inputs)
    
    if len(timeframe_data) < 2:
        print("❌ Need at least 2 timeframes for analysis")
        return {}
    
    # Filter indicators
    available_indicators = analyzer._filter_available_indicators(inputs['indicators'], timeframe_data)
    
    if not available_indicators:
        print("❌ No configured indicators found")
        print(f"Available indicators: {list(analyzer.professional_thresholds.keys())}")
        return {}
    
    print(f"\n📊 Analyzing {len(available_indicators)} professional indicators:")
    for indicator in available_indicators:
        print(f"   ✅ {indicator}")
    
    # Perform advanced data analysis
    print(f"\n🔬 PERFORMING ADVANCED DATA ANALYSIS...")
    analysis_results = analyzer.perform_advanced_data_analysis(timeframe_data, available_indicators)
    
    # Detect professional signals with timing
    print(f"\n🎯 DETECTING PROFESSIONAL SIGNALS WITH TIMING...")
    signal_results = analyzer.detect_professional_signals_with_timing(timeframe_data, available_indicators)
    
    # Export to Excel
    excel_filename = analyzer.export_advanced_analysis_to_excel(analysis_results, signal_results, inputs)
    
    # Print summary
    analyzer._print_advanced_summary(analysis_results, signal_results)
    
    # Show specific PGO examples if found
    print(f"\n🔄 PGO REVERSAL PATTERN EXAMPLES:")
    print("=" * 60)
    
    pgo_signals = signal_results['signals_by_indicator'].get('PGO_14', [])
    pgo_reversals = [s for s in pgo_signals if s['signal_reason'] == 'PGO_REVERSAL_PATTERN']
    
    if pgo_reversals:
        for i, signal in enumerate(pgo_reversals[:5], 1):
            print(f"\n   {i}. {signal['signal_type']} Signal in {signal['timeframe']}")
            print(f"      Pattern: {signal.get('deepest_value', signal.get('highest_value', 0)):.2f} → {signal['reversal_value']:.2f}")
            print(f"      Reversal: {signal['reversal_percentage']:.1%}")
            print(f"      Generated: {signal['signal_generation_time']}")
            print(f"      Confirmed: {signal['confirmation_time']}")
            print(f"      Strength: {signal['signal_strength']:.3f}")
            print(f"      Assessment: {signal['professional_assessment']}")
    else:
        print("   ⚪ No PGO reversal patterns found in current data")
        print("   💡 Try with different date range or timeframes")
    
    # Show entry points
    print(f"\n🎪 ENTRY POINT ANALYSIS:")
    print("=" * 60)
    
    immediate_entries = 0
    pending_entries = 0
    
    for indicator, entries in signal_results['entry_points'].items():
        immediate_entries += len(entries['immediate_entries'])
        pending_entries += len(entries['pending_entries'])
        
        if entries['immediate_entries']:
            print(f"\n   🚨 {indicator} - IMMEDIATE ENTRIES:")
            for entry in entries['immediate_entries'][:3]:
                signal_ref = entry['signal_reference']
                print(f"      {signal_ref['signal_type']} at {entry['current_level']:.2f}")
                print(f"      Entry: {entry['entry_level']:.2f} | SL: {entry['stop_loss']:.2f} | Target: {entry['target']:.2f}")
                print(f"      R:R = {entry['risk_reward_ratio']:.1f}")
    
    print(f"\n   📊 Entry Summary:")
    print(f"      🚨 Immediate Entries: {immediate_entries}")
    print(f"      ⏳ Pending Entries: {pending_entries}")
    
    # Show threshold optimization results
    print(f"\n📈 THRESHOLD OPTIMIZATION HIGHLIGHTS:")
    print("=" * 60)
    
    for indicator, analysis in analysis_results['threshold_optimization'].items():
        best_accuracy = 0
        best_timeframe = ""
        
        for timeframe, optimal in analysis.get('optimal_thresholds', {}).items():
            accuracy = optimal.get('accuracy', 0)
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_timeframe = timeframe
        
        if best_accuracy > 0.6:  # Show only high accuracy optimizations
            print(f"   📊 {indicator}:")
            print(f"      Best Accuracy: {best_accuracy:.1%} in {best_timeframe}")
            
            reversal_acc = analysis.get('reversal_patterns', {}).get(best_timeframe, {}).get('reversal_accuracy', 0)
            breakout_acc = analysis.get('breakout_patterns', {}).get(best_timeframe, {}).get('breakout_accuracy', 0)
            
            if reversal_acc > 0:
                print(f"      Reversal Accuracy: {reversal_acc:.1%}")
            if breakout_acc > 0:
                print(f"      Breakout Accuracy: {breakout_acc:.1%}")
    
    print(f"\n💾 Advanced demo results saved to: {excel_filename}")
    
    return {
        'analysis_results': analysis_results,
        'signal_results': signal_results,
        'inputs': inputs
    }

if __name__ == "__main__":
    results = run_advanced_demo()
    
    if results:
        print("\n✅ Advanced Professional Signal Analysis Demo completed!")
        print("⏰ Time-based signal tracking demonstrated")
        print("🔄 PGO-style reversal detection tested")
        print("📊 Data-driven threshold optimization completed")
        print("🎯 Professional signal values applied")
        print("📈 Hierarchical confirmations analyzed")
        print("🎪 Entry points with risk/reward calculated")
        print("📄 Check the Excel file for comprehensive analysis!")
        
        # Show key insights
        signal_results = results['signal_results']
        total_signals = sum(len(signals) for signals in signal_results['signals_by_indicator'].values())
        
        if total_signals > 0:
            print(f"\n🎯 KEY INSIGHTS:")
            print(f"   📊 Total Professional Signals: {total_signals}")
            
            # Count reversal patterns
            reversal_count = 0
            for signals in signal_results['signals_by_indicator'].values():
                reversal_count += sum(1 for s in signals if 'REVERSAL' in s['signal_reason'])
            
            if reversal_count > 0:
                print(f"   🔄 Reversal Patterns Found: {reversal_count}")
            
            # Count strong confirmations
            strong_confirmations = 0
            for confirmations in signal_results['confirmation_tracking'].values():
                strong_confirmations += sum(1 for conf in confirmations['hierarchical_confirmations'] 
                                          if conf['confirmation_strength'] >= 0.7)
            
            if strong_confirmations > 0:
                print(f"   ✅ Strong Hierarchical Confirmations: {strong_confirmations}")
            
            print(f"   📈 This demonstrates professional-grade signal analysis!")
        
    else:
        print("\n❌ Demo failed.")
