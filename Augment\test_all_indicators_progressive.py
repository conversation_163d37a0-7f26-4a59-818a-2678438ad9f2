"""
Test All 150+ Indicators with Progressive Calculation

This script tests the progressive calculation fix with all available pandas-ta indicators
to ensure each time period shows different values based on the defaults from the help documentation.

Key Test:
- Verify that RSI, MACD, SMA, EMA, ATR, etc. show different values for different time periods
- Use the exact default parameters from pandas-ta documentation
- Test with multiple timeframes (1, 5, 15 minutes)
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from datetime import datetime, timedelta
import logging
from progressive_indicators_calculator import ProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_comprehensive_test_data(num_candles=200, interval_minutes=1):
    """
    Create comprehensive test data with realistic market movements
    """
    logger.info(f"📊 Creating {num_candles} candles of {interval_minutes}-minute data")
    
    # Create time series
    start_time = datetime(2025, 6, 24, 9, 15)
    times = [start_time + timedelta(minutes=i * interval_minutes) for i in range(num_candles)]
    
    # Create realistic price movements with multiple phases
    np.random.seed(42)  # For reproducible results
    
    base_price = 1200
    
    # Create complex price pattern: sideways -> uptrend -> downtrend -> recovery
    phase_length = num_candles // 4
    
    # Phase 1: Sideways movement
    phase1 = np.random.normal(0, 3, phase_length)
    
    # Phase 2: Strong uptrend
    phase2 = np.cumsum(np.random.normal(1.2, 2, phase_length))
    
    # Phase 3: Sharp downtrend
    phase3 = np.cumsum(np.random.normal(-0.8, 2.5, phase_length))
    
    # Phase 4: Recovery
    remaining = num_candles - (phase_length * 3)
    phase4 = np.cumsum(np.random.normal(0.4, 1.8, remaining))
    
    price_changes = np.concatenate([phase1, phase2, phase3, phase4])
    close_prices = base_price + np.cumsum(price_changes)
    
    # Ensure prices don't go negative
    close_prices = np.maximum(close_prices, base_price * 0.5)
    
    # Generate OHLC data with realistic relationships
    data = []
    for i, close in enumerate(close_prices):
        # Generate realistic OHLC
        open_price = close_prices[i-1] if i > 0 else base_price
        
        # High and low with realistic volatility
        volatility = abs(np.random.normal(0, 5))
        high = max(open_price, close) + volatility
        low = min(open_price, close) - volatility
        
        # Ensure OHLC relationships are valid
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # Volume with correlation to price movement and volatility
        price_move = abs(close - open_price) if i > 0 else 1
        volume_base = 2000 + price_move * 50 + volatility * 30
        volume = int(volume_base + np.random.normal(0, 500))
        volume = max(volume, 100)
        
        data.append({
            'time': times[i],
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"✅ Created comprehensive test data: {len(df)} candles from {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
    return df

def test_all_indicators_with_defaults():
    """
    Test all indicators using their default parameters from pandas-ta documentation
    """
    print("🧪 TESTING ALL 150+ INDICATORS WITH DEFAULT PARAMETERS")
    print("=" * 80)
    
    # Create comprehensive test data
    market_data = create_comprehensive_test_data(num_candles=150, interval_minutes=5)
    
    # Test time periods (spread across the data)
    test_times = ['10:30', '11:45', '13:00', '14:15', '15:00']
    
    print(f"\n📊 Testing with {len(test_times)} time periods: {test_times}")
    print("-" * 60)
    
    # Initialize progressive calculator
    calculator = ProgressiveIndicatorsCalculator()
    
    # Test different categories
    test_categories = ['momentum', 'overlap', 'volatility', 'volume']
    
    for category in test_categories:
        print(f"\n📂 TESTING {category.upper()} INDICATORS")
        print("-" * 50)
        
        # Calculate progressive indicators for this category
        progressive_results = calculator.calculate_progressive_indicators(
            market_data, test_times, interval_minutes=5, categories=[category]
        )
        
        if not progressive_results:
            print(f"❌ No results for {category} category")
            continue
        
        # Get all unique indicators from all time periods
        all_indicators = set()
        for time_str, result_data in progressive_results.items():
            all_indicators.update(result_data['indicators'].keys())
        
        print(f"📊 Found {len(all_indicators)} {category} indicators")
        
        # Test each indicator for progressive values
        indicators_with_variation = 0
        indicators_tested = 0
        
        for indicator_name in sorted(all_indicators):
            if indicator_name in ['CURRENT_PRICE', 'PRICE_CHANGE', 'PRICE_CHANGE_PCT', 'ERROR']:
                continue  # Skip price-based indicators
            
            # Collect values across time periods
            values = []
            for time_str in test_times:
                if time_str in progressive_results:
                    indicators = progressive_results[time_str]['indicators']
                    if indicator_name in indicators:
                        value = indicators[indicator_name]
                        if value is not None and not pd.isna(value):
                            values.append(float(value))
            
            if len(values) >= 3:  # Need at least 3 values to test variation
                indicators_tested += 1
                unique_values = len(set([round(v, 6) for v in values]))  # Round to avoid floating point issues
                
                if unique_values > 1:
                    indicators_with_variation += 1
                    print(f"   ✅ {indicator_name}: {unique_values} unique values - {[f'{v:.3f}' for v in values[:3]]}")
                else:
                    print(f"   ❌ {indicator_name}: Same value across periods - {values[0]:.3f}")
        
        # Summary for this category
        if indicators_tested > 0:
            success_rate = (indicators_with_variation / indicators_tested) * 100
            print(f"\n📈 {category.upper()} SUMMARY:")
            print(f"   Indicators tested: {indicators_tested}")
            print(f"   With variation: {indicators_with_variation}")
            print(f"   Success rate: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print(f"   ✅ EXCELLENT: Most indicators show progressive values!")
            elif success_rate >= 60:
                print(f"   ⚠️ GOOD: Many indicators show progressive values")
            else:
                print(f"   ❌ POOR: Few indicators show progressive values")
        else:
            print(f"   ⚠️ No indicators could be tested for {category}")

def test_specific_key_indicators():
    """
    Test specific key indicators that should definitely show variation
    """
    print(f"\n\n🎯 TESTING KEY INDICATORS WITH EXACT DEFAULTS")
    print("=" * 80)
    
    # Create test data
    market_data = create_comprehensive_test_data(num_candles=100, interval_minutes=1)
    
    # Test times
    test_times = ['10:00', '10:30', '11:00', '11:30', '12:00']
    
    # Test key indicators manually with exact defaults from documentation
    print(f"\n📊 Manual calculation with pandas-ta defaults:")
    print("-" * 60)
    
    for i, target_time in enumerate(test_times):
        # Find target time index
        target_indices = market_data[market_data['time'].dt.strftime('%H:%M') == target_time].index
        if len(target_indices) == 0:
            continue
        
        target_index = target_indices[0]
        
        # Get data up to this point (progressive calculation)
        data_subset = market_data.iloc[:target_index + 1].copy()
        
        if len(data_subset) < 20:  # Need minimum data
            continue
        
        # Calculate key indicators with exact defaults
        close = data_subset['Close']
        high = data_subset['High']
        low = data_subset['Low']
        volume = data_subset['Volume']
        
        indicators = {}
        
        # RSI with default length=14
        if len(close) >= 14:
            rsi_result = ta.rsi(close, length=14)
            if rsi_result is not None and len(rsi_result) > 0:
                indicators['RSI_14'] = rsi_result.iloc[-1]
        
        # SMA with default length=10 (common default)
        if len(close) >= 10:
            sma_result = ta.sma(close, length=10)
            if sma_result is not None and len(sma_result) > 0:
                indicators['SMA_10'] = sma_result.iloc[-1]
        
        # EMA with default length=10
        if len(close) >= 10:
            ema_result = ta.ema(close, length=10)
            if ema_result is not None and len(ema_result) > 0:
                indicators['EMA_10'] = ema_result.iloc[-1]
        
        # MACD with defaults: fast=12, slow=26, signal=9
        if len(close) >= 26:
            macd_result = ta.macd(close, fast=12, slow=26, signal=9)
            if macd_result is not None and len(macd_result) > 0:
                if 'MACD_12_26_9' in macd_result.columns:
                    indicators['MACD'] = macd_result['MACD_12_26_9'].iloc[-1]
        
        # ATR with default length=14
        if len(close) >= 14:
            atr_result = ta.atr(high, low, close, length=14)
            if atr_result is not None and len(atr_result) > 0:
                indicators['ATR_14'] = atr_result.iloc[-1]
        
        # Display results
        data_points = len(data_subset)
        price = close.iloc[-1]
        
        print(f"   🕐 {target_time} (using {data_points:3d} candles): Price={price:7.2f}")
        for ind_name, value in indicators.items():
            if value is not None and not pd.isna(value):
                print(f"      📊 {ind_name}: {value:8.3f}")
    
    print(f"\n💡 If values are different across time periods, the fix is working!")
    print(f"💡 If values are the same, there's still an issue with the calculation.")

def main():
    """Main test function"""
    try:
        print("🚀 COMPREHENSIVE TEST: ALL INDICATORS WITH PROGRESSIVE CALCULATION")
        print("=" * 80)
        print("This test verifies that the progressive calculation fix works correctly")
        print("with all 150+ pandas-ta indicators using their default parameters.")
        print()
        
        # Test all indicators by category
        test_all_indicators_with_defaults()
        
        # Test specific key indicators manually
        test_specific_key_indicators()
        
        print(f"\n\n🎯 FINAL SUMMARY:")
        print("=" * 80)
        print("✅ If most indicators show different values across time periods:")
        print("   The progressive calculation fix is working correctly!")
        print()
        print("❌ If indicators still show the same values:")
        print("   There may be an issue with the underlying pandas-ta calculation")
        print("   or the progressive implementation needs further refinement.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
