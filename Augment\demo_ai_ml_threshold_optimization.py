"""
Demo: Advanced AI/ML Threshold Optimization System
Comprehensive demonstration of the AI/ML threshold optimization capabilities

🎯 FEATURES DEMONSTRATED:
- True signal identification (≥0.5% profit within 15 minutes)
- Multi-algorithm ML optimization
- 14 timeframe combination testing
- Mathematical optimization functions
- Performance validation and reporting
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import json

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_ai_ml_threshold_optimizer import AdvancedAIMLThresholdOptimizer

def demo_comprehensive_optimization():
    """Demonstrate the complete AI/ML threshold optimization system"""
    
    print("🚀 DEMO: Advanced AI/ML Threshold Optimization System")
    print("================================================================================")
    print("🎯 OBJECTIVE: Learn from actual market data for maximum accuracy")
    print("📊 TRUE SIGNAL: 1min signal → ≥0.5% profit within 15 minutes")
    print("🤖 ML ALGORITHMS: Multi-algorithm ensemble optimization")
    print("🔍 14 TIMEFRAME COMBINATIONS: Complete confirmation learning")
    print("📈 MATHEMATICAL: Advanced optimization functions")
    print("================================================================================")
    
    # Initialize the optimizer
    optimizer = AdvancedAIMLThresholdOptimizer()
    
    # Define available data files
    data_files = {
        '1min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx',
        '3min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_3min_20250714_020457.xlsx',
        '5min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_5min_20250714_020552.xlsx',
        '15min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_15min_20250714_020711.xlsx',
        '30min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_30min_20250714_020756.xlsx',
        '60min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_60min_20250714_020823.xlsx'
    }
    
    # Check which files exist
    existing_files = {}
    print("\n🔍 CHECKING AVAILABLE DATA FILES:")
    print("-" * 50)
    
    for timeframe, filepath in data_files.items():
        if os.path.exists(filepath):
            existing_files[timeframe] = filepath
            print(f"✅ {timeframe:>6}: {os.path.basename(filepath)}")
        else:
            print(f"⚠️ {timeframe:>6}: File not found")
    
    if not existing_files:
        print("\n❌ No data files found. Creating synthetic demo...")
        return demo_with_synthetic_data(optimizer)
    
    print(f"\n📊 Found {len(existing_files)} timeframe files")
    
    # Run the comprehensive optimization
    try:
        print("\n🚀 STARTING COMPREHENSIVE OPTIMIZATION...")
        print("=" * 80)
        
        results = optimizer.comprehensive_threshold_optimization(
            existing_files, 
            ticker="NATURALGAS26AUG25_MCX"
        )
        
        # Display results
        display_optimization_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error during optimization: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def demo_with_synthetic_data(optimizer):
    """Demo with synthetic data when real files are not available"""
    
    print("\n🔬 RUNNING SYNTHETIC DATA DEMONSTRATION")
    print("=" * 80)
    
    # Create synthetic 1-minute data
    np.random.seed(42)
    dates = pd.date_range('2025-07-14 09:15', periods=1000, freq='1min')
    
    # Generate synthetic price data
    price_base = 100
    price_changes = np.random.normal(0, 0.5, 1000)
    prices = [price_base]
    for change in price_changes[:-1]:
        prices.append(prices[-1] * (1 + change/100))
    
    # Generate synthetic indicator data
    synthetic_data = pd.DataFrame({
        'datetime': dates,
        'close': prices,
        'PGO_14': np.random.normal(0, 2, 1000),
        'CCI_14': np.random.normal(0, 50, 1000),
        'SMI_5_20_5_SMIo_5_20_5_100.0': np.random.normal(0, 20, 1000),
        'BIAS_26': np.random.normal(0, 3, 1000),
        'CG_10': np.random.normal(0, 0.5, 1000)
    })
    
    print(f"📊 Generated synthetic data: {len(synthetic_data)} rows")
    print(f"🎯 Indicators: {', '.join(optimizer.target_indicators)}")
    
    # Run Phase 1: True Signal Analysis
    print("\n📊 PHASE 1: TRUE SIGNAL ANALYSIS (SYNTHETIC)")
    print("-" * 50)
    
    phase1_results = {
        'true_signals': [],
        'false_signals': [],
        'analysis_summary': {},
        'data_1min': synthetic_data
    }
    
    # Simulate signal detection
    for indicator in optimizer.target_indicators:
        if indicator in synthetic_data.columns:
            # Generate some synthetic signals
            indicator_values = synthetic_data[indicator]
            thresholds = optimizer.initial_thresholds.get(indicator, {}).get('1min', {})
            
            if thresholds:
                # Find oversold/overbought conditions
                oversold_mask = indicator_values < thresholds.get('detection_oversold', -999)
                overbought_mask = indicator_values > thresholds.get('detection_overbought', 999)
                
                # Create synthetic true signals
                oversold_indices = np.where(oversold_mask)[0][:5]  # Limit to 5 signals
                overbought_indices = np.where(overbought_mask)[0][:5]
                
                for idx in oversold_indices:
                    if idx < len(synthetic_data) - 15:  # Leave validation window
                        signal = {
                            'type': 'BUY',
                            'indicator': indicator,
                            'time_index': idx,
                            'signal_value': indicator_values.iloc[idx],
                            'detection_value': indicator_values.iloc[idx-1] if idx > 0 else indicator_values.iloc[idx],
                            'entry_price': synthetic_data['close'].iloc[idx],
                            'thresholds_used': thresholds,
                            'is_profitable': True,
                            'max_profit': np.random.uniform(0.5, 2.0),
                            'time_to_profit': np.random.randint(1, 15),
                            'market_context': {'volatility': 0.5, 'trend': 0.1}
                        }
                        phase1_results['true_signals'].append(signal)
                
                for idx in overbought_indices:
                    if idx < len(synthetic_data) - 15:
                        signal = {
                            'type': 'SELL',
                            'indicator': indicator,
                            'time_index': idx,
                            'signal_value': indicator_values.iloc[idx],
                            'detection_value': indicator_values.iloc[idx-1] if idx > 0 else indicator_values.iloc[idx],
                            'entry_price': synthetic_data['close'].iloc[idx],
                            'thresholds_used': thresholds,
                            'is_profitable': True,
                            'max_profit': np.random.uniform(0.5, 2.0),
                            'time_to_profit': np.random.randint(1, 15),
                            'market_context': {'volatility': 0.5, 'trend': -0.1}
                        }
                        phase1_results['true_signals'].append(signal)
    
    phase1_results['analysis_summary'] = {
        'total_true_signals': len(phase1_results['true_signals']),
        'total_false_signals': 0,
        'true_signal_rate': 100.0,
        'indicators_analyzed': len(optimizer.target_indicators)
    }
    
    print(f"✅ Synthetic signals generated: {len(phase1_results['true_signals'])}")
    
    # Run remaining phases with synthetic data
    print("\n🔍 PHASE 2: PATTERN RECOGNITION (SYNTHETIC)")
    phase2_results = optimizer.phase2_pattern_recognition_feature_engineering(phase1_results)
    
    print("\n🤖 PHASE 3: ML OPTIMIZATION (SYNTHETIC)")
    if phase2_results['features_extracted']:
        # Simulate optimization results
        phase3_results = {
            'optimization_successful': True,
            'best_model': 'random_forest',
            'optimized_thresholds': optimizer.initial_thresholds
        }
    else:
        phase3_results = {'optimization_successful': False}
    
    print("\n🔄 PHASE 4: MULTI-TIMEFRAME LEARNING (SYNTHETIC)")
    # Simulate multi-timeframe results
    phase4_results = {
        'learning_successful': True,
        'optimized_thresholds_with_htf': optimizer.initial_thresholds,
        'ranked_combinations': [
            {
                'timeframes': ['15min'],
                'score': 0.85,
                'performance': {'accuracy': 0.85, 'confirmed_signals': 8, 'total_signals': 10}
            }
        ]
    }
    
    print("\n✅ PHASE 5: VALIDATION (SYNTHETIC)")
    final_results = optimizer.phase5_validation_performance_analysis(phase4_results)
    
    # Display synthetic results
    display_optimization_results(final_results)
    
    return final_results

def display_optimization_results(results):
    """Display comprehensive optimization results"""
    
    print("\n🎉 OPTIMIZATION RESULTS SUMMARY")
    print("=" * 80)
    
    if not results or not results.get('validation_successful'):
        print("❌ Optimization was not successful")
        return
    
    # Display performance metrics
    metrics = results.get('final_metrics', {})
    print("\n📊 PERFORMANCE METRICS:")
    print("-" * 40)
    print(f"🎯 True Signal Capture Rate: {metrics.get('true_signal_capture_rate', 0):.1f}%")
    print(f"❌ False Signal Rate:        {metrics.get('false_signal_rate', 0):.1f}%")
    print(f"💰 Average Profit/Signal:    {metrics.get('average_profit', 0):.2f}%")
    print(f"📈 Sharpe Ratio:             {metrics.get('sharpe_ratio', 0):.2f}")
    print(f"📊 Total Signals:            {metrics.get('total_signals', 0)}")
    print(f"✅ Confirmed Signals:        {metrics.get('confirmed_signals', 0)}")
    
    # Display success criteria
    criteria = results.get('success_criteria', {})
    print("\n🎯 SUCCESS CRITERIA:")
    print("-" * 40)
    print(f"✅ True Signal Capture ≥95%: {'PASS' if criteria.get('true_signal_capture_rate_95') else 'FAIL'}")
    print(f"✅ False Signal Rate ≤30%:   {'PASS' if criteria.get('false_signal_rate_30') else 'FAIL'}")
    print(f"✅ Average Profit ≥0.5%:     {'PASS' if criteria.get('average_profit_05') else 'FAIL'}")
    print(f"✅ Sharpe Ratio ≥2.0:        {'PASS' if criteria.get('sharpe_ratio_2') else 'FAIL'}")
    print(f"🏆 ALL CRITERIA MET:         {'YES' if criteria.get('all_criteria_met') else 'NO'}")
    
    # Display best timeframe combinations
    best_combinations = results.get('best_combinations', [])
    if best_combinations:
        print("\n🏆 BEST TIMEFRAME COMBINATIONS:")
        print("-" * 40)
        for i, combo in enumerate(best_combinations[:3], 1):
            timeframes = ' + '.join(combo['timeframes'])
            score = combo['score']
            print(f"{i}. {timeframes:<20} (Score: {score:.3f})")
    
    # Display optimized thresholds sample
    optimized_thresholds = results.get('optimized_thresholds', {})
    if optimized_thresholds:
        print("\n🔧 OPTIMIZED THRESHOLDS (Sample):")
        print("-" * 40)
        for indicator in list(optimized_thresholds.keys())[:2]:  # Show first 2 indicators
            print(f"\n{indicator}:")
            thresholds_1min = optimized_thresholds[indicator].get('1min', {})
            for key, value in thresholds_1min.items():
                print(f"  {key}: {value:.2f}")
    
    print("\n" + "=" * 80)
    print("🎉 OPTIMIZATION COMPLETE!")
    print("📄 Detailed report saved to JSON file")

def main():
    """Main demo execution"""
    
    print(f"🕒 Demo started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run the comprehensive demo
    results = demo_comprehensive_optimization()
    
    if results:
        print("\n✅ Demo completed successfully!")
        print("📊 Review the results above for optimization insights")
    else:
        print("\n⚠️ Demo completed with issues")
        print("🔧 Check the error messages and adjust as needed")
    
    print(f"\n🕒 Demo finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
