"""
Check Actual Indicators in Excel

This script checks what indicators are actually in the latest Excel file.
"""

import pandas as pd
import os

def check_actual_indicators():
    """Check what indicators are actually in the Excel file"""
    
    # Find the most recent Excel file
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and 'technical_analysis' in f and not f.startswith('~$')]
    if not excel_files:
        print("❌ No Excel files found")
        return
    
    # Get the most recent file
    latest_file = max(excel_files, key=os.path.getmtime)
    print(f"📄 Checking Excel file: {latest_file}")
    
    try:
        # Read the Time_Series_Indicators sheet
        df = pd.read_excel(latest_file, sheet_name='Time_Series_Indicators')
        
        print(f"📊 Total rows: {len(df)}")
        print(f"📊 Total columns: {len(df.columns)}")
        
        # Get all indicators
        indicators = df['Indicator'].tolist()
        categories = df['Category'].tolist()
        
        print(f"\n📋 ALL INDICATORS FOUND:")
        print("=" * 60)
        
        # Group by category
        category_indicators = {}
        for indicator, category in zip(indicators, categories):
            if category not in category_indicators:
                category_indicators[category] = []
            category_indicators[category].append(indicator)
        
        # Display by category
        for category, indicator_list in sorted(category_indicators.items()):
            print(f"\n📂 {category.upper()} ({len(indicator_list)} indicators):")
            for indicator in sorted(indicator_list):
                print(f"   • {indicator}")
        
        # Check for common indicators
        print(f"\n🔍 CHECKING FOR COMMON INDICATORS:")
        print("=" * 60)
        
        common_indicators = [
            'RSI_14', 'RSI', 'rsi', 'RSI_14_RSI_14',
            'SMA_10', 'SMA', 'sma', 'SMA_10_SMA_10',
            'EMA_10', 'EMA', 'ema', 'EMA_10_EMA_10',
            'MACD_12_26_9', 'MACD', 'macd', 'MACD_12_26_9_MACD_12_26_9',
            'ATR_14', 'ATR', 'atr', 'ATR_14_ATR_14',
            'BBANDS', 'BB', 'bbands',
            'STOCH', 'stoch',
            'CCI', 'cci'
        ]
        
        found_indicators = []
        for common in common_indicators:
            matches = [ind for ind in indicators if common.lower() in ind.lower()]
            if matches:
                found_indicators.extend(matches)
                print(f"✅ Found {common}: {matches}")
            else:
                print(f"❌ Missing {common}")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total indicators in Excel: {len(indicators)}")
        print(f"   Common indicators found: {len(set(found_indicators))}")
        print(f"   Categories: {list(category_indicators.keys())}")
        
        # Show sample values for a few indicators
        print(f"\n📈 SAMPLE VALUES (first 5 time periods):")
        print("=" * 60)
        
        time_columns = [col for col in df.columns if col not in ['Indicator', 'Category']][:5]
        
        for i, (indicator, category) in enumerate(zip(indicators[:10], categories[:10])):
            values = []
            for time_col in time_columns:
                val = df.iloc[i][time_col]
                if pd.notna(val) and val != '':
                    try:
                        values.append(f"{float(val):.3f}")
                    except:
                        values.append(str(val))
                else:
                    values.append('NaN')
            
            print(f"   {indicator} ({category}): {' | '.join(values)}")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {str(e)}")

def main():
    """Main function"""
    print("🔍 CHECKING ACTUAL INDICATORS IN EXCEL")
    print("=" * 60)
    check_actual_indicators()

if __name__ == "__main__":
    main()
