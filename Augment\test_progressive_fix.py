"""
Test Script to Demonstrate the Progressive Indicators Fix

This script shows how the progressive calculation fixes the issue where
all technical indicators showed the same values across different time periods.

BEFORE: All indicators had identical values for all time periods
AFTER: Each time period shows different indicator values based on data available up to that point
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from datetime import datetime, timedelta
import logging
from progressive_indicators_calculator import ProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_realistic_market_data(num_candles=100, interval_minutes=5):
    """
    Create realistic market data with clear trends and patterns
    """
    logger.info(f"📊 Creating {num_candles} candles of {interval_minutes}-minute data")
    
    # Create time series
    start_time = datetime(2025, 6, 24, 9, 15)
    times = [start_time + timedelta(minutes=i * interval_minutes) for i in range(num_candles)]
    
    # Create realistic price movements with trends
    np.random.seed(42)  # For reproducible results
    
    base_price = 1200
    
    # Create different phases: sideways, uptrend, downtrend
    phase_length = num_candles // 3
    remaining = num_candles - (phase_length * 2)

    phase1 = np.random.normal(0, 2, phase_length)  # Sideways
    phase2 = np.cumsum(np.random.normal(0.5, 1.5, phase_length))  # Uptrend
    phase3 = np.cumsum(np.random.normal(-0.3, 1.2, remaining))  # Downtrend

    price_changes = np.concatenate([phase1, phase2, phase3])
    close_prices = base_price + np.cumsum(price_changes)
    
    # Generate OHLC data
    data = []
    for i, close in enumerate(close_prices):
        # Generate realistic OHLC
        open_price = close_prices[i-1] if i > 0 else base_price
        
        # High and low with realistic ranges
        volatility = abs(np.random.normal(0, 3))
        high = max(open_price, close) + volatility
        low = min(open_price, close) - volatility
        
        # Volume with some correlation to price movement
        price_move = abs(close - open_price) if i > 0 else 1
        volume = int(1000 + price_move * 100 + np.random.normal(0, 500))
        volume = max(volume, 100)
        
        data.append({
            'time': times[i],
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"✅ Created market data: {len(df)} candles from {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
    return df

def demonstrate_old_vs_new_calculation():
    """
    Demonstrate the difference between old (same values) and new (progressive) calculation
    """
    print("🔍 DEMONSTRATING THE FIX FOR SAME VALUES ISSUE")
    print("=" * 80)
    
    # Create sample data
    market_data = create_realistic_market_data(num_candles=75, interval_minutes=5)
    
    # Test time periods
    test_times = ['10:30', '11:15', '12:00', '13:30', '14:15']
    
    print(f"\n📊 Testing with {len(test_times)} time periods: {test_times}")
    print("-" * 60)
    
    # OLD METHOD (WRONG): Calculate indicators once on full dataset
    print(f"\n❌ OLD METHOD (WRONG - Same values for all times):")
    print("-" * 50)
    
    # Calculate indicators on full dataset (this is what was causing the issue)
    close_prices = market_data['Close']
    
    # Calculate some indicators on full dataset
    rsi_full = ta.rsi(close_prices, length=14)
    sma_full = ta.sma(close_prices, length=20)
    ema_full = ta.ema(close_prices, length=20)
    
    # Get final values (this is what was being used for all time periods)
    final_rsi = rsi_full.iloc[-1] if rsi_full is not None else None
    final_sma = sma_full.iloc[-1] if sma_full is not None else None
    final_ema = ema_full.iloc[-1] if ema_full is not None else None
    
    print(f"   📊 RSI (same for all times): {final_rsi:.2f}")
    print(f"   📈 SMA (same for all times): {final_sma:.2f}")
    print(f"   📉 EMA (same for all times): {final_ema:.2f}")
    print(f"   ❌ PROBLEM: All time periods would show these same values!")
    
    # NEW METHOD (CORRECT): Progressive calculation
    print(f"\n✅ NEW METHOD (CORRECT - Different values for each time):")
    print("-" * 50)
    
    calculator = ProgressiveIndicatorsCalculator()
    
    # Calculate progressively for each time period
    progressive_results = calculator.calculate_progressive_indicators(
        market_data, test_times, interval_minutes=5, categories=['momentum', 'overlap']
    )
    
    # Display results
    for time_str, result_data in progressive_results.items():
        indicators = result_data['indicators']
        data_points = result_data['data_points_used']
        price = result_data['price_data']['Close']
        
        rsi = indicators.get('RSI_14', 'N/A')
        sma = indicators.get('SMA_20', 'N/A')
        ema = indicators.get('EMA_20', 'N/A')

        # Format values properly
        rsi_str = f"{rsi:6.2f}" if isinstance(rsi, (int, float)) else str(rsi)
        sma_str = f"{sma:7.2f}" if isinstance(sma, (int, float)) else str(sma)
        ema_str = f"{ema:7.2f}" if isinstance(ema, (int, float)) else str(ema)

        print(f"   🕐 {time_str} (using {data_points:2d} candles): Price={price:7.2f}, RSI={rsi_str}, SMA={sma_str}, EMA={ema_str}")
    
    # Verify values are different
    rsi_values = [progressive_results[t]['indicators'].get('RSI_14') for t in test_times if 'RSI_14' in progressive_results[t]['indicators']]
    sma_values = [progressive_results[t]['indicators'].get('SMA_20') for t in test_times if 'SMA_20' in progressive_results[t]['indicators']]
    
    unique_rsi = len(set([v for v in rsi_values if v is not None]))
    unique_sma = len(set([v for v in sma_values if v is not None]))
    
    print(f"\n🔍 VERIFICATION:")
    print(f"   RSI unique values: {unique_rsi}/{len(rsi_values)}")
    print(f"   SMA unique values: {unique_sma}/{len(sma_values)}")
    
    if unique_rsi > 1 and unique_sma > 1:
        print(f"   ✅ SUCCESS: Values are now different across time periods!")
    else:
        print(f"   ❌ ISSUE: Values are still the same")
    
    return progressive_results

def demonstrate_timeframe_differences():
    """
    Demonstrate how different timeframes produce different indicator values
    """
    print(f"\n\n🎯 DEMONSTRATING TIMEFRAME DIFFERENCES")
    print("=" * 80)
    
    # Create base 1-minute data
    base_data = create_realistic_market_data(num_candles=300, interval_minutes=1)
    
    # Test different timeframes
    timeframes = [1, 5, 15]
    test_time = '12:30'
    
    calculator = ProgressiveIndicatorsCalculator()
    
    print(f"\n📊 Comparing indicators at {test_time} across different timeframes:")
    print("-" * 70)
    
    for interval in timeframes:
        if interval == 1:
            # Use original 1-minute data
            timeframe_data = base_data
        else:
            # Resample to target timeframe
            df_resampled = base_data.set_index('time')
            interval_str = f'{interval}T'
            
            resampled = df_resampled.resample(interval_str).agg({
                'Open': 'first',
                'High': 'max', 
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            }).dropna().reset_index()
            
            timeframe_data = resampled
        
        # Calculate indicators for this timeframe
        results = calculator.calculate_progressive_indicators(
            timeframe_data, [test_time], interval_minutes=interval, categories=['momentum', 'overlap', 'volatility']
        )
        
        if test_time in results:
            indicators = results[test_time]['indicators']
            data_points = results[test_time]['data_points_used']
            
            # Format values properly
            price = indicators.get('CURRENT_PRICE', 'N/A')
            rsi = indicators.get('RSI_14', 'N/A')
            sma = indicators.get('SMA_20', 'N/A')
            ema = indicators.get('EMA_20', 'N/A')
            atr = indicators.get('ATR_14', 'N/A')

            price_str = f"{price:8.2f}" if isinstance(price, (int, float)) else str(price)
            rsi_str = f"{rsi:8.2f}" if isinstance(rsi, (int, float)) else str(rsi)
            sma_str = f"{sma:8.2f}" if isinstance(sma, (int, float)) else str(sma)
            ema_str = f"{ema:8.2f}" if isinstance(ema, (int, float)) else str(ema)
            atr_str = f"{atr:8.2f}" if isinstance(atr, (int, float)) else str(atr)

            print(f"\n   📈 {interval:2d}-minute timeframe (using {data_points:3d} candles):")
            print(f"      💰 Price: {price_str}")
            print(f"      📊 RSI:   {rsi_str}")
            print(f"      📈 SMA:   {sma_str}")
            print(f"      📉 EMA:   {ema_str}")
            print(f"      🎯 ATR:   {atr_str}")
        else:
            print(f"   ❌ {interval}-minute: No data for {test_time}")
    
    print(f"\n💡 EXPLANATION:")
    print(f"   • Different timeframes use different amounts of historical data")
    print(f"   • 1-minute: Most sensitive, captures every small movement")
    print(f"   • 5-minute: Smooths out noise, more reliable signals")
    print(f"   • 15-minute: Even smoother, good for trend identification")

def main():
    """Main demonstration function"""
    try:
        print("🚀 TECHNICAL INDICATORS PROGRESSIVE CALCULATION FIX")
        print("=" * 80)
        print("This demonstrates the fix for the issue where all technical indicators")
        print("showed the same values across different time periods.")
        print()
        
        # Demonstrate the fix
        progressive_results = demonstrate_old_vs_new_calculation()
        
        # Demonstrate timeframe differences
        demonstrate_timeframe_differences()
        
        print(f"\n\n🎯 SUMMARY OF THE FIX:")
        print("=" * 80)
        print("✅ BEFORE: All indicators showed identical values for all time periods")
        print("✅ AFTER:  Each time period shows different values based on available data")
        print("✅ METHOD: Progressive calculation using only data up to each time point")
        print("✅ RESULT: Meaningful variations that enable AI/ML feature extraction")
        
        print(f"\n🚀 Your integrated technical analyzer now calculates indicators correctly!")
        print(f"   Each candle will have different indicator values based on its historical context.")
        print(f"   This enables proper leading indicator analysis and candle prediction.")
        
    except Exception as e:
        logger.error(f"❌ Error in demonstration: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
