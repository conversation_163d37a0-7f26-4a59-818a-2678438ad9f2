"""
Test Functions Fix

Test script to verify that the functions parameter now works correctly 
and doesn't default to strategy_all.
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime, timedelta
import time

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data(interval_minutes=1, start_time="09:15", end_time="15:30"):
    """Create test data with specific interval"""
    
    start_dt = datetime.strptime(f"2025-06-30 {start_time}", "%Y-%m-%d %H:%M")
    end_dt = datetime.strptime(f"2025-06-30 {end_time}", "%Y-%m-%d %H:%M")
    
    times = []
    current = start_dt
    while current <= end_dt:
        times.append(current)
        current += timedelta(minutes=interval_minutes)
    
    # Create realistic price data
    import random
    random.seed(42)
    
    base_price = 100.0
    data = []
    
    for i, time in enumerate(times):
        trend = 0.01 * i
        noise = random.uniform(-0.5, 0.5)
        
        open_price = base_price + trend + noise
        high_price = open_price + random.uniform(0.1, 0.8)
        low_price = open_price - random.uniform(0.1, 0.6)
        close_price = open_price + random.uniform(-0.3, 0.4)
        volume = random.randint(1000, 5000)
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
        
        base_price = close_price
    
    df = pd.DataFrame(data)
    df.set_index('time', inplace=True)
    df.reset_index(inplace=True)
    
    return df

def test_progressive_calculator_functions():
    """Test progressive calculator with specific functions"""
    
    logger.info("Testing progressive calculator with specific functions...")
    
    try:
        from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator
        
        calculator = CompleteProgressiveIndicatorsCalculator()
        
        # Create test data
        test_data = create_test_data(interval_minutes=1)
        logger.info(f"Created test data: {len(test_data)} candles")
        
        # Add time_str column
        test_data['time_str'] = test_data['time'].dt.strftime('%H:%M')
        
        # Test specific functions
        test_functions = ['pgo', 'cci', 'cg', 'accbands', 'qqe', 'smi', 'bias']
        time_periods = ["09:30", "10:00", "10:30", "11:00", "11:30"]
        
        logger.info(f"Testing functions: {', '.join(test_functions)}")
        logger.info(f"Time periods: {', '.join(time_periods)}")
        
        # Measure time for functions-only calculation
        start_time = time.time()
        
        result = calculator.calculate_all_indicators_progressive(
            df=test_data,
            time_periods=time_periods,
            interval_minutes=1,
            categories=None,
            method='extension',
            functions=test_functions
        )
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        if result:
            logger.info(f"✅ Functions calculation completed in {calculation_time:.2f} seconds")
            logger.info(f"📊 Results for {len(result)} time periods")
            
            # Check first result
            first_time = list(result.keys())[0]
            first_result = result[first_time]
            indicators = first_result.get('indicators', {})
            
            logger.info(f"Sample result for {first_time}:")
            logger.info(f"  Indicators calculated: {len(indicators)}")
            logger.info(f"  Sample indicators: {list(indicators.keys())[:5]}")
            
            # Check if we got the expected functions
            found_functions = []
            for indicator_name in indicators.keys():
                for func in test_functions:
                    if func.lower() in indicator_name.lower():
                        found_functions.append(func)
                        break
            
            logger.info(f"  Found indicators for functions: {', '.join(set(found_functions))}")
            
            # Performance check - should be much faster than strategy_all
            if calculation_time < 10:  # Should be under 10 seconds for specific functions
                logger.info(f"✅ Performance test PASSED: {calculation_time:.2f}s (expected < 10s)")
                return True
            else:
                logger.warning(f"⚠️ Performance test SLOW: {calculation_time:.2f}s (expected < 10s)")
                return False
        else:
            logger.error("❌ No results returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing progressive calculator: {e}")
        return False

def test_strategy_all_comparison():
    """Test comparison between functions and strategy_all"""
    
    logger.info("Testing comparison between functions and strategy_all...")
    
    try:
        from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator
        
        calculator = CompleteProgressiveIndicatorsCalculator()
        test_data = create_test_data(interval_minutes=1)
        test_data['time_str'] = test_data['time'].dt.strftime('%H:%M')
        
        time_periods = ["09:30", "10:00"]  # Just 2 periods for comparison
        test_functions = ['rsi', 'macd', 'bbands']
        
        # Test 1: Specific functions
        logger.info("Test 1: Specific functions (rsi, macd, bbands)")
        start_time = time.time()
        
        result_functions = calculator.calculate_all_indicators_progressive(
            df=test_data,
            time_periods=time_periods,
            interval_minutes=1,
            categories=None,
            method='extension',
            functions=test_functions
        )
        
        functions_time = time.time() - start_time
        functions_indicators = len(result_functions[time_periods[0]]['indicators']) if result_functions else 0
        
        logger.info(f"  Functions: {functions_time:.2f}s, {functions_indicators} indicators")
        
        # Test 2: Strategy all (for comparison - but we'll limit time)
        logger.info("Test 2: Strategy all (limited time)")
        start_time = time.time()
        
        try:
            # This might take too long, so we'll timeout after 30 seconds
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError("Strategy all took too long")
            
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(30)  # 30 second timeout
            
            result_all = calculator.calculate_all_indicators_progressive(
                df=test_data,
                time_periods=time_periods,
                interval_minutes=1,
                categories=None,
                method='strategy_all',
                functions=None
            )
            
            signal.alarm(0)  # Cancel timeout
            all_time = time.time() - start_time
            all_indicators = len(result_all[time_periods[0]]['indicators']) if result_all else 0
            
            logger.info(f"  Strategy all: {all_time:.2f}s, {all_indicators} indicators")
            
        except (TimeoutError, Exception) as e:
            logger.info(f"  Strategy all: TIMEOUT/ERROR after 30s (as expected)")
            all_time = 30
            all_indicators = "N/A"
        
        # Performance comparison
        if functions_time < 10:
            logger.info(f"✅ Functions method is FAST: {functions_time:.2f}s")
            if functions_indicators > 0:
                logger.info(f"✅ Functions method calculated {functions_indicators} indicators successfully")
                return True
            else:
                logger.warning(f"⚠️ Functions method calculated 0 indicators")
                return False
        else:
            logger.warning(f"⚠️ Functions method still slow: {functions_time:.2f}s")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in comparison test: {e}")
        return False

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Functions Fix Tests...")
    
    # Test 1: Progressive calculator with functions
    logger.info("\n" + "="*60)
    logger.info("TEST 1: PROGRESSIVE CALCULATOR WITH FUNCTIONS")
    logger.info("="*60)
    progressive_test = test_progressive_calculator_functions()
    
    # Test 2: Performance comparison
    logger.info("\n" + "="*60)
    logger.info("TEST 2: PERFORMANCE COMPARISON")
    logger.info("="*60)
    comparison_test = test_strategy_all_comparison()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    tests = {
        'Progressive Calculator Functions': progressive_test,
        'Performance Comparison': comparison_test
    }
    
    passed = sum(1 for success in tests.values() if success)
    total = len(tests)
    
    logger.info(f"Tests passed: {passed}/{total}")
    
    for test_name, success in tests.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    if passed == total:
        logger.info("🎉 Functions fix is working! No more strategy_all slowdown!")
        logger.info("You can now use: --functions pgo,cci,cg,accbands,qqe,smi,bias")
    else:
        logger.info("❌ Functions fix needs more work.")
    
    logger.info("Tests completed!")

if __name__ == "__main__":
    main()
