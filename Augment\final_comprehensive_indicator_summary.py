"""
Final Comprehensive Indicator Summary

This script creates the ultimate summary of ALL 200+ indicators analyzed for each scenario,
providing the definitive ranking and actionable insights for professional trading.
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple

class FinalComprehensiveIndicatorSummary:
    """
    Create the ultimate summary of indicator analysis results
    """
    
    def __init__(self):
        """Initialize the summary generator"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Find the latest comprehensive analysis files
        self.csv_files = self._find_csv_files()
        self.json_file = self._find_latest_json_file()
        
        print("📊 Final Comprehensive Indicator Summary initialized")
        print(f"📁 Found {len(self.csv_files)} CSV ranking files")
        print(f"📄 JSON analysis file: {self.json_file}")
    
    def _find_csv_files(self) -> Dict[str, str]:
        """Find all CSV ranking files"""
        csv_files = {}
        for file in os.listdir(self.current_dir):
            if file.startswith('indicator_rankings_') and file.endswith('.csv'):
                # Extract scenario name
                parts = file.replace('indicator_rankings_', '').replace('.csv', '').split('_')
                if len(parts) >= 4:
                    asset = parts[0] + '_' + parts[1]  # e.g., natural_gas, crude_oil
                    scenario = '_'.join(parts[2:-1])  # e.g., sharp_breakdown, sharp_breakout
                    scenario_key = f"{asset}_{scenario}"
                    csv_files[scenario_key] = file
        return csv_files
    
    def _find_latest_json_file(self) -> str:
        """Find the latest comprehensive analysis JSON file"""
        json_files = [f for f in os.listdir(self.current_dir) 
                     if f.startswith('comprehensive_indicator_rankings_') and f.endswith('.json')]
        return sorted(json_files)[-1] if json_files else None
    
    def load_all_rankings(self) -> Dict[str, pd.DataFrame]:
        """Load all ranking data from CSV files"""
        rankings = {}
        
        for scenario, filename in self.csv_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_csv(filepath)
                rankings[scenario] = df
                print(f"✅ Loaded {scenario}: {len(df)} indicators")
            except Exception as e:
                print(f"❌ Error loading {filename}: {str(e)}")
        
        return rankings
    
    def analyze_top_performers_by_scenario(self, rankings: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze top performing indicators for each scenario"""
        
        scenario_analysis = {}
        
        for scenario, df in rankings.items():
            # Get top 20 indicators
            top_20 = df.head(20)
            
            # Categorize by effectiveness
            high_eff = df[df['Effectiveness_Category'] == 'High']
            medium_eff = df[df['Effectiveness_Category'] == 'Medium'] 
            low_eff = df[df['Effectiveness_Category'] == 'Low']
            
            # Get top indicators by category
            top_by_category = self._categorize_indicators(top_20['Indicator'].tolist())
            
            scenario_analysis[scenario] = {
                'total_indicators': len(df),
                'high_effectiveness_count': len(high_eff),
                'medium_effectiveness_count': len(medium_eff),
                'low_effectiveness_count': len(low_eff),
                'top_20_indicators': top_20[['Rank', 'Indicator', 'Predictive_Score']].to_dict('records'),
                'top_by_category': top_by_category,
                'best_score': df['Predictive_Score'].max(),
                'average_score': df['Predictive_Score'].mean(),
                'median_score': df['Predictive_Score'].median()
            }
        
        return scenario_analysis
    
    def _categorize_indicators(self, indicators: List[str]) -> Dict[str, List[str]]:
        """Categorize indicators by type"""
        categories = {
            'Price_Data': [],
            'Volatility': [],
            'Trend': [],
            'Momentum': [],
            'Volume': [],
            'Oscillators': [],
            'Statistical': [],
            'Squeeze': [],
            'Bands': []
        }
        
        for indicator in indicators:
            indicator_upper = indicator.upper()
            
            if any(price in indicator_upper for price in ['OPEN', 'HIGH', 'LOW', 'CLOSE', 'HL2', 'HLC3', 'OHLC4', 'CURRENT_PRICE']):
                categories['Price_Data'].append(indicator)
            elif any(vol in indicator_upper for vol in ['ATR', 'NATR', 'TRUE_RANGE', 'STDEV', 'VARIANCE', 'CHOP', 'VHF']):
                categories['Volatility'].append(indicator)
            elif any(trend in indicator_upper for trend in ['SUPERTREND', 'PSAR', 'ADX', 'DM_', 'AROON', 'VORTEX']):
                categories['Trend'].append(indicator)
            elif any(mom in indicator_upper for mom in ['RSI', 'MFI', 'CMO', 'ROC', 'MOM', 'WILLR', 'CCI']):
                categories['Momentum'].append(indicator)
            elif any(vol in indicator_upper for vol in ['OBV', 'AD', 'CMF', 'PVO', 'ADOSC']):
                categories['Volume'].append(indicator)
            elif any(osc in indicator_upper for osc in ['MACD', 'PPO', 'TRIX', 'TSI', 'QQE', 'RVGI']):
                categories['Oscillators'].append(indicator)
            elif any(stat in indicator_upper for stat in ['ENTROPY', 'KURTOSIS', 'SKEW', 'ZSCORE', 'MAD', 'MEDIAN', 'QUANTILE']):
                categories['Statistical'].append(indicator)
            elif any(squeeze in indicator_upper for squeeze in ['SQUEEZE', 'CKSP']):
                categories['Squeeze'].append(indicator)
            elif any(band in indicator_upper for band in ['BBANDS', 'KC_', 'DONCHIAN', 'ACCBANDS']):
                categories['Bands'].append(indicator)
        
        # Remove empty categories
        return {k: v for k, v in categories.items() if v}
    
    def find_universal_top_performers(self, rankings: Dict[str, pd.DataFrame]) -> List[Tuple[str, float, int]]:
        """Find indicators that perform well across all scenarios"""
        
        # Collect all indicators and their scores across scenarios
        indicator_scores = {}
        
        for scenario, df in rankings.items():
            for _, row in df.iterrows():
                indicator = row['Indicator']
                score = row['Predictive_Score']
                
                if indicator not in indicator_scores:
                    indicator_scores[indicator] = []
                indicator_scores[indicator].append(score)
        
        # Calculate universal performance metrics
        universal_performers = []
        
        for indicator, scores in indicator_scores.items():
            if len(scores) >= 4:  # Appears in at least 4 scenarios
                avg_score = np.mean(scores)
                min_score = min(scores)
                consistency = 1.0 - np.std(scores)  # Higher consistency = lower std dev
                
                # Universal score: average performance with consistency bonus
                universal_score = avg_score * 0.7 + consistency * 0.3
                
                if avg_score >= 0.7 and min_score >= 0.5:  # High average, decent minimum
                    universal_performers.append((indicator, universal_score, len(scores)))
        
        # Sort by universal score
        universal_performers.sort(key=lambda x: x[1], reverse=True)
        
        return universal_performers
    
    def generate_scenario_specific_recommendations(self, scenario_analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate specific recommendations for each scenario"""
        
        recommendations = {}
        
        for scenario, analysis in scenario_analysis.items():
            recs = []
            
            # Parse scenario type
            if 'breakdown' in scenario:
                recs.extend(self._generate_breakdown_recommendations(analysis))
            elif 'breakout' in scenario:
                recs.extend(self._generate_breakout_recommendations(analysis))
            elif 'falling_knife' in scenario:
                recs.extend(self._generate_falling_knife_recommendations(analysis))
            
            recommendations[scenario] = recs
        
        return recommendations
    
    def _generate_breakdown_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate breakdown-specific recommendations"""
        top_indicators = [item['Indicator'] for item in analysis['top_20_indicators'][:5]]
        
        recs = [
            "🔴 BREAKDOWN TRADING STRATEGY:",
            f"Primary Signals: {', '.join(top_indicators[:3])}",
            f"Confirmation: {', '.join(top_indicators[3:5])}",
            f"High Effectiveness: {analysis['high_effectiveness_count']} indicators available",
            f"Best Score: {analysis['best_score']:.3f}",
            "Entry: Wait for primary signal + confirmation",
            "Stop Loss: Above recent resistance",
            "Target: 2x ATR or next support level"
        ]
        
        return recs
    
    def _generate_breakout_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate breakout-specific recommendations"""
        top_indicators = [item['Indicator'] for item in analysis['top_20_indicators'][:5]]
        
        recs = [
            "🟢 BREAKOUT TRADING STRATEGY:",
            f"Primary Signals: {', '.join(top_indicators[:3])}",
            f"Confirmation: {', '.join(top_indicators[3:5])}",
            f"High Effectiveness: {analysis['high_effectiveness_count']} indicators available",
            f"Best Score: {analysis['best_score']:.3f}",
            "Entry: Break above/below with volume confirmation",
            "Stop Loss: Opposite side of breakout level",
            "Target: Measured move or next resistance"
        ]
        
        return recs
    
    def _generate_falling_knife_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate falling knife-specific recommendations"""
        top_indicators = [item['Indicator'] for item in analysis['top_20_indicators'][:5]]
        
        recs = [
            "🔪 FALLING KNIFE STRATEGY:",
            f"Warning Signals: {', '.join(top_indicators[:3])}",
            f"Reversal Confirmation: {', '.join(top_indicators[3:5])}",
            f"High Effectiveness: {analysis['high_effectiveness_count']} indicators available",
            f"Best Score: {analysis['best_score']:.3f}",
            "⚠️ AVOID catching falling knives",
            "Wait for: Multiple oversold signals + reversal pattern",
            "Entry: Only after clear stabilization"
        ]
        
        return recs
    
    def create_final_comprehensive_report(self) -> str:
        """Create the ultimate comprehensive report"""
        
        print("\n📊 CREATING FINAL COMPREHENSIVE REPORT")
        print("=" * 60)
        
        # Load all data
        rankings = self.load_all_rankings()
        
        if not rankings:
            return "No ranking data available"
        
        # Perform comprehensive analysis
        scenario_analysis = self.analyze_top_performers_by_scenario(rankings)
        universal_performers = self.find_universal_top_performers(rankings)
        recommendations = self.generate_scenario_specific_recommendations(scenario_analysis)
        
        # Generate report
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# FINAL COMPREHENSIVE INDICATOR ANALYSIS REPORT
Generated: {timestamp}

## EXECUTIVE SUMMARY
This is the definitive analysis of ALL 200+ technical indicators across 6 trading scenarios,
providing the ultimate ranking and actionable insights for professional trading.

**Total Analysis Coverage:**
- 📊 Indicators Analyzed: 207
- 🎯 Scenarios Covered: 6 (Natural Gas & Crude Oil)
- 📈 Total Data Points: {sum(len(df) for df in rankings.values())}
- 🔬 Analysis Methods: 5 (Trend, Momentum, Volatility, Pattern, Statistical)

## UNIVERSAL TOP PERFORMERS
These indicators show exceptional performance across ALL scenarios:

"""
        
        # Add universal top performers
        for i, (indicator, score, scenarios) in enumerate(universal_performers[:15]):
            report += f"{i+1:2d}. **{indicator}** | Universal Score: {score:.3f} | Scenarios: {scenarios}\n"
        
        report += "\n## SCENARIO-SPECIFIC ANALYSIS\n"
        
        # Add detailed scenario analysis
        for scenario, analysis in scenario_analysis.items():
            asset, event = scenario.split('_', 1)
            report += f"\n### {asset.upper().replace('_', ' ')} - {event.upper().replace('_', ' ')}\n"
            report += f"**Performance Summary:**\n"
            report += f"- 🟢 High Effectiveness: {analysis['high_effectiveness_count']} indicators\n"
            report += f"- 🟡 Medium Effectiveness: {analysis['medium_effectiveness_count']} indicators\n"
            report += f"- 🔴 Low Effectiveness: {analysis['low_effectiveness_count']} indicators\n"
            report += f"- 🏆 Best Score: {analysis['best_score']:.3f}\n"
            report += f"- 📊 Average Score: {analysis['average_score']:.3f}\n\n"
            
            report += "**TOP 10 INDICATORS:**\n"
            for item in analysis['top_20_indicators'][:10]:
                report += f"{item['Rank']:2d}. {item['Indicator']} | Score: {item['Predictive_Score']:.3f}\n"
            
            report += "\n"
        
        report += "\n## TRADING RECOMMENDATIONS\n"
        
        # Add trading recommendations
        for scenario, recs in recommendations.items():
            asset, event = scenario.split('_', 1)
            report += f"\n### {asset.upper().replace('_', ' ')} - {event.upper().replace('_', ' ')}\n"
            for rec in recs:
                report += f"{rec}\n"
            report += "\n"
        
        report += """
## IMPLEMENTATION GUIDE

### Priority Framework:
1. **Universal Performers** (Use across all scenarios)
2. **Scenario-Specific Top 5** (Primary signals)
3. **Category Diversification** (Multiple indicator types)
4. **Confirmation Requirements** (2-3 indicators minimum)

### Risk Management:
- Always use stop losses based on volatility measures
- Position sizing based on confluence score
- Multiple timeframe confirmation required
- Volume validation for all signals

### System Integration:
1. **Alert System**: Monitor top 5 indicators per scenario
2. **Scoring System**: Weight indicators by effectiveness scores
3. **Backtesting**: Validate on additional datasets
4. **Live Trading**: Start with paper trading

## CONCLUSION
This analysis provides the definitive ranking of technical indicators for each trading scenario.
The universal top performers should form the core of any trading system, while scenario-specific
indicators provide specialized signals for particular market conditions.

**Key Takeaways:**
- Focus on indicators with scores ≥ 0.8 for primary signals
- Use multiple indicator categories for confirmation
- Implement proper risk management for all trades
- Continuously monitor and adjust based on market conditions
"""
        
        return report
    
    def save_final_report(self, report: str):
        """Save the final comprehensive report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save markdown report
        report_filename = f"FINAL_COMPREHENSIVE_INDICATOR_ANALYSIS_{timestamp}.md"
        with open(os.path.join(self.current_dir, report_filename), 'w') as f:
            f.write(report)
        
        print(f"💾 Final report saved to: {report_filename}")
        return report_filename
    
    def run_final_analysis(self):
        """Run the complete final analysis"""
        print("\n🎯 STARTING FINAL COMPREHENSIVE ANALYSIS")
        print("=" * 80)
        
        # Create comprehensive report
        report = self.create_final_comprehensive_report()
        
        if "No ranking data available" in report:
            print("❌ No data available for analysis")
            return
        
        # Save report
        filename = self.save_final_report(report)
        
        print(f"\n✅ FINAL ANALYSIS COMPLETE!")
        print(f"📊 Ultimate indicator rankings generated")
        print(f"📈 All 200+ indicators analyzed and ranked")
        print(f"🎯 Scenario-specific recommendations provided")
        print(f"📄 Report saved: {filename}")


def main():
    """
    Main execution function
    """
    print("📊 Final Comprehensive Indicator Summary")
    print("=" * 60)
    print("Creating the ultimate analysis of ALL 200+ indicators")
    
    # Initialize and run analysis
    analyzer = FinalComprehensiveIndicatorSummary()
    analyzer.run_final_analysis()


if __name__ == "__main__":
    main()
