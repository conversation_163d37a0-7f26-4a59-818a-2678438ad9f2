"""
Test Technical Analysis Fix

Quick test to verify the technical analysis integration works correctly.
"""

import sys
import os
import logging

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_technical_analysis_integration():
    """Test the technical analysis integration"""
    
    try:
        logger.info("Testing technical analysis integration...")
        
        # Import the fixed function
        from active_stocks_finder import run_technical_analysis_on_good_stocks
        
        # Create a mock good stock result
        mock_good_stocks = [{
            'ticker': 'ACC',
            'token_info': {
                'token': '1465',
                'tsym': 'ACCURACY-EQ',
                'symname': 'ACCURACY',
                'cname': 'ACCURACY SHIPPING LIMITED',
                'exchange': 'NSE'
            },
            'movement_score': {'movement_score': 40, 'grade': 'B'}
        }]
        
        # Test the function
        logger.info("Running technical analysis test...")
        run_technical_analysis_on_good_stocks(mock_good_stocks, "30-06-2025")
        
        logger.info("✅ Technical analysis integration test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Technical analysis integration test failed: {str(e)}")
        return False
    
    return True

def test_analyzer_methods():
    """Test the IntegratedTechnicalAnalyzer methods"""
    
    try:
        logger.info("Testing IntegratedTechnicalAnalyzer methods...")
        
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Check available methods
        methods = [method for method in dir(analyzer) if not method.startswith('_')]
        logger.info(f"Available methods: {methods}")
        
        # Check for the correct method
        if hasattr(analyzer, 'analyze_with_market_data'):
            logger.info("✅ analyze_with_market_data method found")
        else:
            logger.error("❌ analyze_with_market_data method not found")
            return False
        
        if hasattr(analyzer, 'export_to_excel'):
            logger.info("✅ export_to_excel method found")
        else:
            logger.error("❌ export_to_excel method not found")
            return False
        
        logger.info("✅ IntegratedTechnicalAnalyzer methods test passed!")
        
    except Exception as e:
        logger.error(f"❌ IntegratedTechnicalAnalyzer methods test failed: {str(e)}")
        return False
    
    return True

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Technical Analysis Fix Tests...")
    
    # Test 1: Check analyzer methods
    logger.info("\n" + "="*60)
    logger.info("TEST 1: ANALYZER METHODS")
    logger.info("="*60)
    test1_passed = test_analyzer_methods()
    
    # Test 2: Test integration
    logger.info("\n" + "="*60)
    logger.info("TEST 2: INTEGRATION TEST")
    logger.info("="*60)
    test2_passed = test_technical_analysis_integration()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    if test1_passed and test2_passed:
        logger.info("🎉 All tests passed! Technical analysis integration is fixed.")
    else:
        logger.info("❌ Some tests failed. Check the logs above.")
    
    logger.info("Tests completed!")

if __name__ == "__main__":
    main()
