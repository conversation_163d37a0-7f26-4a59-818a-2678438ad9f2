"""
Multi-Timeframe Predictive Analyzer

This script generates technical analysis data for multiple timeframes and analyzes
predictive signatures across different candle intervals (1, 3, 5, 10, 15, 30, 60, 120, 240 minutes).

Usage Examples:
# 5-minute analysis
conda activate Shoonya1; python integrated_technical_analyzer.py --mode analysis --analysis-type signals --ticker NATURALGAS26AUG25 --exchange MCX --date 01-07-2025 --method strategy_all --interval 5 --start-time "10:00" --end-time "16:32"

# 15-minute analysis  
conda activate Shoonya1; python integrated_technical_analyzer.py --mode analysis --analysis-type signals --ticker NATURALGAS26AUG25 --exchange MCX --date 01-07-2025 --method strategy_all --interval 15 --start-time "10:00" --end-time "16:32"

Key Features:
- Multi-timeframe analysis (1, 3, 5, 10, 15, 30, 60, 120, 240 minutes)
- Automated data generation for all timeframes
- Predictive signature analysis for each timeframe
- Cross-timeframe confluence detection
- Institutional-level signal filtering
"""

import subprocess
import os
import sys
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class MultiTimeframePredictiveAnalyzer:
    """
    Analyze predictive signatures across multiple timeframes
    """
    
    def __init__(self):
        """Initialize the multi-timeframe analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.available_intervals = ["1", "3", "5", "10", "15", "30", "60", "120", "240"]
        self.conda_env = "Shoonya1"
        
        # Default parameters
        self.default_params = {
            'ticker': 'NATURALGAS26AUG25',
            'exchange': 'MCX',
            'date': '01-07-2025',
            'method': 'strategy_all',
            'start_time': '10:00',
            'end_time': '16:32'
        }
        
        print("🚀 Multi-Timeframe Predictive Analyzer initialized")
        print(f"⏰ Available intervals: {', '.join(self.available_intervals)} minutes")
    
    def generate_multi_timeframe_data(self, 
                                    ticker: str = None,
                                    exchange: str = None,
                                    date: str = None,
                                    intervals: List[str] = None,
                                    start_time: str = None,
                                    end_time: str = None) -> Dict[str, str]:
        """
        Generate technical analysis data for multiple timeframes
        """
        # Use defaults if not provided
        ticker = ticker or self.default_params['ticker']
        exchange = exchange or self.default_params['exchange']
        date = date or self.default_params['date']
        start_time = start_time or self.default_params['start_time']
        end_time = end_time or self.default_params['end_time']
        intervals = intervals or ["1", "5", "15", "30", "60"]
        
        print(f"\n🔄 GENERATING MULTI-TIMEFRAME DATA")
        print("=" * 60)
        print(f"📊 Ticker: {ticker}")
        print(f"🏢 Exchange: {exchange}")
        print(f"📅 Date: {date}")
        print(f"⏰ Intervals: {', '.join(intervals)} minutes")
        print(f"🕐 Time Range: {start_time} to {end_time}")
        
        generated_files = {}
        
        for interval in intervals:
            print(f"\n📈 Generating {interval}-minute data...")
            
            # Construct command
            cmd = [
                "conda", "activate", self.conda_env, "&&",
                "python", "integrated_technical_analyzer.py",
                "--mode", "analysis",
                "--analysis-type", "signals",
                "--ticker", ticker,
                "--exchange", exchange,
                "--date", date,
                "--method", "strategy_all",
                "--interval", interval,
                "--start-time", start_time,
                "--end-time", end_time
            ]
            
            # Execute command
            try:
                result = subprocess.run(
                    " ".join(cmd),
                    shell=True,
                    capture_output=True,
                    text=True,
                    cwd=self.current_dir
                )

                if result.returncode == 0:
                    # Add small delay to ensure file is written
                    import time
                    time.sleep(2)

                    # Find generated file
                    generated_file = self._find_generated_file(ticker, exchange, date, interval)
                    if generated_file:
                        # Rename file to include interval
                        new_filename = self._create_interval_filename(generated_file, interval)
                        old_path = os.path.join(self.current_dir, generated_file)
                        new_path = os.path.join(self.current_dir, new_filename)

                        try:
                            os.rename(old_path, new_path)
                            generated_files[f"{interval}min"] = new_filename
                            print(f"✅ {interval}-minute data generated: {new_filename}")
                        except Exception as rename_error:
                            print(f"⚠️ Could not rename file: {str(rename_error)}")
                            generated_files[f"{interval}min"] = generated_file
                            print(f"✅ {interval}-minute data generated: {generated_file}")
                    else:
                        print(f"⚠️ {interval}-minute data generated but file not found")
                else:
                    print(f"❌ Error generating {interval}-minute data: {result.stderr}")

            except Exception as e:
                print(f"❌ Exception generating {interval}-minute data: {str(e)}")
        
        print(f"\n✅ Multi-timeframe data generation completed")
        print(f"📁 Generated {len(generated_files)} files")
        
        return generated_files
    
    def _find_generated_file(self, ticker: str, exchange: str, date: str, interval: str) -> Optional[str]:
        """Find the generated technical analysis file"""
        # Look for files matching the pattern
        date_str = datetime.strptime(date, '%d-%m-%Y').strftime('%Y%m%d')

        # Get all matching files and find the most recent one
        matching_files = []
        for file in os.listdir(self.current_dir):
            if (file.startswith(f'technical_analysis_{ticker}_{exchange}') and
                date_str in file and
                file.endswith('.xlsx')):
                # Get file modification time
                filepath = os.path.join(self.current_dir, file)
                mtime = os.path.getmtime(filepath)
                matching_files.append((file, mtime))

        if matching_files:
            # Return the most recently created file
            matching_files.sort(key=lambda x: x[1], reverse=True)
            return matching_files[0][0]

        return None

    def _create_interval_filename(self, original_filename: str, interval: str) -> str:
        """Create a new filename that includes the interval"""
        # Split filename and extension
        name, ext = os.path.splitext(original_filename)

        # Replace any existing interval patterns and add the new one
        parts = name.split('_')

        # Remove any existing interval patterns (like "1min", "5min", etc.)
        cleaned_parts = []
        for part in parts:
            if not (part.endswith('min') and part[:-3].isdigit()):
                cleaned_parts.append(part)

        # Insert interval before the timestamp (last part)
        if len(cleaned_parts) >= 4:
            cleaned_parts.insert(-1, f"{interval}min")
        else:
            cleaned_parts.append(f"{interval}min")

        new_name = '_'.join(cleaned_parts)
        return f"{new_name}{ext}"
    
    def find_existing_usable_files(self) -> Dict[str, str]:
        """Find existing usable files for multi-timeframe analysis"""
        usable_files = {}

        print(f"\n🔍 SEARCHING FOR EXISTING USABLE FILES")
        print("=" * 60)

        # Find all Natural Gas files with Time_Series_Indicators
        for file in os.listdir(self.current_dir):
            if ('NATURALGAS26AUG25' in file and
                file.endswith('.xlsx') and
                not file.startswith('~$')):

                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)

                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')

                        if df.shape[1] > 50:  # Should have many indicators
                            usable_files[file] = {
                                'shape': df.shape,
                                'indicators': df.shape[1]
                            }
                            print(f"✅ Found usable file: {file} ({df.shape[1]} indicators)")

                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")

        # Assign timeframes to files
        timeframe_files = {}
        file_list = list(usable_files.keys())
        timeframe_names = ['1min', '5min', '15min', '30min']

        for i, file in enumerate(file_list[:4]):
            timeframe = timeframe_names[i] if i < len(timeframe_names) else f'tf{i+1}'
            timeframe_files[timeframe] = file
            print(f"📊 Assigned {timeframe}: {file}")

        return timeframe_files

    def analyze_cross_timeframe_confluence(self, generated_files: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze confluence across multiple timeframes
        """
        print(f"\n🔍 ANALYZING CROSS-TIMEFRAME CONFLUENCE")
        print("=" * 60)
        
        confluence_analysis = {
            'timeframes_analyzed': list(generated_files.keys()),
            'confluence_signals': {},
            'divergence_signals': {},
            'strength_analysis': {}
        }
        
        # Load data from each timeframe
        timeframe_data = {}
        for timeframe, filename in generated_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)

                # Check if file exists, if not try to find it with different patterns
                if not os.path.exists(filepath):
                    print(f"⚠️ File not found: {filename}")
                    # Try to find a similar file
                    interval = timeframe.replace('min', '')
                    for file in os.listdir(self.current_dir):
                        if (f'{interval}min' in file and
                            'technical_analysis_NATURALGAS26AUG25_MCX' in file and
                            file.endswith('.xlsx')):
                            filepath = os.path.join(self.current_dir, file)
                            filename = file
                            print(f"🔍 Found alternative file: {filename}")
                            break

                if os.path.exists(filepath):
                    # First, check what sheets are available
                    excel_file = pd.ExcelFile(filepath)
                    available_sheets = excel_file.sheet_names
                    print(f"📋 Available sheets in {filename}: {available_sheets}")

                    # Try different sheet names
                    sheet_name = None
                    possible_names = ['Time_Series_Indicators', 'Time Series Indicators', 'Indicators', 'Sheet1']

                    for name in possible_names:
                        if name in available_sheets:
                            sheet_name = name
                            break

                    if sheet_name is None:
                        # Use the first sheet if no match found
                        sheet_name = available_sheets[0]
                        print(f"⚠️ Using first available sheet: {sheet_name}")

                    df = pd.read_excel(filepath, sheet_name=sheet_name)

                    # Check if data needs transposing
                    if len(df.columns) > len(df):
                        # More columns than rows, likely needs transposing
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        # Check if first column contains time data
                        first_col = df.iloc[:, 0]
                        if any(':' in str(val) for val in first_col.head(10)):
                            # First column has time data, transpose
                            df_transposed = df.set_index(df.columns[0]).T
                        else:
                            # Use as is
                            df_transposed = df

                    # Clean and convert to numeric
                    for col in df_transposed.columns:
                        df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')

                    timeframe_data[timeframe] = df_transposed
                    print(f"✅ Loaded {timeframe} data: {df_transposed.shape}")
                else:
                    print(f"❌ File still not found: {filename}")
            except Exception as e:
                print(f"❌ Error loading {timeframe} data: {str(e)}")
                import traceback
                traceback.print_exc()
        
        if len(timeframe_data) < 2:
            print("❌ Need at least 2 timeframes for confluence analysis")
            return confluence_analysis
        
        # Analyze key indicators across timeframes
        key_indicators = [
            'SQUEEZE_SQZ_OFF', 'SQUEEZE_PRO_SQZPRO_OFF',
            'RSI_14', 'MACD_12_26_9_MACD_12_26_9', 'ADX_14_ADX_14',
            'SUPERTREND_7_3.0_SUPERTd_7_3.0', 'BBANDS_5_2_BBB_5_2.0'
        ]
        
        for indicator in key_indicators:
            confluence_analysis['confluence_signals'][indicator] = self._analyze_indicator_confluence(
                timeframe_data, indicator
            )
        
        # Calculate overall confluence strength
        confluence_analysis['overall_strength'] = self._calculate_overall_confluence_strength(
            confluence_analysis['confluence_signals']
        )
        
        return confluence_analysis
    
    def _analyze_indicator_confluence(self, timeframe_data: Dict[str, pd.DataFrame], indicator: str) -> Dict[str, Any]:
        """Analyze confluence for a specific indicator across timeframes"""
        confluence_info = {
            'timeframes_with_signal': [],
            'signal_strength': {},
            'confluence_score': 0.0
        }
        
        total_timeframes = 0
        timeframes_with_signal = 0
        
        for timeframe, df in timeframe_data.items():
            if indicator in df.columns:
                total_timeframes += 1
                values = df[indicator].dropna()
                
                if len(values) > 0:
                    latest_value = values.iloc[-1]
                    
                    # Determine if there's a signal
                    has_signal = False
                    signal_strength = 0.0
                    
                    if 'SQUEEZE' in indicator and 'OFF' in indicator:
                        has_signal = latest_value == 1
                        signal_strength = 1.0 if has_signal else 0.0
                    elif 'RSI' in indicator:
                        if latest_value > 70 or latest_value < 30:
                            has_signal = True
                            signal_strength = abs(latest_value - 50) / 50
                    elif 'MACD' in indicator:
                        if abs(latest_value) > values.std():
                            has_signal = True
                            signal_strength = abs(latest_value) / values.std()
                    elif 'ADX' in indicator:
                        if latest_value > 25:
                            has_signal = True
                            signal_strength = min(latest_value / 50, 1.0)
                    
                    if has_signal:
                        timeframes_with_signal += 1
                        confluence_info['timeframes_with_signal'].append(timeframe)
                        confluence_info['signal_strength'][timeframe] = signal_strength
        
        # Calculate confluence score
        if total_timeframes > 0:
            confluence_info['confluence_score'] = timeframes_with_signal / total_timeframes
        
        return confluence_info
    
    def _calculate_overall_confluence_strength(self, confluence_signals: Dict[str, Any]) -> float:
        """Calculate overall confluence strength across all indicators"""
        if not confluence_signals:
            return 0.0
        
        total_score = 0.0
        indicator_count = 0
        
        for indicator, confluence_info in confluence_signals.items():
            if isinstance(confluence_info, dict) and 'confluence_score' in confluence_info:
                total_score += confluence_info['confluence_score']
                indicator_count += 1
        
        return total_score / indicator_count if indicator_count > 0 else 0.0
    
    def generate_timeframe_recommendations(self, confluence_analysis: Dict[str, Any]) -> List[str]:
        """Generate trading recommendations based on timeframe analysis"""
        recommendations = []
        
        overall_strength = confluence_analysis.get('overall_strength', 0.0)
        
        recommendations.append("🎯 MULTI-TIMEFRAME TRADING RECOMMENDATIONS")
        recommendations.append("=" * 50)
        
        if overall_strength > 0.7:
            recommendations.append("🟢 HIGH CONFLUENCE - Strong multi-timeframe alignment")
            recommendations.append("   • High probability setup detected")
            recommendations.append("   • Consider larger position size")
            recommendations.append("   • Multiple timeframes confirm signal")
        elif overall_strength > 0.5:
            recommendations.append("🟡 MODERATE CONFLUENCE - Some timeframe alignment")
            recommendations.append("   • Moderate probability setup")
            recommendations.append("   • Use standard position size")
            recommendations.append("   • Wait for additional confirmation")
        else:
            recommendations.append("🔴 LOW CONFLUENCE - Limited timeframe alignment")
            recommendations.append("   • Low probability setup")
            recommendations.append("   • Avoid trading or use small size")
            recommendations.append("   • Wait for better setup")
        
        # Add specific timeframe guidance
        recommendations.append("\n📊 TIMEFRAME-SPECIFIC GUIDANCE:")
        
        timeframes = confluence_analysis.get('timeframes_analyzed', [])
        if '1min' in timeframes:
            recommendations.append("   • 1-minute: Use for precise entry/exit timing")
        if '5min' in timeframes:
            recommendations.append("   • 5-minute: Primary signal confirmation")
        if '15min' in timeframes:
            recommendations.append("   • 15-minute: Trend direction validation")
        if '60min' in timeframes:
            recommendations.append("   • 60-minute: Major level identification")
        
        return recommendations
    
    def run_complete_multi_timeframe_analysis(self, 
                                            ticker: str = None,
                                            exchange: str = None,
                                            date: str = None,
                                            intervals: List[str] = None) -> Dict[str, Any]:
        """
        Run complete multi-timeframe analysis
        """
        print("\n🚀 STARTING COMPLETE MULTI-TIMEFRAME ANALYSIS")
        print("=" * 80)
        
        # Generate data for multiple timeframes
        generated_files = self.generate_multi_timeframe_data(
            ticker=ticker,
            exchange=exchange,
            date=date,
            intervals=intervals
        )

        # If generation failed or insufficient files, try to use existing files
        if len(generated_files) < 2:
            print("⚠️ Insufficient generated files, searching for existing usable files...")
            existing_files = self.find_existing_usable_files()

            if len(existing_files) >= 2:
                print(f"✅ Found {len(existing_files)} existing usable files")
                generated_files = existing_files
            else:
                print("❌ No sufficient data files available")
                return {}
        
        # Analyze cross-timeframe confluence
        confluence_analysis = self.analyze_cross_timeframe_confluence(generated_files)
        
        # Generate recommendations
        recommendations = self.generate_timeframe_recommendations(confluence_analysis)
        
        # Print recommendations
        print("\n")
        for rec in recommendations:
            print(rec)
        
        # Save results
        results = {
            'generated_files': generated_files,
            'confluence_analysis': confluence_analysis,
            'recommendations': recommendations,
            'timestamp': datetime.now().isoformat()
        }
        
        # Save to file
        results_filename = f"multi_timeframe_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(os.path.join(self.current_dir, results_filename), 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_filename}")
        
        return results


def main():
    """
    Main execution function
    """
    print("🚀 Multi-Timeframe Predictive Analyzer")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = MultiTimeframePredictiveAnalyzer()
    
    # Example usage - analyze Natural Gas across multiple timeframes
    results = analyzer.run_complete_multi_timeframe_analysis(
        ticker='NATURALGAS26AUG25',
        exchange='MCX',
        date='01-07-2025',
        intervals=['1', '5', '15', '30']  # Start with key timeframes
    )
    
    if results:
        print("\n✅ Multi-timeframe analysis completed successfully!")
        print("📊 Check the generated files for detailed results.")
    else:
        print("\n❌ Multi-timeframe analysis failed.")


if __name__ == "__main__":
    main()
