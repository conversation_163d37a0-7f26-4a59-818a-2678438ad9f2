"""
Simple Multi-Timeframe Analyzer

Uses existing files to perform multi-timeframe confluence analysis
without generating new data.
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class SimpleMultiTimeframeAnalyzer:
    """
    Simple multi-timeframe analyzer using existing files
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        print("🚀 Simple Multi-Timeframe Analyzer initialized")
    
    def find_usable_files(self) -> Dict[str, str]:
        """Find existing usable files for multi-timeframe analysis"""
        usable_files = {}
        
        print(f"\n🔍 SEARCHING FOR USABLE FILES")
        print("=" * 50)
        
        # Find all Natural Gas files with Time_Series_Indicators
        for file in os.listdir(self.current_dir):
            if ('NATURALGAS26AUG25' in file and 
                file.endswith('.xlsx') and 
                not file.startswith('~$')):
                
                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)
                    
                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                        
                        if df.shape[1] > 50:  # Should have many indicators
                            usable_files[file] = {
                                'shape': df.shape,
                                'indicators': df.shape[1]
                            }
                            print(f"✅ Found: {file} ({df.shape[1]} indicators, {df.shape[0]} time points)")
                        
                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")
        
        return usable_files
    
    def assign_timeframes(self, usable_files: Dict[str, Dict]) -> Dict[str, str]:
        """Assign timeframes to files"""
        timeframe_files = {}
        file_list = list(usable_files.keys())
        timeframe_names = ['1min', '5min', '15min', '30min', '60min']
        
        print(f"\n📊 ASSIGNING TIMEFRAMES")
        print("=" * 50)
        
        for i, file in enumerate(file_list[:5]):  # Use up to 5 files
            timeframe = timeframe_names[i] if i < len(timeframe_names) else f'tf{i+1}'
            timeframe_files[timeframe] = file
            indicators = usable_files[file]['indicators']
            print(f"📈 {timeframe}: {file} ({indicators} indicators)")
        
        return timeframe_files
    
    def load_timeframe_data(self, timeframe_files: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load data from timeframe files"""
        timeframe_data = {}
        
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)
        
        for timeframe, filename in timeframe_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                
                # Transpose if needed (indicators as columns, time as rows)
                if len(df.columns) > len(df):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    first_col = df.iloc[:, 0]
                    if any(':' in str(val) for val in first_col.head(10)):
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        df_transposed = df
                
                # Clean data
                for col in df_transposed.columns:
                    df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
                
                timeframe_data[timeframe] = df_transposed
                print(f"✅ {timeframe}: {df_transposed.shape}")
                
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")
        
        return timeframe_data
    
    def analyze_confluence(self, timeframe_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze confluence across timeframes for ALL indicators"""
        print(f"\n🔍 ANALYZING CONFLUENCE FOR ALL INDICATORS")
        print("=" * 50)

        # Get ALL indicators that appear in at least 2 timeframes
        all_indicators = set()
        for df in timeframe_data.values():
            all_indicators.update(df.columns)

        # Filter to indicators that appear in at least 2 timeframes
        valid_indicators = []
        for indicator in all_indicators:
            timeframe_count = sum(1 for df in timeframe_data.values() if indicator in df.columns)
            if timeframe_count >= 2:  # Must appear in at least 2 timeframes
                valid_indicators.append(indicator)

        print(f"📊 Found {len(valid_indicators)} indicators across timeframes")
        print(f"🔍 Analyzing confluence for ALL {len(valid_indicators)} indicators...")

        # Use all valid indicators instead of just key ones
        key_indicators = valid_indicators
        
        confluence_results = {}
        
        for indicator in key_indicators:
            confluence_info = {
                'timeframes_with_signal': [],
                'signal_strength': {},
                'confluence_score': 0.0,
                'latest_values': {}
            }
            
            total_timeframes = 0
            timeframes_with_signal = 0
            
            for timeframe, df in timeframe_data.items():
                if indicator in df.columns:
                    total_timeframes += 1
                    values = df[indicator].dropna()
                    
                    if len(values) > 0:
                        latest_value = values.iloc[-1]
                        confluence_info['latest_values'][timeframe] = latest_value
                        
                        # Determine if there's a signal using intelligent detection
                        has_signal = False
                        signal_strength = 0.0

                        # Intelligent signal detection based on indicator type
                        has_signal, signal_strength = self._detect_indicator_signal(indicator, latest_value, values)
                        
                        if has_signal:
                            timeframes_with_signal += 1
                            confluence_info['timeframes_with_signal'].append(timeframe)
                            confluence_info['signal_strength'][timeframe] = signal_strength
            
            # Calculate confluence score
            if total_timeframes > 0:
                confluence_info['confluence_score'] = timeframes_with_signal / total_timeframes
                confluence_results[indicator] = confluence_info
        
        return confluence_results

    def _detect_indicator_signal(self, indicator: str, latest_value: float, values: pd.Series) -> tuple:
        """
        Intelligent signal detection for any indicator type
        Returns (has_signal: bool, signal_strength: float)
        """
        has_signal = False
        signal_strength = 0.0

        try:
            # Handle NaN values
            if pd.isna(latest_value) or len(values) == 0:
                return False, 0.0

            indicator_upper = indicator.upper()

            # 1. SQUEEZE INDICATORS (Binary signals)
            if 'SQUEEZE' in indicator_upper and ('OFF' in indicator_upper or 'ON' in indicator_upper):
                has_signal = latest_value == 1
                signal_strength = 1.0 if has_signal else 0.0

            # 2. OSCILLATORS (Overbought/Oversold)
            elif any(osc in indicator_upper for osc in ['RSI', 'MFI', 'STOCH', 'WILLR', 'CCI']):
                if 'RSI' in indicator_upper or 'MFI' in indicator_upper:
                    # RSI/MFI: 0-100 scale, signals at extremes
                    if latest_value > 70 or latest_value < 30:
                        has_signal = True
                        signal_strength = abs(latest_value - 50) / 50
                elif 'STOCH' in indicator_upper:
                    # Stochastic: 0-100 scale
                    if latest_value > 80 or latest_value < 20:
                        has_signal = True
                        signal_strength = abs(latest_value - 50) / 50
                elif 'WILLR' in indicator_upper:
                    # Williams %R: -100 to 0 scale
                    if latest_value > -20 or latest_value < -80:
                        has_signal = True
                        signal_strength = abs(latest_value + 50) / 50
                elif 'CCI' in indicator_upper:
                    # CCI: signals beyond ±100
                    if abs(latest_value) > 100:
                        has_signal = True
                        signal_strength = min(abs(latest_value) / 200, 1.0)

            # 3. TREND INDICATORS
            elif any(trend in indicator_upper for trend in ['ADX', 'DM_', 'AROON', 'VORTEX', 'SUPERTREND']):
                if 'ADX' in indicator_upper:
                    # ADX: Trend strength, signal above 25
                    if latest_value > 25:
                        has_signal = True
                        signal_strength = min(latest_value / 50, 1.0)
                elif 'VORTEX' in indicator_upper:
                    # Vortex: Signal when deviating from 1.0
                    if latest_value > 1.1 or latest_value < 0.9:
                        has_signal = True
                        signal_strength = abs(latest_value - 1.0)
                elif 'SUPERTREND' in indicator_upper:
                    # SuperTrend: Trend change signals
                    if len(values) > 1:
                        prev_value = values.iloc[-2]
                        if latest_value != prev_value:
                            has_signal = True
                            signal_strength = 0.8
                elif 'AROON' in indicator_upper:
                    # Aroon: Signal at extremes
                    if latest_value > 70 or latest_value < 30:
                        has_signal = True
                        signal_strength = abs(latest_value - 50) / 50

            # 4. MOMENTUM INDICATORS
            elif any(mom in indicator_upper for mom in ['MACD', 'MOM', 'ROC', 'CMO', 'TRIX', 'PPO']):
                if len(values) > 1:
                    # Signal when momentum exceeds standard deviation
                    std_val = values.std()
                    if std_val > 0 and abs(latest_value) > std_val:
                        has_signal = True
                        signal_strength = min(abs(latest_value) / (std_val * 2), 1.0)

            # 5. VOLUME INDICATORS
            elif any(vol in indicator_upper for vol in ['OBV', 'AD', 'CMF', 'PVO', 'ADOSC']):
                if len(values) > 1:
                    # Signal on significant volume changes
                    recent_change = abs(latest_value - values.iloc[-2])
                    avg_change = values.diff().abs().mean()
                    if avg_change > 0 and recent_change > avg_change * 1.5:
                        has_signal = True
                        signal_strength = min(recent_change / (avg_change * 2), 1.0)

            # 6. VOLATILITY INDICATORS
            elif any(vol in indicator_upper for vol in ['ATR', 'NATR', 'TRUE_RANGE', 'BBANDS', 'KC_', 'DONCHIAN']):
                if 'BBANDS' in indicator_upper and 'BBP' in indicator_upper:
                    # Bollinger Band Position: Signal at extremes
                    if latest_value > 0.8 or latest_value < 0.2:
                        has_signal = True
                        signal_strength = abs(latest_value - 0.5) * 2
                elif len(values) > 1:
                    # Volatility expansion/contraction
                    mean_val = values.mean()
                    if mean_val > 0:
                        deviation = abs(latest_value - mean_val) / mean_val
                        if deviation > 0.2:  # 20% deviation
                            has_signal = True
                            signal_strength = min(deviation, 1.0)

            # 7. STATISTICAL INDICATORS
            elif any(stat in indicator_upper for stat in ['ZSCORE', 'SKEW', 'KURTOSIS', 'ENTROPY']):
                if 'ZSCORE' in indicator_upper:
                    # Z-Score: Signal beyond ±2
                    if abs(latest_value) > 2:
                        has_signal = True
                        signal_strength = min(abs(latest_value) / 3, 1.0)
                elif len(values) > 1:
                    # Statistical deviation from norm
                    std_val = values.std()
                    mean_val = values.mean()
                    if std_val > 0:
                        z_score = abs(latest_value - mean_val) / std_val
                        if z_score > 1.5:
                            has_signal = True
                            signal_strength = min(z_score / 3, 1.0)

            # 8. PRICE-BASED INDICATORS
            elif any(price in indicator_upper for price in ['OPEN', 'HIGH', 'LOW', 'CLOSE', 'HL2', 'HLC3', 'OHLC4']):
                if len(values) > 1:
                    # Price momentum signals
                    price_change = abs(latest_value - values.iloc[-2]) / values.iloc[-2]
                    if price_change > 0.01:  # 1% change
                        has_signal = True
                        signal_strength = min(price_change * 10, 1.0)

            # 9. MOVING AVERAGES
            elif any(ma in indicator_upper for ma in ['SMA', 'EMA', 'WMA', 'DEMA', 'TEMA', 'ZLMA', 'JMA']):
                if len(values) > 1:
                    # Moving average crossover/divergence
                    ma_change = abs(latest_value - values.iloc[-2]) / values.iloc[-2]
                    if ma_change > 0.005:  # 0.5% change
                        has_signal = True
                        signal_strength = min(ma_change * 20, 1.0)

            # 10. DEFAULT: Statistical approach for unknown indicators
            else:
                if len(values) > 5:
                    # Use statistical approach for unknown indicators
                    mean_val = values.mean()
                    std_val = values.std()
                    if std_val > 0:
                        z_score = abs(latest_value - mean_val) / std_val
                        if z_score > 1.5:
                            has_signal = True
                            signal_strength = min(z_score / 3, 1.0)

            # Ensure signal_strength is between 0 and 1
            signal_strength = max(0.0, min(1.0, signal_strength))

        except Exception as e:
            # If any error occurs, return no signal
            has_signal = False
            signal_strength = 0.0

        return has_signal, signal_strength
    
    def generate_recommendations(self, confluence_results: Dict[str, Any], timeframe_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Generate trading recommendations"""
        recommendations = []
        
        # Calculate overall confluence
        scores = [info['confluence_score'] for info in confluence_results.values()]
        overall_score = np.mean(scores) if scores else 0.0
        
        recommendations.append("🎯 MULTI-TIMEFRAME TRADING RECOMMENDATIONS")
        recommendations.append("=" * 50)
        
        if overall_score > 0.6:
            recommendations.append("🟢 HIGH CONFLUENCE - Strong multi-timeframe alignment")
            recommendations.append("   • High probability setup detected")
            recommendations.append("   • Consider larger position size")
            recommendations.append("   • Multiple timeframes confirm signal")
        elif overall_score > 0.3:
            recommendations.append("🟡 MODERATE CONFLUENCE - Some timeframe alignment")
            recommendations.append("   • Moderate probability setup")
            recommendations.append("   • Use standard position size")
            recommendations.append("   • Wait for additional confirmation")
        else:
            recommendations.append("🔴 LOW CONFLUENCE - Limited timeframe alignment")
            recommendations.append("   • Low probability setup")
            recommendations.append("   • Avoid trading or use small size")
            recommendations.append("   • Wait for better setup")
        
        recommendations.append(f"\n📊 Overall Confluence Score: {overall_score:.2f}")
        
        # Add specific indicator insights
        recommendations.append("\n🔍 TOP INDICATOR SIGNALS:")

        # Sort by confluence score
        sorted_indicators = sorted(confluence_results.items(),
                                 key=lambda x: x[1]['confluence_score'],
                                 reverse=True)

        # Show top 20 indicators with signals
        top_indicators = [item for item in sorted_indicators if item[1]['confluence_score'] > 0]

        recommendations.append(f"📊 Total indicators analyzed: {len(confluence_results)}")
        recommendations.append(f"🎯 Indicators with signals: {len(top_indicators)}")

        # Show top performers
        for i, (indicator, info) in enumerate(top_indicators[:20]):  # Top 20
            score = info['confluence_score']
            timeframes = ', '.join(info['timeframes_with_signal'])
            recommendations.append(f"   {i+1:2d}. {indicator}: {score:.2f} ({timeframes})")

        # Show perfect confluence indicators separately
        perfect_indicators = [item for item in sorted_indicators if item[1]['confluence_score'] == 1.0]
        if perfect_indicators:
            recommendations.append(f"\n⭐ PERFECT CONFLUENCE INDICATORS ({len(perfect_indicators)}):")
            for indicator, info in perfect_indicators:
                timeframes = ', '.join(info['timeframes_with_signal'])
                recommendations.append(f"   • {indicator} (All timeframes: {timeframes})")
        
        # Add timeframe-specific guidance
        recommendations.append("\n📈 TIMEFRAME-SPECIFIC GUIDANCE:")
        timeframes = list(timeframe_data.keys())
        
        if '1min' in timeframes:
            recommendations.append("   • 1-minute: Use for precise entry/exit timing")
        if '5min' in timeframes:
            recommendations.append("   • 5-minute: Primary signal confirmation")
        if '15min' in timeframes:
            recommendations.append("   • 15-minute: Trend direction validation")
        if '30min' in timeframes:
            recommendations.append("   • 30-minute: Major level identification")
        if '60min' in timeframes:
            recommendations.append("   • 60-minute: Overall market structure")
        
        return recommendations
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete multi-timeframe analysis"""
        print("\n🚀 STARTING SIMPLE MULTI-TIMEFRAME ANALYSIS")
        print("=" * 80)
        
        # Find usable files
        usable_files = self.find_usable_files()
        
        if len(usable_files) < 2:
            print("❌ Need at least 2 usable files for analysis")
            return {}
        
        # Assign timeframes
        timeframe_files = self.assign_timeframes(usable_files)
        
        # Load data
        timeframe_data = self.load_timeframe_data(timeframe_files)
        
        if len(timeframe_data) < 2:
            print("❌ Need at least 2 timeframes loaded for analysis")
            return {}
        
        # Analyze confluence
        confluence_results = self.analyze_confluence(timeframe_data)
        
        # Generate recommendations
        recommendations = self.generate_recommendations(confluence_results, timeframe_data)
        
        # Print recommendations
        print("\n")
        for rec in recommendations:
            print(rec)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results = {
            'timeframes_analyzed': list(timeframe_data.keys()),
            'files_used': timeframe_files,
            'confluence_analysis': confluence_results,
            'recommendations': recommendations,
            'timestamp': timestamp
        }
        
        # Save to file
        results_filename = f"simple_multi_timeframe_analysis_{timestamp}.json"
        with open(os.path.join(self.current_dir, results_filename), 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_filename}")
        
        return results


def main():
    """
    Main execution function
    """
    print("🚀 Simple Multi-Timeframe Analyzer")
    print("=" * 60)
    
    # Initialize and run analyzer
    analyzer = SimpleMultiTimeframeAnalyzer()
    results = analyzer.run_analysis()
    
    if results:
        print("\n✅ Multi-timeframe analysis completed successfully!")
        print("📊 Check the generated file for detailed results.")
    else:
        print("\n❌ Multi-timeframe analysis failed.")


if __name__ == "__main__":
    main()
