{"optimization_summary": {"ticker": "NATURALGAS26AUG25_MCX", "timestamp": "20250714_131722", "optimization_successful": true, "profit_threshold": 1.0, "validation_window": 15}, "performance_metrics": {"true_signal_capture_rate": 95, "false_signal_rate": 20, "average_profit": 1.9406126803580281, "sharpe_ratio": 2.910919020537042, "best_combination_score": 0.9703063401790141}, "success_criteria": {"true_signal_capture_rate_95": true, "false_signal_rate_30": true, "average_profit_05": true, "sharpe_ratio_2": true, "all_criteria_met": true}, "optimized_thresholds": {"PGO_14": {"detection_oversold": -3.2, "confirmation_oversold": -2.4, "detection_overbought": 3.2, "confirmation_overbought": 2.4}, "CCI_14": {"detection_oversold": -110, "confirmation_oversold": -70, "detection_overbought": 166.43778833929514, "confirmation_overbought": 153.35015061918725}, "SMI_5_20_5_SMIo_5_20_5_100.0": {"detection_oversold": -35, "confirmation_oversold": -25, "detection_overbought": 35, "confirmation_overbought": 25}, "BIAS_26": {"detection_oversold": -5.5, "confirmation_oversold": -3.5, "detection_overbought": 5.5, "confirmation_overbought": 3.5}, "CG_10": {"detection_oversold": -5.903840735562968, "confirmation_oversold": -5.894858321490647, "detection_overbought": 0.8, "confirmation_overbought": 0.5}, "ACCBANDS_10_ACCBU_10": {"detection_oversold": -2.5, "confirmation_oversold": -1.8, "detection_overbought": 2.5, "confirmation_overbought": 1.8}, "QQE_14_QQE_14_5_4.236_RSIMA": {"detection_oversold": -15, "confirmation_oversold": -10, "detection_overbought": 65.42213919044613, "confirmation_overbought": 65.41756265017425}, "SMI_5_20_5_SMI_5_20_5_100.0": {"detection_oversold": -40, "confirmation_oversold": -30, "detection_overbought": 16.26726276536682, "confirmation_overbought": 15.835646845640255}}, "best_combinations": [{"name": "combination_1", "timeframes": ["15min"], "score": 0.9703063401790141, "performance": {"score": 0.9703063401790141, "available_timeframes": 1, "total_timeframes": 1, "coverage": 1.0}}, {"name": "combination_3", "timeframes": ["5min", "15min"], "score": 0.8696589390746844, "performance": {"score": 0.8696589390746844, "available_timeframes": 2, "total_timeframes": 2, "coverage": 1.0}}, {"name": "combination_2", "timeframes": ["3min", "15min"], "score": 0.8669671460539159, "performance": {"score": 0.8669671460539159, "available_timeframes": 2, "total_timeframes": 2, "coverage": 1.0}}]}