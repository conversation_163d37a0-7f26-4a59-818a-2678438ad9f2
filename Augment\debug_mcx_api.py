"""
Debug MCX API Issues

This script helps debug the searchscrip API issues with MCX exchange
"""

import sys
import os
import json

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from shared_api_manager import get_api

def test_api_connection():
    """Test basic API connection"""
    print("🔍 Testing API Connection")
    print("=" * 30)
    
    try:
        api = get_api()
        print("✅ API object created successfully")
        
        # Test if we can access basic methods
        if hasattr(api, 'searchscrip'):
            print("✅ searchscrip method available")
        else:
            print("❌ searchscrip method not available")
            
        return api
    except Exception as e:
        print(f"❌ API connection failed: {str(e)}")
        return None

def test_exchanges(api):
    """Test different exchanges"""
    print("\n🔍 Testing Different Exchanges")
    print("=" * 40)
    
    exchanges = ['NSE', 'BSE', 'MCX', 'NFO']
    test_tickers = {
        'NSE': 'RELIANCE',
        'BSE': 'BATAINDIA', 
        'MCX': 'SILVER',
        'NFO': 'NIFTY'
    }
    
    for exchange in exchanges:
        ticker = test_tickers.get(exchange, 'TEST')
        print(f"\n📊 Testing {exchange} with ticker: {ticker}")
        print("-" * 25)
        
        try:
            ret = api.searchscrip(exchange=exchange, searchtext=ticker)
            
            print(f"Raw response type: {type(ret)}")
            print(f"Raw response: {str(ret)[:200]}...")
            
            if ret is None:
                print("❌ None response")
                continue
                
            if isinstance(ret, str):
                print("⚠️ String response (might be HTML error)")
                if len(ret) < 500:
                    print(f"Response content: {ret}")
                continue
                
            if isinstance(ret, dict):
                print("✅ Dictionary response")
                print(f"Status: {ret.get('stat', 'N/A')}")
                print(f"Message: {ret.get('emsg', 'N/A')}")
                
                values = ret.get('values', [])
                print(f"Results count: {len(values)}")
                
                if values:
                    first_result = values[0]
                    print(f"First result: {first_result.get('tsym', 'N/A')} - {first_result.get('token', 'N/A')}")
                    
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {str(e)}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def test_mcx_specific_tickers(api):
    """Test MCX specific tickers"""
    print("\n🥈 Testing MCX Specific Tickers")
    print("=" * 35)
    
    mcx_tickers = [
        'SILVER',
        'GOLD', 
        'CRUDEOIL',
        'NATURALGAS',
        'COPPER',
        'ZINC',
        'SILVERM',
        'GOLDM'
    ]
    
    for ticker in mcx_tickers:
        print(f"\n🔍 Testing MCX ticker: {ticker}")
        try:
            ret = api.searchscrip(exchange='MCX', searchtext=ticker)
            
            if ret and isinstance(ret, dict) and ret.get('stat') == 'Ok':
                values = ret.get('values', [])
                print(f"✅ Found {len(values)} results")
                
                if values:
                    for i, item in enumerate(values[:3]):
                        print(f"  {i+1}. {item.get('tsym', 'N/A')} (Token: {item.get('token', 'N/A')})")
            else:
                print(f"❌ No results or error: {ret}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def test_raw_api_call(api):
    """Test raw API call to see exact response"""
    print("\n🔍 Testing Raw API Call")
    print("=" * 25)
    
    try:
        print("Making raw searchscrip call for SILVER on MCX...")
        
        # Try to access the underlying API object
        if hasattr(api, 'api'):
            raw_api = api.api
            print("✅ Found underlying API object")
        else:
            raw_api = api
            print("⚠️ Using API object directly")
        
        # Make the call
        response = raw_api.searchscrip(exchange='MCX', searchtext='SILVER')
        
        print(f"Response type: {type(response)}")
        print(f"Response content: {response}")
        
        if isinstance(response, str):
            print("String response - checking if it's JSON...")
            try:
                import json
                parsed = json.loads(response)
                print(f"Parsed JSON: {parsed}")
            except:
                print("Not valid JSON")
                
    except Exception as e:
        print(f"❌ Raw API call failed: {str(e)}")

def main():
    """Main debug function"""
    print("🚀 MCX API Debug Tool")
    print("=" * 50)
    
    # Test 1: API Connection
    api = test_api_connection()
    if not api:
        print("❌ Cannot proceed without API connection")
        return
    
    # Test 2: Different exchanges
    test_exchanges(api)
    
    # Test 3: MCX specific tickers
    test_mcx_specific_tickers(api)
    
    # Test 4: Raw API call
    test_raw_api_call(api)
    
    print("\n" + "=" * 50)
    print("✅ Debug completed!")
    
    print("\n💡 Troubleshooting Tips:")
    print("1. If MCX returns empty responses, the exchange might be closed")
    print("2. MCX trading hours are typically 9:00 AM to 11:30 PM")
    print("3. Try different ticker formats (SILVER vs SILVERM vs SILVERMINI)")
    print("4. Check if the date is a trading day for MCX")
    
    print("\n🔧 Alternative MCX Tickers to Try:")
    print("- SILVER (main contract)")
    print("- SILVERM (mini contract)")
    print("- GOLD (main contract)")
    print("- GOLDM (mini contract)")
    print("- CRUDEOIL")
    print("- NATURALGAS")

if __name__ == "__main__":
    main()
