"""
Run Active Stocks Finder - Easy execution script

This script provides an easy way to run the active stocks finder with different options.
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from active_stocks_finder import ActiveStocksFinder, ALL_STOCKS, A_TO_G_STOCKS, KNOWN_GOOD_STOCKS, get_current_time_info, is_market_live

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_user_inputs():
    """Get user inputs for analysis"""
    
    print("\n" + "="*80)
    print("🔍 ACTIVE STOCKS FINDER")
    print("="*80)
    print("Find stocks with good movement patterns using candle analysis and technical indicators")
    print()
    
    # Date selection
    current_date, current_time = get_current_time_info()
    print(f"📅 Current date: {current_date}")
    print(f"🕐 Current time: {current_time}")
    
    date_input = input(f"📅 Enter date (DD-MM-YYYY) or press Enter for today ({current_date}): ").strip()
    if not date_input:
        date_input = current_date
    
    # Mode selection
    if is_market_live():
        print(f"🔴 Market is currently LIVE")
        mode_default = "live"
    else:
        print(f"⚫ Market is currently CLOSED")
        mode_default = "historical"
    
    print(f"\n📊 Analysis modes:")
    print(f"   1. Live mode (current time: {current_time})")
    print(f"   2. Historical mode (full day: 09:15-15:30)")
    
    mode_choice = input(f"📊 Choose mode (1/2) or press Enter for auto ({mode_default}): ").strip()
    if mode_choice == "1":
        mode = "live"
    elif mode_choice == "2":
        mode = "historical"
    else:
        mode = mode_default
    
    # Stock list selection
    print(f"\n📈 Stock lists:")
    print(f"   1. A-G stocks only ({len(A_TO_G_STOCKS)} stocks) - For testing/validation")
    print(f"   2. All stocks ({len(ALL_STOCKS)} stocks) - Complete analysis")
    print(f"   3. Known good stocks only ({len(KNOWN_GOOD_STOCKS)} stocks) - Quick test")
    
    stock_choice = input(f"📈 Choose stock list (1/2/3) or press Enter for A-G (1): ").strip()
    if stock_choice == "2":
        stock_list = ALL_STOCKS
        list_name = "All stocks"
    elif stock_choice == "3":
        stock_list = KNOWN_GOOD_STOCKS
        list_name = "Known good stocks"
    else:
        stock_list = A_TO_G_STOCKS
        list_name = "A-G stocks"
    
    # Minimum score
    min_score_input = input(f"🎯 Minimum movement score (default: 60.0): ").strip()
    try:
        min_score = float(min_score_input) if min_score_input else 60.0
    except ValueError:
        min_score = 60.0
    
    # Technical analysis option
    tech_analysis = input(f"🔬 Run comprehensive technical analysis on good stocks? (y/N): ").strip().lower()
    run_tech_analysis = tech_analysis in ['y', 'yes']
    
    return {
        'date': date_input,
        'mode': mode,
        'stock_list': stock_list,
        'list_name': list_name,
        'min_score': min_score,
        'run_tech_analysis': run_tech_analysis
    }

def main():
    """Main execution function"""
    
    try:
        # Get user inputs
        config = get_user_inputs()
        
        # Display configuration
        print(f"\n" + "="*80)
        print(f"🚀 STARTING ANALYSIS")
        print(f"="*80)
        print(f"📅 Date: {config['date']}")
        print(f"📊 Mode: {config['mode']}")
        print(f"📈 Stock list: {config['list_name']} ({len(config['stock_list'])} stocks)")
        print(f"🎯 Minimum score: {config['min_score']}")
        print(f"🔬 Technical analysis: {'Yes' if config['run_tech_analysis'] else 'No'}")
        
        # Determine time range
        if config['mode'] == 'live':
            current_time = get_current_time_info()[1]
            start_time = "09:15"
            end_time = current_time
            print(f"⏰ Time range: {start_time} to {current_time} (live)")
        else:
            start_time = "09:15"
            end_time = "15:30"
            print(f"⏰ Time range: {start_time} to {end_time} (full day)")
        
        print(f"\nPress Enter to continue or Ctrl+C to cancel...")
        input()
        
        # Initialize analyzer
        logger.info(f"Initializing Active Stocks Finder...")
        finder = ActiveStocksFinder(date=config['date'])
        
        # Analyze stocks
        logger.info(f"Starting analysis of {len(config['stock_list'])} stocks...")
        results = finder.analyze_multiple_stocks(config['stock_list'], start_time, end_time)
        
        # Filter good stocks
        good_stocks = finder.filter_good_stocks(results, config['min_score'])
        
        # Display results
        print(f"\n" + "="*80)
        print(f"📊 ANALYSIS RESULTS")
        print(f"="*80)
        print(f"Total stocks analyzed: {len(results)}")
        print(f"Good stocks found (score >= {config['min_score']}): {len(good_stocks)}")
        
        if good_stocks:
            print(f"\n🏆 Top Good Stocks:")
            for i, stock in enumerate(good_stocks[:15], 1):
                ticker = stock['ticker']
                score = stock['movement_score']['movement_score']
                grade = stock['movement_score']['grade']
                is_known = ticker in KNOWN_GOOD_STOCKS
                print(f"{i:2d}. {ticker:12s} - Score: {score:5.1f} ({grade}) {'✓ Known Good' if is_known else ''}")
        
        # Validation check
        known_good_found = [s['ticker'] for s in good_stocks if s['ticker'] in KNOWN_GOOD_STOCKS]
        print(f"\n✅ Validation: Found {len(known_good_found)}/{len(KNOWN_GOOD_STOCKS)} known good stocks")
        if known_good_found:
            print(f"Known good stocks found: {', '.join(known_good_found)}")
        
        # Export results
        excel_file = finder.export_to_excel(results)
        if excel_file:
            print(f"\n📁 Results exported to: {excel_file}")
        
        # Run technical analysis if requested
        if config['run_tech_analysis'] and good_stocks:
            print(f"\n🔬 Running comprehensive technical analysis...")
            try:
                from active_stocks_finder import run_technical_analysis_on_good_stocks
                run_technical_analysis_on_good_stocks(good_stocks, config['date'])
                print(f"✅ Technical analysis completed!")
            except Exception as e:
                print(f"❌ Technical analysis failed: {str(e)}")
        
        print(f"\n🎉 Analysis completed successfully!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ Analysis cancelled by user.")
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        print(f"❌ Analysis failed: {str(e)}")

if __name__ == "__main__":
    main()
