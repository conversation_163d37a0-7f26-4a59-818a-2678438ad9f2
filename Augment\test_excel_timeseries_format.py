"""
Test Excel Time-Series Format

This script tests if the Excel file contains the proper time-series format:
Indicator | Category | Time1 | Time2 | Time3 | ...
"""

import pandas as pd
import os
from datetime import datetime

def test_excel_timeseries_format():
    """Test if the latest Excel file contains proper time-series format"""
    
    # Find the most recent Excel file (exclude temporary files)
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and 'technical_analysis' in f and not f.startswith('~$')]
    if not excel_files:
        print("❌ No Excel files found")
        return False
    
    # Get the most recent file
    latest_file = max(excel_files, key=os.path.getmtime)
    print(f"📄 Testing Excel file: {latest_file}")
    
    try:
        # Read all sheets
        excel_data = pd.read_excel(latest_file, sheet_name=None)
        
        print(f"📊 Found {len(excel_data)} sheets: {list(excel_data.keys())}")
        
        # Check for Time_Series_Indicators sheet
        if 'Time_Series_Indicators' in excel_data:
            timeseries_df = excel_data['Time_Series_Indicators']
            print(f"\n✅ Found 'Time_Series_Indicators' sheet!")
            print(f"📊 Shape: {timeseries_df.shape}")
            print(f"📂 Columns: {list(timeseries_df.columns)}")
            
            # Check if it has the proper format: Indicator | Category | Time1 | Time2 | ...
            if len(timeseries_df.columns) >= 3:
                first_cols = list(timeseries_df.columns[:3])
                print(f"🔍 First 3 columns: {first_cols}")
                
                if 'Indicator' in first_cols and 'Category' in first_cols:
                    print(f"✅ Proper format detected: Indicator and Category columns found!")
                    
                    # Count time columns (should be time periods like 16:00, 16:05, etc.)
                    time_columns = [col for col in timeseries_df.columns if col not in ['Indicator', 'Category']]
                    print(f"⏰ Time periods: {len(time_columns)} columns")
                    print(f"🕐 Sample time periods: {time_columns[:5]}{'...' if len(time_columns) > 5 else ''}")
                    
                    # Show sample data
                    print(f"\n📋 Sample data (first 5 rows):")
                    print(timeseries_df.head())
                    
                    # Check for different indicator categories
                    if 'Category' in timeseries_df.columns:
                        categories = timeseries_df['Category'].unique()
                        print(f"\n📂 Categories found: {list(categories)}")
                        
                        # Count indicators per category
                        for category in categories:
                            if category not in ['---', 'Price']:
                                count = len(timeseries_df[timeseries_df['Category'] == category])
                                print(f"   {category}: {count} indicators")
                    
                    # Check if values are different across time periods (progressive calculation)
                    if len(time_columns) >= 2:
                        print(f"\n🔍 Testing progressive calculation (different values across time):")
                        
                        # Test a few indicators
                        test_indicators = ['RSI_14', 'SMA_10', 'EMA_10', 'MACD_12_26_9', 'ATR_14']
                        
                        for test_indicator in test_indicators:
                            matching_rows = timeseries_df[timeseries_df['Indicator'].str.contains(test_indicator, na=False)]
                            if len(matching_rows) > 0:
                                row = matching_rows.iloc[0]
                                values = []
                                for time_col in time_columns[:3]:  # Check first 3 time periods
                                    val = row[time_col]
                                    if pd.notna(val) and val != '':
                                        try:
                                            values.append(float(val))
                                        except:
                                            pass
                                
                                if len(values) >= 2:
                                    unique_values = len(set([round(v, 6) for v in values]))
                                    if unique_values > 1:
                                        print(f"   ✅ {test_indicator}: Different values - {[f'{v:.3f}' for v in values]}")
                                    else:
                                        print(f"   ⚠️ {test_indicator}: Same values - {values[0]:.3f}")
                                else:
                                    print(f"   ❌ {test_indicator}: Insufficient data")
                            else:
                                print(f"   ❌ {test_indicator}: Not found")
                    
                    return True
                else:
                    print(f"❌ Wrong format: Expected 'Indicator' and 'Category' columns")
                    return False
            else:
                print(f"❌ Insufficient columns: {len(timeseries_df.columns)}")
                return False
        else:
            print(f"❌ No 'Time_Series_Indicators' sheet found")
            print(f"📋 Available sheets: {list(excel_data.keys())}")
            
            # Check if any other sheet has time-series data
            for sheet_name, df in excel_data.items():
                if len(df.columns) > 3:
                    cols = list(df.columns)
                    if 'Indicator' in cols and 'Category' in cols:
                        print(f"✅ Found time-series format in '{sheet_name}' sheet!")
                        print(f"📊 Shape: {df.shape}")
                        print(f"📂 Columns: {list(df.columns)}")

                        # Show sample data
                        print(f"\n📋 Sample data from '{sheet_name}' sheet:")
                        print(df.head())

                        # Check time columns
                        time_columns = [col for col in df.columns if col not in ['Indicator', 'Category']]
                        print(f"\n⏰ Time periods: {len(time_columns)} columns")
                        if time_columns:
                            print(f"🕐 Time periods: {time_columns}")

                        return True
            
            return False
            
    except Exception as e:
        print(f"❌ Error reading Excel file: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING EXCEL TIME-SERIES FORMAT")
    print("=" * 60)
    print("Checking if the Excel file contains proper time-series data:")
    print("Expected format: Indicator | Category | Time1 | Time2 | Time3 | ...")
    print()
    
    success = test_excel_timeseries_format()
    
    print(f"\n🎯 RESULT:")
    if success:
        print("✅ SUCCESS: Excel file contains proper time-series format!")
        print("✅ Time-series data with indicators across multiple time periods is working!")
        print("✅ Ready for professional AI/ML analysis with progressive indicators!")
    else:
        print("❌ FAILED: Excel file does not contain proper time-series format")
        print("❌ Time-series data needs to be fixed")

if __name__ == "__main__":
    main()
