"""
Pro Trading Data Analyst - Predictive Signatures Analyzer

This script analyzes high-frequency trading data to uncover hidden "predictive signatures" 
that signal imminent breakouts, breakdowns, and strong trending moves.

Key Events to Analyze:
- Natural Gas (12:54 PM breakdown, 16:08 PM breakout, 11:00 AM falling knife)
- Crude Oil (similar patterns)

The script identifies pre-event conditions using 200+ technical indicators
and creates reusable analysis functions for future datasets.
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class PredictiveSignaturesAnalyzer:
    """
    Advanced analyzer for identifying predictive signatures in trading data
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.natural_gas_file = "technical_analysis_NATURALGAS26AUG25_MCX_signals_20250701_163713.xlsx"
        self.crude_oil_file = "technical_analysis_CRUDEOIL21JUL25_MCX_signals_20250702_012815.xlsx"
        
        # Key events for Natural Gas analysis
        self.key_events = {
            'natural_gas': {
                'sharp_breakdown': '12:54',
                'sharp_breakout': '16:08', 
                'falling_knife_start': '11:00'
            }
        }
        
        # Lookback window for pre-event analysis (10 minutes)
        self.lookback_minutes = 10
        
        print("🚀 Predictive Signatures Analyzer initialized")
        print(f"📊 Target files: {self.natural_gas_file}, {self.crude_oil_file}")
        print(f"⏰ Lookback window: {self.lookback_minutes} minutes")
    
    def load_excel_data(self, filename):
        """
        Load and prepare Excel data from Time_Series_Indicators sheet
        """
        try:
            filepath = os.path.join(current_dir, filename)
            if not os.path.exists(filepath):
                print(f"❌ File not found: {filepath}")
                return None

            print(f"📂 Loading data from: {filename}")

            # Load the Time_Series_Indicators sheet
            df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')

            # Transpose so timestamps become index and indicators become columns
            df_transposed = df.set_index(df.columns[0]).T

            # Clean up the index - convert time strings to proper datetime
            # First, let's see what the index looks like
            print(f"🔍 Index sample: {df_transposed.index[:5].tolist()}")

            # Convert index to datetime with proper handling
            time_index = []
            for idx in df_transposed.index:
                try:
                    # Try different time formats
                    if isinstance(idx, str):
                        if ':' in idx:
                            # Format like "12:54"
                            time_obj = pd.to_datetime(f"2025-07-01 {idx}:00", format='%Y-%m-%d %H:%M:%S')
                        else:
                            # Skip non-time entries
                            continue
                    else:
                        # Skip non-string entries
                        continue
                    time_index.append(time_obj)
                except:
                    # Skip invalid entries
                    continue

            # Filter dataframe to only include valid time entries
            valid_indices = []
            for i, idx in enumerate(df_transposed.index):
                if isinstance(idx, str) and ':' in idx:
                    valid_indices.append(i)

            df_filtered = df_transposed.iloc[valid_indices].copy()

            # Set the proper datetime index
            if len(time_index) == len(df_filtered):
                df_filtered.index = time_index
            else:
                print(f"⚠️ Index length mismatch: {len(time_index)} vs {len(df_filtered)}")
                return None

            # Remove any rows with all NaN values
            df_filtered = df_filtered.dropna(how='all')

            # Clean the data - convert to numeric, replacing non-numeric values with NaN
            for col in df_filtered.columns:
                df_filtered[col] = pd.to_numeric(df_filtered[col], errors='coerce')

            # Remove columns that are all NaN after conversion
            df_filtered = df_filtered.dropna(axis=1, how='all')

            print(f"✅ Data loaded successfully")
            print(f"📈 Shape: {df_filtered.shape}")
            if len(df_filtered) > 0:
                print(f"🕐 Time range: {df_filtered.index.min().strftime('%H:%M')} to {df_filtered.index.max().strftime('%H:%M')}")
            print(f"📊 Indicators: {len(df_filtered.columns)}")

            return df_filtered

        except Exception as e:
            print(f"❌ Error loading {filename}: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_pre_event_window(self, df, event_time_str, lookback_minutes=10):
        """
        Extract pre-event window data for analysis

        Args:
            df: DataFrame with datetime index
            event_time_str: Event time as string (e.g., '12:54')
            lookback_minutes: Minutes to look back before event

        Returns:
            DataFrame with pre-event window data
        """
        try:
            # Convert event time to datetime (using same date as data)
            event_time = pd.to_datetime(f"2025-07-01 {event_time_str}:00")

            # Calculate start time for lookback window
            start_time = event_time - timedelta(minutes=lookback_minutes)

            # Filter data for the pre-event window using datetime comparison
            mask = (df.index >= start_time) & (df.index < event_time)
            pre_event_data = df[mask].copy()

            print(f"🔍 Pre-event window for {event_time_str}:")
            print(f"   📅 Window: {start_time.strftime('%H:%M')} to {event_time.strftime('%H:%M')}")
            print(f"   📊 Data points: {len(pre_event_data)}")

            if len(pre_event_data) == 0:
                # Try to find closest available data
                print(f"   ⚠️ No exact data found, looking for closest available data...")

                # Find data points within a wider window
                wider_start = event_time - timedelta(minutes=lookback_minutes*2)
                wider_mask = (df.index >= wider_start) & (df.index < event_time)
                wider_data = df[wider_mask]

                if len(wider_data) > 0:
                    print(f"   📊 Found {len(wider_data)} data points in wider window")
                    # Take the last few points before the event
                    pre_event_data = wider_data.tail(min(10, len(wider_data)))
                    print(f"   📊 Using {len(pre_event_data)} most recent data points")

            return pre_event_data

        except Exception as e:
            print(f"❌ Error extracting pre-event window: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def analyze_volatility_indicators(self, df):
        """
        Analyze volatility indicators for predictive signatures
        """
        volatility_analysis = {}
        
        # Find volatility-related indicators
        volatility_indicators = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                               ['SQUEEZE', 'BBANDS', 'ATR', 'BANDWIDTH', 'VOLATILITY', 'VIX'])]
        
        print(f"🔥 Analyzing {len(volatility_indicators)} volatility indicators")
        
        for indicator in volatility_indicators:
            if indicator in df.columns:
                try:
                    values = pd.to_numeric(df[indicator], errors='coerce').dropna()
                    if len(values) > 0:
                        volatility_analysis[indicator] = {
                            'min': values.min(),
                            'max': values.max(),
                            'mean': values.mean(),
                            'std': values.std(),
                            'trend': 'increasing' if values.iloc[-1] > values.iloc[0] else 'decreasing',
                            'final_value': values.iloc[-1] if len(values) > 0 else None,
                            'change_pct': ((values.iloc[-1] - values.iloc[0]) / values.iloc[0] * 100) if values.iloc[0] != 0 else 0
                        }
                except Exception as e:
                    print(f"⚠️ Error analyzing {indicator}: {str(e)}")
                    continue
        
        return volatility_analysis
    
    def analyze_trend_indicators(self, df):
        """
        Analyze trend and directional indicators
        """
        trend_analysis = {}
        
        # Find trend-related indicators
        trend_indicators = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                          ['SUPERTREND', 'PSAR', 'ADX', 'DI', 'TREND', 'MA', 'EMA', 'SMA'])]
        
        print(f"📈 Analyzing {len(trend_indicators)} trend indicators")
        
        for indicator in trend_indicators:
            if indicator in df.columns:
                try:
                    values = pd.to_numeric(df[indicator], errors='coerce').dropna()
                    if len(values) > 0:
                        trend_analysis[indicator] = {
                            'direction': 'bullish' if values.iloc[-1] > values.mean() else 'bearish',
                            'strength': abs(values.iloc[-1] - values.mean()) / values.std() if values.std() > 0 else 0,
                            'momentum': 'accelerating' if len(values) >= 3 and abs(values.iloc[-1] - values.iloc[-2]) > abs(values.iloc[-2] - values.iloc[-3]) else 'decelerating',
                            'final_value': values.iloc[-1],
                            'crossover': self._detect_crossover(values)
                        }
                except Exception as e:
                    print(f"⚠️ Error analyzing {indicator}: {str(e)}")
                    continue
        
        return trend_analysis
    
    def analyze_momentum_indicators(self, df):
        """
        Analyze momentum and volume indicators
        """
        momentum_analysis = {}
        
        # Find momentum-related indicators
        momentum_indicators = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                             ['MACD', 'RSI', 'OBV', 'CMF', 'VOLUME', 'MOMENTUM', 'STOCH', 'CCI'])]
        
        print(f"⚡ Analyzing {len(momentum_indicators)} momentum indicators")
        
        for indicator in momentum_indicators:
            if indicator in df.columns:
                try:
                    values = pd.to_numeric(df[indicator], errors='coerce').dropna()
                    if len(values) > 0:
                        momentum_analysis[indicator] = {
                            'divergence': self._detect_divergence(values),
                            'overbought_oversold': self._check_overbought_oversold(indicator, values.iloc[-1]),
                            'zero_line_cross': self._detect_zero_cross(values),
                            'histogram_direction': 'positive' if values.iloc[-1] > 0 else 'negative',
                            'acceleration': values.iloc[-1] - values.iloc[-2] if len(values) >= 2 else 0
                        }
                except Exception as e:
                    print(f"⚠️ Error analyzing {indicator}: {str(e)}")
                    continue
        
        return momentum_analysis
    
    def _detect_crossover(self, values):
        """Detect recent crossovers in indicator values"""
        if len(values) < 3:
            return 'insufficient_data'
        
        recent_change = values.iloc[-1] - values.iloc[-3]
        if abs(recent_change) > values.std():
            return 'significant_crossover'
        return 'no_crossover'
    
    def _detect_divergence(self, values):
        """Detect divergence patterns"""
        if len(values) < 5:
            return 'insufficient_data'
        
        # Simple divergence detection based on trend reversal
        early_trend = values.iloc[1] - values.iloc[0]
        late_trend = values.iloc[-1] - values.iloc[-2]
        
        if early_trend * late_trend < 0:  # Opposite signs indicate divergence
            return 'divergence_detected'
        return 'no_divergence'
    
    def _check_overbought_oversold(self, indicator_name, value):
        """Check if indicator is in overbought/oversold territory"""
        if 'RSI' in indicator_name.upper():
            if value > 70:
                return 'overbought'
            elif value < 30:
                return 'oversold'
        elif 'STOCH' in indicator_name.upper():
            if value > 80:
                return 'overbought'
            elif value < 20:
                return 'oversold'
        
        return 'neutral'
    
    def _detect_zero_cross(self, values):
        """Detect zero line crossings"""
        if len(values) < 2:
            return 'insufficient_data'

        if (values.iloc[-2] <= 0 < values.iloc[-1]) or (values.iloc[-2] >= 0 > values.iloc[-1]):
            return 'zero_cross_detected'
        return 'no_zero_cross'

    def analyze_event_signature(self, df, event_name, event_time):
        """
        Comprehensive analysis of predictive signature for a specific event
        """
        print(f"\n🎯 ANALYZING EVENT: {event_name} at {event_time}")
        print("=" * 60)

        # Get pre-event window
        pre_event_data = self.get_pre_event_window(df, event_time, self.lookback_minutes)

        if pre_event_data is None or len(pre_event_data) == 0:
            print(f"❌ No data available for pre-event analysis")
            return None

        # Perform multi-indicator analysis
        volatility_analysis = self.analyze_volatility_indicators(pre_event_data)
        trend_analysis = self.analyze_trend_indicators(pre_event_data)
        momentum_analysis = self.analyze_momentum_indicators(pre_event_data)

        # Generate comprehensive signature report
        signature = {
            'event_name': event_name,
            'event_time': event_time,
            'analysis_window': f"{self.lookback_minutes} minutes before event",
            'data_points': len(pre_event_data),
            'volatility_signature': volatility_analysis,
            'trend_signature': trend_analysis,
            'momentum_signature': momentum_analysis,
            'confluence_score': self._calculate_confluence_score(volatility_analysis, trend_analysis, momentum_analysis)
        }

        # Print human-readable report
        self._print_signature_report(signature)

        return signature

    def _calculate_confluence_score(self, volatility, trend, momentum):
        """
        Calculate confluence score based on multiple indicator alignments
        """
        score = 0
        total_indicators = 0

        # Volatility confluence
        for indicator, data in volatility.items():
            total_indicators += 1
            if 'SQUEEZE' in indicator.upper() and data.get('final_value', 0) == 1:
                score += 2  # Squeeze release is highly significant
            elif data.get('trend') == 'decreasing' and 'BANDWIDTH' in indicator.upper():
                score += 1  # Contracting volatility

        # Trend confluence
        for indicator, data in trend.items():
            total_indicators += 1
            if data.get('crossover') == 'significant_crossover':
                score += 2
            elif data.get('strength', 0) > 1:
                score += 1

        # Momentum confluence
        for indicator, data in momentum.items():
            total_indicators += 1
            if data.get('divergence') == 'divergence_detected':
                score += 2
            elif data.get('zero_line_cross') == 'zero_cross_detected':
                score += 1

        # Normalize score (0-100)
        confluence_score = (score / max(total_indicators, 1)) * 100 if total_indicators > 0 else 0

        return {
            'score': confluence_score,
            'raw_score': score,
            'total_indicators': total_indicators,
            'interpretation': self._interpret_confluence_score(confluence_score)
        }

    def _interpret_confluence_score(self, score):
        """Interpret confluence score"""
        if score >= 80:
            return "EXTREMELY HIGH - Multiple strong signals aligned"
        elif score >= 60:
            return "HIGH - Several indicators showing confluence"
        elif score >= 40:
            return "MODERATE - Some indicator alignment"
        elif score >= 20:
            return "LOW - Limited confluence"
        else:
            return "VERY LOW - No significant confluence"

    def _print_signature_report(self, signature):
        """
        Print human-readable signature report
        """
        print(f"\n📊 PREDICTIVE SIGNATURE REPORT")
        print(f"Event: {signature['event_name']} at {signature['event_time']}")
        print(f"Analysis Window: {signature['analysis_window']}")
        print(f"Data Points: {signature['data_points']}")

        # Confluence Score
        confluence = signature['confluence_score']
        print(f"\n🎯 CONFLUENCE SCORE: {confluence['score']:.1f}/100")
        print(f"Interpretation: {confluence['interpretation']}")

        # Volatility Signature
        print(f"\n🔥 VOLATILITY SIGNATURE:")
        vol_sig = signature['volatility_signature']
        if vol_sig:
            for indicator, data in list(vol_sig.items())[:5]:  # Show top 5
                print(f"  • {indicator}: {data.get('trend', 'N/A')} trend, "
                      f"Change: {data.get('change_pct', 0):.1f}%")
        else:
            print("  No volatility indicators found")

        # Trend Signature
        print(f"\n📈 TREND SIGNATURE:")
        trend_sig = signature['trend_signature']
        if trend_sig:
            for indicator, data in list(trend_sig.items())[:5]:  # Show top 5
                print(f"  • {indicator}: {data.get('direction', 'N/A')} direction, "
                      f"Strength: {data.get('strength', 0):.2f}")
        else:
            print("  No trend indicators found")

        # Momentum Signature
        print(f"\n⚡ MOMENTUM SIGNATURE:")
        mom_sig = signature['momentum_signature']
        if mom_sig:
            for indicator, data in list(mom_sig.items())[:5]:  # Show top 5
                print(f"  • {indicator}: {data.get('histogram_direction', 'N/A')}, "
                      f"Divergence: {data.get('divergence', 'N/A')}")
        else:
            print("  No momentum indicators found")

        print("\n" + "=" * 60)

    def analyze_all_natural_gas_events(self):
        """
        Analyze all key events for Natural Gas dataset
        """
        print("\n🚀 STARTING COMPREHENSIVE NATURAL GAS ANALYSIS")
        print("=" * 80)

        # Load Natural Gas data
        ng_data = self.load_excel_data(self.natural_gas_file)
        if ng_data is None:
            print("❌ Failed to load Natural Gas data")
            return None

        results = {}

        # Analyze each key event
        for event_name, event_time in self.key_events['natural_gas'].items():
            signature = self.analyze_event_signature(ng_data, event_name, event_time)
            if signature:
                results[event_name] = signature

        return results

    def analyze_crude_oil_events(self):
        """
        Analyze Crude Oil dataset for similar patterns
        """
        print("\n🛢️ STARTING CRUDE OIL ANALYSIS")
        print("=" * 80)

        # Load Crude Oil data
        co_data = self.load_excel_data(self.crude_oil_file)
        if co_data is None:
            print("❌ Failed to load Crude Oil data")
            return None

        # For Crude Oil, we'll analyze the same time periods to see if similar patterns exist
        results = {}

        for event_name, event_time in self.key_events['natural_gas'].items():
            print(f"\n🔍 Checking Crude Oil for similar patterns at {event_time}")
            signature = self.analyze_event_signature(co_data, f"crude_oil_{event_name}", event_time)
            if signature:
                results[f"crude_oil_{event_name}"] = signature

        return results

    def generate_predictive_function(self, signatures):
        """
        Generate a reusable function based on discovered signatures
        """
        print("\n🧠 GENERATING PREDICTIVE FUNCTION")
        print("=" * 60)

        # Analyze common patterns across all signatures
        common_patterns = self._extract_common_patterns(signatures)

        # Generate function code
        function_code = self._create_predictive_function_code(common_patterns)

        return function_code, common_patterns

    def _extract_common_patterns(self, signatures):
        """
        Extract common patterns across multiple event signatures
        """
        patterns = {
            'high_confluence_indicators': [],
            'volatility_patterns': [],
            'trend_patterns': [],
            'momentum_patterns': []
        }

        for event_name, signature in signatures.items():
            confluence_score = signature.get('confluence_score', {}).get('score', 0)

            if confluence_score > 60:  # High confluence events
                patterns['high_confluence_indicators'].append(event_name)

                # Extract specific patterns
                vol_sig = signature.get('volatility_signature', {})
                for indicator, data in vol_sig.items():
                    if 'SQUEEZE' in indicator.upper() and data.get('final_value') == 1:
                        patterns['volatility_patterns'].append('squeeze_release')
                    elif data.get('trend') == 'decreasing' and 'BANDWIDTH' in indicator.upper():
                        patterns['volatility_patterns'].append('bandwidth_contraction')

                trend_sig = signature.get('trend_signature', {})
                for indicator, data in trend_sig.items():
                    if data.get('crossover') == 'significant_crossover':
                        patterns['trend_patterns'].append('trend_crossover')

                mom_sig = signature.get('momentum_signature', {})
                for indicator, data in mom_sig.items():
                    if data.get('divergence') == 'divergence_detected':
                        patterns['momentum_patterns'].append('momentum_divergence')

        return patterns

    def _create_predictive_function_code(self, patterns):
        """
        Create Python code for a generalized predictive function
        """
        function_code = '''
def predict_breakout_probability(df, timestamp, lookback_minutes=10):
    """
    Generalized function to predict breakout probability based on discovered patterns

    Args:
        df: DataFrame with technical indicators (indicators as columns, time as index)
        timestamp: Target timestamp to analyze
        lookback_minutes: Minutes to look back for analysis

    Returns:
        dict: Prediction results with probability score and reasoning
    """
    import pandas as pd
    from datetime import timedelta

    # Extract pre-event window
    target_time = pd.to_datetime(timestamp)
    start_time = target_time - timedelta(minutes=lookback_minutes)

    # Filter data
    mask = (df.index >= start_time) & (df.index < target_time)
    pre_event_data = df[mask]

    if len(pre_event_data) == 0:
        return {"probability": 0, "reason": "No data available"}

    probability_score = 0
    reasons = []

    # Check for discovered patterns
'''

        # Add pattern-specific checks based on discovered patterns
        if 'squeeze_release' in patterns['volatility_patterns']:
            function_code += '''
    # Check for Squeeze Release Pattern
    squeeze_indicators = [col for col in df.columns if 'SQUEEZE' in col.upper()]
    for indicator in squeeze_indicators:
        if indicator in pre_event_data.columns:
            values = pre_event_data[indicator].dropna()
            if len(values) > 0 and values.iloc[-1] == 1:  # Squeeze OFF
                probability_score += 25
                reasons.append("Squeeze release detected")
'''

        if 'bandwidth_contraction' in patterns['volatility_patterns']:
            function_code += '''
    # Check for Bandwidth Contraction
    bandwidth_indicators = [col for col in df.columns if 'BANDWIDTH' in col.upper() or 'BBB' in col.upper()]
    for indicator in bandwidth_indicators:
        if indicator in pre_event_data.columns:
            values = pre_event_data[indicator].dropna()
            if len(values) >= 3:
                recent_trend = values.iloc[-1] - values.iloc[-3]
                if recent_trend < 0:  # Contracting
                    probability_score += 15
                    reasons.append("Volatility contraction detected")
'''

        function_code += '''
    # Normalize probability score
    probability_score = min(probability_score, 100)

    return {
        "probability": probability_score,
        "reasons": reasons,
        "interpretation": "HIGH" if probability_score > 60 else "MODERATE" if probability_score > 30 else "LOW"
    }
'''

        return function_code

    def run_complete_analysis(self):
        """
        Run complete analysis on both datasets and generate predictive function
        """
        print("\n🎯 STARTING COMPLETE PREDICTIVE SIGNATURES ANALYSIS")
        print("=" * 80)
        print("Analyzing high-frequency trading data to uncover predictive signatures")
        print("for breakouts, breakdowns, and strong trending moves...")

        all_signatures = {}

        # Analyze Natural Gas events
        ng_results = self.analyze_all_natural_gas_events()
        if ng_results:
            all_signatures.update(ng_results)

        # Analyze Crude Oil events
        co_results = self.analyze_crude_oil_events()
        if co_results:
            all_signatures.update(co_results)

        if not all_signatures:
            print("❌ No signatures could be analyzed")
            return None

        # Generate predictive function
        function_code, patterns = self.generate_predictive_function(all_signatures)

        # Save results
        self._save_analysis_results(all_signatures, function_code, patterns)

        # Print summary
        self._print_final_summary(all_signatures, patterns)

        return {
            'signatures': all_signatures,
            'predictive_function': function_code,
            'patterns': patterns
        }

    def _save_analysis_results(self, signatures, function_code, patterns):
        """
        Save analysis results to files
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save predictive function
        function_filename = f"predictive_breakout_function_{timestamp}.py"
        with open(os.path.join(current_dir, function_filename), 'w') as f:
            f.write(f'"""\nPredictive Breakout Function\nGenerated: {datetime.now()}\n"""\n')
            f.write(function_code)

        print(f"💾 Predictive function saved to: {function_filename}")

        # Save detailed analysis
        import json
        analysis_filename = f"predictive_signatures_analysis_{timestamp}.json"
        with open(os.path.join(current_dir, analysis_filename), 'w') as f:
            json.dump({
                'signatures': signatures,
                'patterns': patterns,
                'analysis_timestamp': timestamp
            }, f, indent=2, default=str)

        print(f"💾 Detailed analysis saved to: {analysis_filename}")

    def _print_final_summary(self, signatures, patterns):
        """
        Print final summary of analysis
        """
        print("\n🏆 FINAL ANALYSIS SUMMARY")
        print("=" * 80)

        print(f"📊 Total Events Analyzed: {len(signatures)}")
        print(f"🎯 High Confluence Events: {len(patterns['high_confluence_indicators'])}")

        print(f"\n🔥 DISCOVERED VOLATILITY PATTERNS:")
        for pattern in set(patterns['volatility_patterns']):
            count = patterns['volatility_patterns'].count(pattern)
            print(f"  • {pattern}: {count} occurrences")

        print(f"\n📈 DISCOVERED TREND PATTERNS:")
        for pattern in set(patterns['trend_patterns']):
            count = patterns['trend_patterns'].count(pattern)
            print(f"  • {pattern}: {count} occurrences")

        print(f"\n⚡ DISCOVERED MOMENTUM PATTERNS:")
        for pattern in set(patterns['momentum_patterns']):
            count = patterns['momentum_patterns'].count(pattern)
            print(f"  • {pattern}: {count} occurrences")

        print(f"\n🧠 PREDICTIVE INSIGHTS:")
        if len(patterns['high_confluence_indicators']) > 0:
            print(f"  • {len(patterns['high_confluence_indicators'])} events showed high predictive confluence")
            print(f"  • Most reliable patterns: {', '.join(set(patterns['volatility_patterns'][:3]))}")
        else:
            print("  • No high-confluence patterns detected - may need different timeframes")

        print("\n✅ Analysis complete! Use the generated predictive function for future analysis.")


def main():
    """
    Main execution function
    """
    print("🚀 Pro Trading Data Analyst - Predictive Signatures Analyzer")
    print("=" * 80)

    # Initialize analyzer
    analyzer = PredictiveSignaturesAnalyzer()

    # Run complete analysis
    results = analyzer.run_complete_analysis()

    if results:
        print("\n🎉 Analysis completed successfully!")
        print("Check the generated files for detailed results and predictive function.")
    else:
        print("\n❌ Analysis failed. Please check the data files and try again.")


if __name__ == "__main__":
    main()
