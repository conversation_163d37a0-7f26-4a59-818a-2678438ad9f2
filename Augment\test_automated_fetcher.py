#!/usr/bin/env python3
"""
Test script for automated technical data fetcher
Tests the enhanced optimizer's ability to fetch real API data
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from enhanced_ai_ml_threshold_optimizer import EnhancedAIMLThresholdOptimizer

def test_automated_fetcher():
    """Test the automated fetcher with real API data"""
    
    print("🚀 TESTING AUTOMATED TECHNICAL DATA FETCHER")
    print("=" * 60)
    
    try:
        # Initialize optimizer
        optimizer = EnhancedAIMLThresholdOptimizer()
        print(f"✅ Using analyzer: {optimizer.analyzer_script.name}")
        
        # Test configuration
        ticker = "NATURALGAS26AUG25"
        exchange = "MCX"
        start_date = datetime(2025, 7, 14)  # Monday
        num_days = 2  # Just test 2 days
        intervals = ["1", "3"]  # Just test 2 intervals
        method = "extension"
        functions = "pgo,cci,cg"
        
        print(f"📊 Test Configuration:")
        print(f"   Ticker: {ticker}")
        print(f"   Exchange: {exchange}")
        print(f"   Start Date: {start_date.strftime('%d-%m-%Y')}")
        print(f"   Days: {num_days}")
        print(f"   Intervals: {intervals}")
        print(f"   Method: {method}")
        print(f"   Functions: {functions}")
        print()
        
        # Generate trading dates (excluding weekends)
        trading_dates = []
        current_date = start_date
        days_added = 0
        
        while days_added < num_days:
            # Skip weekends (Saturday=5, Sunday=6)
            if current_date.weekday() < 5:
                trading_dates.append(current_date)
                days_added += 1
            current_date = current_date.replace(day=current_date.day - 1)
        
        print(f"📅 Trading dates: {[d.strftime('%d-%m-%Y') for d in trading_dates]}")
        print()
        
        # Create output folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_folder = Path(current_dir) / f"{ticker}_TestAPIData_{timestamp}"
        output_folder.mkdir(exist_ok=True)
        
        print(f"📁 Output folder: {output_folder}")
        print()
        
        # Test each combination
        total_calls = len(trading_dates) * len(intervals)
        completed = 0
        failed = 0
        
        print(f"🔄 Starting {total_calls} API calls...")
        print()
        
        for i, date in enumerate(trading_dates, 1):
            print(f"📅 Processing date {i}/{len(trading_dates)}: {date.strftime('%d-%m-%Y')}")
            
            for j, interval in enumerate(intervals, 1):
                print(f"  ⏱️ Interval {j}/{len(intervals)}: {interval} minutes")
                
                try:
                    success = optimizer.run_single_technical_analyzer(
                        ticker=ticker,
                        exchange=exchange,
                        date=date,
                        method=method,
                        functions=functions,
                        interval=interval,
                        output_folder=output_folder
                    )
                    
                    if success:
                        completed += 1
                        print(f"    ✅ SUCCESS: {ticker} {exchange} {date.strftime('%d-%m-%Y')} {interval}min")
                    else:
                        failed += 1
                        print(f"    ❌ FAILED: {ticker} {exchange} {date.strftime('%d-%m-%Y')} {interval}min")
                        
                except Exception as e:
                    failed += 1
                    print(f"    ❌ ERROR: {ticker} {exchange} {date.strftime('%d-%m-%Y')} {interval}min - {str(e)}")
                
                print(f"    📊 Progress: {((completed + failed) / total_calls * 100):.1f}% ({completed + failed}/{total_calls})")
                print()
        
        # Summary
        print("📊 FINAL RESULTS:")
        print("=" * 40)
        print(f"✅ Successful API calls: {completed}")
        print(f"❌ Failed API calls: {failed}")
        print(f"📊 Success rate: {(completed / total_calls * 100):.1f}%")
        print(f"📁 Output folder: {output_folder}")
        
        # List generated files
        excel_files = list(output_folder.glob("*.xlsx"))
        print(f"📄 Generated Excel files: {len(excel_files)}")
        for file in excel_files[:5]:  # Show first 5 files
            print(f"   - {file.name}")
        if len(excel_files) > 5:
            print(f"   ... and {len(excel_files) - 5} more files")
        
        return completed > 0
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_automated_fetcher()
    if success:
        print("\n🎉 AUTOMATED FETCHER TEST PASSED!")
        sys.exit(0)
    else:
        print("\n❌ AUTOMATED FETCHER TEST FAILED!")
        sys.exit(1)
