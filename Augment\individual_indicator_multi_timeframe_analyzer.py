"""
Individual Indicator Multi-Timeframe Analyzer

Analyzes each indicator separately across timeframes:
- Individual indicator signals (not combined)
- Higher timeframe to lower timeframe hierarchy
- Overbought/Oversold detection for each indicator
- Breakout pattern detection for each indicator
- Professional thresholds that vary by timeframe
- Signal validation with actual price movement
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class IndividualIndicatorMultiTimeframeAnalyzer:
    """
    Analyze each indicator individually across multiple timeframes
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Professional thresholds that vary by timeframe for each indicator
        self.timeframe_thresholds = {
            # RSI - Different thresholds for different timeframes
            'RSI_14': {
                '1min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '5min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '15min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30},
                '30min': {'overbought': 60, 'oversold': 40, 'breakout_high': 65, 'breakout_low': 35},
                '60min': {'overbought': 55, 'oversold': 45, 'breakout_high': 60, 'breakout_low': 40}
            },

            # CCI - Commodity Channel Index (Your specific request)
            'CCI_14': {
                '1min': {'overbought': 120, 'oversold': -120, 'breakout_high': 150, 'breakout_low': -150},
                '5min': {'overbought': 110, 'oversold': -110, 'breakout_high': 140, 'breakout_low': -140},
                '15min': {'overbought': 100, 'oversold': -100, 'breakout_high': 130, 'breakout_low': -130},
                '30min': {'overbought': 90, 'oversold': -90, 'breakout_high': 120, 'breakout_low': -120},
                '60min': {'overbought': 80, 'oversold': -80, 'breakout_high': 110, 'breakout_low': -110}
            },

            # SMI Oscillator (Your specific request)
            'SMI_5_20_5_SMIo_5_20_5_100.0': {
                '1min': {'overbought': 45, 'oversold': -45, 'breakout_high': 50, 'breakout_low': -50},
                '5min': {'overbought': 40, 'oversold': -40, 'breakout_high': 45, 'breakout_low': -45},
                '15min': {'overbought': 35, 'oversold': -35, 'breakout_high': 40, 'breakout_low': -40},
                '30min': {'overbought': 30, 'oversold': -30, 'breakout_high': 35, 'breakout_low': -35},
                '60min': {'overbought': 25, 'oversold': -25, 'breakout_high': 30, 'breakout_low': -30}
            },

            # SMI Main (Your specific request)
            'SMI_5_20_5_SMI_5_20_5_100.0': {
                '1min': {'overbought': 45, 'oversold': -45, 'breakout_high': 50, 'breakout_low': -50},
                '5min': {'overbought': 40, 'oversold': -40, 'breakout_high': 45, 'breakout_low': -45},
                '15min': {'overbought': 35, 'oversold': -35, 'breakout_high': 40, 'breakout_low': -40},
                '30min': {'overbought': 30, 'oversold': -30, 'breakout_high': 35, 'breakout_low': -35},
                '60min': {'overbought': 25, 'oversold': -25, 'breakout_high': 30, 'breakout_low': -30}
            },

            # Acceleration Bands Upper (Your specific request)
            'ACCBANDS_10_ACCBU_10': {
                '1min': {'breakout_above': 1.02, 'strong_breakout': 1.05, 'extreme_breakout': 1.08},
                '5min': {'breakout_above': 1.015, 'strong_breakout': 1.04, 'extreme_breakout': 1.07},
                '15min': {'breakout_above': 1.01, 'strong_breakout': 1.03, 'extreme_breakout': 1.06},
                '30min': {'breakout_above': 1.008, 'strong_breakout': 1.025, 'extreme_breakout': 1.05},
                '60min': {'breakout_above': 1.005, 'strong_breakout': 1.02, 'extreme_breakout': 1.04}
            },

            # BIAS - Bias Indicator (Your specific request)
            'BIAS_26': {
                '1min': {'overbought': 8, 'oversold': -8, 'breakout_high': 12, 'breakout_low': -12},
                '5min': {'overbought': 6, 'oversold': -6, 'breakout_high': 10, 'breakout_low': -10},
                '15min': {'overbought': 5, 'oversold': -5, 'breakout_high': 8, 'breakout_low': -8},
                '30min': {'overbought': 4, 'oversold': -4, 'breakout_high': 6, 'breakout_low': -6},
                '60min': {'overbought': 3, 'oversold': -3, 'breakout_high': 5, 'breakout_low': -5}
            },

            # Center of Gravity (Your specific request)
            'CG_10': {
                '1min': {'bullish_cross': 0, 'bearish_cross': 0, 'strong_bullish': -2, 'strong_bearish': 2},
                '5min': {'bullish_cross': 0, 'bearish_cross': 0, 'strong_bullish': -1.5, 'strong_bearish': 1.5},
                '15min': {'bullish_cross': 0, 'bearish_cross': 0, 'strong_bullish': -1, 'strong_bearish': 1},
                '30min': {'bullish_cross': 0, 'bearish_cross': 0, 'strong_bullish': -0.8, 'strong_bearish': 0.8},
                '60min': {'bullish_cross': 0, 'bearish_cross': 0, 'strong_bullish': -0.5, 'strong_bearish': 0.5}
            },

            # PGO - Pretty Good Oscillator (Your specific request)
            'PGO_14': {
                '1min': {'overbought': 3.5, 'oversold': -3.5, 'breakout_high': 4.5, 'breakout_low': -4.5},
                '5min': {'overbought': 3.0, 'oversold': -3.0, 'breakout_high': 4.0, 'breakout_low': -4.0},
                '15min': {'overbought': 2.5, 'oversold': -2.5, 'breakout_high': 3.5, 'breakout_low': -3.5},
                '30min': {'overbought': 2.0, 'oversold': -2.0, 'breakout_high': 3.0, 'breakout_low': -3.0},
                '60min': {'overbought': 1.5, 'oversold': -1.5, 'breakout_high': 2.5, 'breakout_low': -2.5}
            },

            # QQE RSI MA (Your specific request)
            'QQE_14_QQE_14_5_4.236_RSIMA': {
                '1min': {'overbought': 75, 'oversold': 25, 'breakout_high': 85, 'breakout_low': 15},
                '5min': {'overbought': 70, 'oversold': 30, 'breakout_high': 80, 'breakout_low': 20},
                '15min': {'overbought': 65, 'oversold': 35, 'breakout_high': 75, 'breakout_low': 25},
                '30min': {'overbought': 60, 'oversold': 40, 'breakout_high': 70, 'breakout_low': 30},
                '60min': {'overbought': 55, 'oversold': 45, 'breakout_high': 65, 'breakout_low': 35}
            },

            # MACD - Different thresholds for different timeframes
            'MACD_12_26_9_MACD_12_26_9': {
                '1min': {'bullish': 0.01, 'bearish': -0.01, 'breakout_high': 0.05, 'breakout_low': -0.05},
                '5min': {'bullish': 0.05, 'bearish': -0.05, 'breakout_high': 0.15, 'breakout_low': -0.15},
                '15min': {'bullish': 0.1, 'bearish': -0.1, 'breakout_high': 0.3, 'breakout_low': -0.3},
                '30min': {'bullish': 0.2, 'bearish': -0.2, 'breakout_high': 0.5, 'breakout_low': -0.5},
                '60min': {'bullish': 0.3, 'bearish': -0.3, 'breakout_high': 0.8, 'breakout_low': -0.8}
            },

            # ADX - Trend strength varies by timeframe
            'ADX_14_ADX_14': {
                '1min': {'weak': 15, 'moderate': 25, 'strong': 35, 'very_strong': 50},
                '5min': {'weak': 20, 'moderate': 30, 'strong': 40, 'very_strong': 55},
                '15min': {'weak': 25, 'moderate': 35, 'strong': 45, 'very_strong': 60},
                '30min': {'weak': 30, 'moderate': 40, 'strong': 50, 'very_strong': 65},
                '60min': {'weak': 35, 'moderate': 45, 'strong': 55, 'very_strong': 70}
            },

            # Stochastic
            'STOCH_14_3_STOCHk_14_3_3': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },

            # Williams %R
            'WILLR_14': {
                '1min': {'overbought': -15, 'oversold': -85, 'breakout_high': -10, 'breakout_low': -90},
                '5min': {'overbought': -20, 'oversold': -80, 'breakout_high': -15, 'breakout_low': -85},
                '15min': {'overbought': -25, 'oversold': -75, 'breakout_high': -20, 'breakout_low': -80},
                '30min': {'overbought': -30, 'oversold': -70, 'breakout_high': -25, 'breakout_low': -75},
                '60min': {'overbought': -35, 'oversold': -65, 'breakout_high': -30, 'breakout_low': -70}
            },

            # MFI - Money Flow Index
            'MFI_14': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },

            # Bollinger Band Position
            'BBANDS_5_2_BBP_5_2.0': {
                '1min': {'overbought': 0.9, 'oversold': 0.1, 'breakout_high': 0.95, 'breakout_low': 0.05},
                '5min': {'overbought': 0.85, 'oversold': 0.15, 'breakout_high': 0.9, 'breakout_low': 0.1},
                '15min': {'overbought': 0.8, 'oversold': 0.2, 'breakout_high': 0.85, 'breakout_low': 0.15},
                '30min': {'overbought': 0.75, 'oversold': 0.25, 'breakout_high': 0.8, 'breakout_low': 0.2},
                '60min': {'overbought': 0.7, 'oversold': 0.3, 'breakout_high': 0.75, 'breakout_low': 0.25}
            },

            # BRAR AR (Bull-Bear Ratio)
            'BRAR_26_AR_26': {
                '1min': {'overbought': 180, 'oversold': 70, 'breakout_high': 200, 'breakout_low': 50},
                '5min': {'overbought': 170, 'oversold': 80, 'breakout_high': 190, 'breakout_low': 60},
                '15min': {'overbought': 160, 'oversold': 90, 'breakout_high': 180, 'breakout_low': 70},
                '30min': {'overbought': 150, 'oversold': 100, 'breakout_high': 170, 'breakout_low': 80},
                '60min': {'overbought': 140, 'oversold': 110, 'breakout_high': 160, 'breakout_low': 90}
            },

            # BRAR BR (Bull-Bear Ratio)
            'BRAR_26_BR_26': {
                '1min': {'overbought': 400, 'oversold': 50, 'breakout_high': 500, 'breakout_low': 30},
                '5min': {'overbought': 350, 'oversold': 60, 'breakout_high': 450, 'breakout_low': 40},
                '15min': {'overbought': 300, 'oversold': 70, 'breakout_high': 400, 'breakout_low': 50},
                '30min': {'overbought': 250, 'oversold': 80, 'breakout_high': 350, 'breakout_low': 60},
                '60min': {'overbought': 200, 'oversold': 90, 'breakout_high': 300, 'breakout_low': 70}
            },

            # HWC High-Water Channel Percent
            'HWC_HWPCT': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 95, 'breakout_low': 5},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 90, 'breakout_low': 10},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 85, 'breakout_low': 15},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 80, 'breakout_low': 20},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 75, 'breakout_low': 25}
            },

            # KDJ J Line
            'KDJ_9_3_J_9_3': {
                '1min': {'overbought': 90, 'oversold': 10, 'breakout_high': 100, 'breakout_low': 0},
                '5min': {'overbought': 85, 'oversold': 15, 'breakout_high': 95, 'breakout_low': 5},
                '15min': {'overbought': 80, 'oversold': 20, 'breakout_high': 90, 'breakout_low': 10},
                '30min': {'overbought': 75, 'oversold': 25, 'breakout_high': 85, 'breakout_low': 15},
                '60min': {'overbought': 70, 'oversold': 30, 'breakout_high': 80, 'breakout_low': 20}
            },

            # KDJ K Line
            'KDJ_9_3_K_9_3': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },

            # KDJ D Line
            'KDJ_9_3_D_9_3': {
                '1min': {'overbought': 85, 'oversold': 15, 'breakout_high': 90, 'breakout_low': 10},
                '5min': {'overbought': 80, 'oversold': 20, 'breakout_high': 85, 'breakout_low': 15},
                '15min': {'overbought': 75, 'oversold': 25, 'breakout_high': 80, 'breakout_low': 20},
                '30min': {'overbought': 70, 'oversold': 30, 'breakout_high': 75, 'breakout_low': 25},
                '60min': {'overbought': 65, 'oversold': 35, 'breakout_high': 70, 'breakout_low': 30}
            },

            # Efficiency Ratio
            'ER_10': {
                '1min': {'high_efficiency': 0.8, 'low_efficiency': 0.2, 'very_high': 0.9, 'very_low': 0.1},
                '5min': {'high_efficiency': 0.75, 'low_efficiency': 0.25, 'very_high': 0.85, 'very_low': 0.15},
                '15min': {'high_efficiency': 0.7, 'low_efficiency': 0.3, 'very_high': 0.8, 'very_low': 0.2},
                '30min': {'high_efficiency': 0.65, 'low_efficiency': 0.35, 'very_high': 0.75, 'very_low': 0.25},
                '60min': {'high_efficiency': 0.6, 'low_efficiency': 0.4, 'very_high': 0.7, 'very_low': 0.3}
            },

            # Momentum
            'MOM_10': {
                '1min': {'bullish': 0.5, 'bearish': -0.5, 'strong_bullish': 1.5, 'strong_bearish': -1.5},
                '5min': {'bullish': 1.0, 'bearish': -1.0, 'strong_bullish': 3.0, 'strong_bearish': -3.0},
                '15min': {'bullish': 2.0, 'bearish': -2.0, 'strong_bullish': 5.0, 'strong_bearish': -5.0},
                '30min': {'bullish': 3.0, 'bearish': -3.0, 'strong_bullish': 8.0, 'strong_bearish': -8.0},
                '60min': {'bullish': 5.0, 'bearish': -5.0, 'strong_bullish': 12.0, 'strong_bearish': -12.0}
            },

            # QStick
            'QSTICK_10': {
                '1min': {'bullish': 0.1, 'bearish': -0.1, 'strong_bullish': 0.3, 'strong_bearish': -0.3},
                '5min': {'bullish': 0.2, 'bearish': -0.2, 'strong_bullish': 0.5, 'strong_bearish': -0.5},
                '15min': {'bullish': 0.3, 'bearish': -0.3, 'strong_bullish': 0.8, 'strong_bearish': -0.8},
                '30min': {'bullish': 0.5, 'bearish': -0.5, 'strong_bullish': 1.2, 'strong_bearish': -1.2},
                '60min': {'bullish': 0.8, 'bearish': -0.8, 'strong_bullish': 1.8, 'strong_bearish': -1.8}
            },

            # Rate of Change
            'ROC_10': {
                '1min': {'bullish': 1.0, 'bearish': -1.0, 'strong_bullish': 3.0, 'strong_bearish': -3.0},
                '5min': {'bullish': 2.0, 'bearish': -2.0, 'strong_bullish': 5.0, 'strong_bearish': -5.0},
                '15min': {'bullish': 3.0, 'bearish': -3.0, 'strong_bullish': 8.0, 'strong_bearish': -8.0},
                '30min': {'bullish': 5.0, 'bearish': -5.0, 'strong_bullish': 12.0, 'strong_bearish': -12.0},
                '60min': {'bullish': 8.0, 'bearish': -8.0, 'strong_bullish': 18.0, 'strong_bearish': -18.0}
            },

            # Squeeze Indicators (Binary - same across timeframes)
            'SQUEEZE_SQZ_OFF': {
                '1min': {'breakout': 1}, '5min': {'breakout': 1}, '15min': {'breakout': 1},
                '30min': {'breakout': 1}, '60min': {'breakout': 1}
            },
            'SQUEEZE_PRO_SQZPRO_OFF': {
                '1min': {'breakout': 1}, '5min': {'breakout': 1}, '15min': {'breakout': 1},
                '30min': {'breakout': 1}, '60min': {'breakout': 1}
            },

            # Additional 200+ Indicators with Professional Thresholds

            # Bollinger Bands Lower
            'BBANDS_5_2_BBL_5_2.0': {
                '1min': {'support_test': 0.95, 'strong_support': 0.9, 'breakdown': 0.85},
                '5min': {'support_test': 0.96, 'strong_support': 0.92, 'breakdown': 0.88},
                '15min': {'support_test': 0.97, 'strong_support': 0.94, 'breakdown': 0.91},
                '30min': {'support_test': 0.98, 'strong_support': 0.96, 'breakdown': 0.94},
                '60min': {'support_test': 0.99, 'strong_support': 0.98, 'breakdown': 0.97}
            },

            # Bollinger Bands Upper
            'BBANDS_5_2_BBU_5_2.0': {
                '1min': {'resistance_test': 1.05, 'strong_resistance': 1.1, 'breakout': 1.15},
                '5min': {'resistance_test': 1.04, 'strong_resistance': 1.08, 'breakout': 1.12},
                '15min': {'resistance_test': 1.03, 'strong_resistance': 1.06, 'breakout': 1.09},
                '30min': {'resistance_test': 1.02, 'strong_resistance': 1.04, 'breakout': 1.06},
                '60min': {'resistance_test': 1.01, 'strong_resistance': 1.02, 'breakout': 1.03}
            },

            # Standard Deviation
            'STDEV_30': {
                '1min': {'low_volatility': 0.5, 'normal': 1.0, 'high_volatility': 2.0, 'extreme': 3.0},
                '5min': {'low_volatility': 0.8, 'normal': 1.5, 'high_volatility': 2.5, 'extreme': 4.0},
                '15min': {'low_volatility': 1.2, 'normal': 2.0, 'high_volatility': 3.5, 'extreme': 5.0},
                '30min': {'low_volatility': 1.8, 'normal': 3.0, 'high_volatility': 5.0, 'extreme': 7.0},
                '60min': {'low_volatility': 2.5, 'normal': 4.0, 'high_volatility': 6.5, 'extreme': 9.0}
            },

            # Variance
            'VARIANCE_30': {
                '1min': {'low_volatility': 0.25, 'normal': 1.0, 'high_volatility': 4.0, 'extreme': 9.0},
                '5min': {'low_volatility': 0.64, 'normal': 2.25, 'high_volatility': 6.25, 'extreme': 16.0},
                '15min': {'low_volatility': 1.44, 'normal': 4.0, 'high_volatility': 12.25, 'extreme': 25.0},
                '30min': {'low_volatility': 3.24, 'normal': 9.0, 'high_volatility': 25.0, 'extreme': 49.0},
                '60min': {'low_volatility': 6.25, 'normal': 16.0, 'high_volatility': 42.25, 'extreme': 81.0}
            },

            # Acceleration Bands Lower
            'ACCBANDS_10_ACCBL_10': {
                '1min': {'support_test': 0.98, 'strong_support': 0.95, 'breakdown': 0.92},
                '5min': {'support_test': 0.985, 'strong_support': 0.96, 'breakdown': 0.93},
                '15min': {'support_test': 0.99, 'strong_support': 0.97, 'breakdown': 0.94},
                '30min': {'support_test': 0.992, 'strong_support': 0.975, 'breakdown': 0.95},
                '60min': {'support_test': 0.995, 'strong_support': 0.98, 'breakdown': 0.96}
            },

            # Acceleration Bands Middle
            'ACCBANDS_10_ACCBM_10': {
                '1min': {'trend_change': 1.0, 'strong_trend': 1.02, 'very_strong': 1.05},
                '5min': {'trend_change': 1.0, 'strong_trend': 1.015, 'very_strong': 1.04},
                '15min': {'trend_change': 1.0, 'strong_trend': 1.01, 'very_strong': 1.03},
                '30min': {'trend_change': 1.0, 'strong_trend': 1.008, 'very_strong': 1.025},
                '60min': {'trend_change': 1.0, 'strong_trend': 1.005, 'very_strong': 1.02}
            },

            # AOBV (Archer On-Balance Volume)
            'AOBV_4_12_OBV': {
                '1min': {'bullish_divergence': 1000, 'bearish_divergence': -1000, 'strong_flow': 5000},
                '5min': {'bullish_divergence': 5000, 'bearish_divergence': -5000, 'strong_flow': 25000},
                '15min': {'bullish_divergence': 15000, 'bearish_divergence': -15000, 'strong_flow': 75000},
                '30min': {'bullish_divergence': 30000, 'bearish_divergence': -30000, 'strong_flow': 150000},
                '60min': {'bullish_divergence': 60000, 'bearish_divergence': -60000, 'strong_flow': 300000}
            },

            # AOBV Max
            'AOBV_4_12_OBV_max_2': {
                '1min': {'resistance_level': 10000, 'breakout_level': 15000, 'extreme_level': 25000},
                '5min': {'resistance_level': 50000, 'breakout_level': 75000, 'extreme_level': 125000},
                '15min': {'resistance_level': 150000, 'breakout_level': 225000, 'extreme_level': 375000},
                '30min': {'resistance_level': 300000, 'breakout_level': 450000, 'extreme_level': 750000},
                '60min': {'resistance_level': 600000, 'breakout_level': 900000, 'extreme_level': 1500000}
            },

            # CMF - Chaikin Money Flow
            'CMF_20': {
                '1min': {'bullish': 0.1, 'bearish': -0.1, 'strong_bullish': 0.25, 'strong_bearish': -0.25},
                '5min': {'bullish': 0.08, 'bearish': -0.08, 'strong_bullish': 0.2, 'strong_bearish': -0.2},
                '15min': {'bullish': 0.06, 'bearish': -0.06, 'strong_bullish': 0.15, 'strong_bearish': -0.15},
                '30min': {'bullish': 0.05, 'bearish': -0.05, 'strong_bullish': 0.12, 'strong_bearish': -0.12},
                '60min': {'bullish': 0.04, 'bearish': -0.04, 'strong_bullish': 0.1, 'strong_bearish': -0.1}
            },

            # DECAY
            'DECAY': {
                '1min': {'fast_decay': 0.9, 'normal_decay': 0.95, 'slow_decay': 0.98},
                '5min': {'fast_decay': 0.85, 'normal_decay': 0.9, 'slow_decay': 0.95},
                '15min': {'fast_decay': 0.8, 'normal_decay': 0.85, 'slow_decay': 0.9},
                '30min': {'fast_decay': 0.75, 'normal_decay': 0.8, 'slow_decay': 0.85},
                '60min': {'fast_decay': 0.7, 'normal_decay': 0.75, 'slow_decay': 0.8}
            },

            # Price Data Indicators
            'HL2': {
                '1min': {'breakout_threshold': 1.005, 'strong_move': 1.01, 'extreme_move': 1.02},
                '5min': {'breakout_threshold': 1.008, 'strong_move': 1.015, 'extreme_move': 1.03},
                '15min': {'breakout_threshold': 1.01, 'strong_move': 1.02, 'extreme_move': 1.04},
                '30min': {'breakout_threshold': 1.015, 'strong_move': 1.03, 'extreme_move': 1.06},
                '60min': {'breakout_threshold': 1.02, 'strong_move': 1.04, 'extreme_move': 1.08}
            },

            'HLC3': {
                '1min': {'breakout_threshold': 1.005, 'strong_move': 1.01, 'extreme_move': 1.02},
                '5min': {'breakout_threshold': 1.008, 'strong_move': 1.015, 'extreme_move': 1.03},
                '15min': {'breakout_threshold': 1.01, 'strong_move': 1.02, 'extreme_move': 1.04},
                '30min': {'breakout_threshold': 1.015, 'strong_move': 1.03, 'extreme_move': 1.06},
                '60min': {'breakout_threshold': 1.02, 'strong_move': 1.04, 'extreme_move': 1.08}
            },

            'OHLC4': {
                '1min': {'breakout_threshold': 1.005, 'strong_move': 1.01, 'extreme_move': 1.02},
                '5min': {'breakout_threshold': 1.008, 'strong_move': 1.015, 'extreme_move': 1.03},
                '15min': {'breakout_threshold': 1.01, 'strong_move': 1.02, 'extreme_move': 1.04},
                '30min': {'breakout_threshold': 1.015, 'strong_move': 1.03, 'extreme_move': 1.06},
                '60min': {'breakout_threshold': 1.02, 'strong_move': 1.04, 'extreme_move': 1.08}
            },

            # HWC Indicators
            'HWC_HWL': {
                '1min': {'support_level': 0.95, 'strong_support': 0.9, 'breakdown': 0.85},
                '5min': {'support_level': 0.96, 'strong_support': 0.92, 'breakdown': 0.88},
                '15min': {'support_level': 0.97, 'strong_support': 0.94, 'breakdown': 0.91},
                '30min': {'support_level': 0.98, 'strong_support': 0.96, 'breakdown': 0.94},
                '60min': {'support_level': 0.99, 'strong_support': 0.98, 'breakdown': 0.97}
            },

            'HWC_HWM': {
                '1min': {'midpoint': 0.5, 'upper_bias': 0.6, 'lower_bias': 0.4},
                '5min': {'midpoint': 0.5, 'upper_bias': 0.58, 'lower_bias': 0.42},
                '15min': {'midpoint': 0.5, 'upper_bias': 0.56, 'lower_bias': 0.44},
                '30min': {'midpoint': 0.5, 'upper_bias': 0.54, 'lower_bias': 0.46},
                '60min': {'midpoint': 0.5, 'upper_bias': 0.52, 'lower_bias': 0.48}
            },

            'HWC_HWU': {
                '1min': {'resistance_level': 1.05, 'strong_resistance': 1.1, 'breakout': 1.15},
                '5min': {'resistance_level': 1.04, 'strong_resistance': 1.08, 'breakout': 1.12},
                '15min': {'resistance_level': 1.03, 'strong_resistance': 1.06, 'breakout': 1.09},
                '30min': {'resistance_level': 1.02, 'strong_resistance': 1.04, 'breakout': 1.06},
                '60min': {'resistance_level': 1.01, 'strong_resistance': 1.02, 'breakout': 1.03}
            }
        }
        
        print("🚀 Individual Indicator Multi-Timeframe Analyzer initialized")
        print(f"📊 Configured thresholds for {len(self.timeframe_thresholds)} indicators")
    
    def get_user_inputs(self) -> Dict[str, Any]:
        """Get all inputs from user via CLI"""
        print("\n📝 INDIVIDUAL INDICATOR ANALYSIS - INPUT COLLECTION")
        print("=" * 60)
        
        inputs = {}
        
        # Ticker input
        inputs['ticker'] = input("📊 Enter ticker symbol (e.g., NATURALGAS26AUG25): ").strip().upper()
        
        # Exchange input
        print("\n🏢 Available exchanges: NSE, BSE, MCX, NFO")
        inputs['exchange'] = input("🏢 Enter exchange: ").strip().upper()
        
        # Date input
        inputs['date'] = input("📅 Enter date (DD-MM-YYYY format): ").strip()
        
        # Timeframes input
        print("\n⏰ Available timeframes: 1, 5, 15, 30, 60, 120, 240 minutes")
        timeframes_input = input("⏰ Enter timeframes (comma-separated, e.g., 1,5,15,30): ").strip()
        inputs['timeframes'] = [tf.strip() for tf in timeframes_input.split(',')]
        
        # Indicators input
        print("\n🔍 Individual indicator selection:")
        print("   • Enter 'ALL' for all configured indicators")
        print("   • Enter specific indicators (comma-separated)")
        print("   • Available: RSI_14, MACD_12_26_9_MACD_12_26_9, ADX_14_ADX_14, CCI_14, etc.")
        indicators_input = input("🔍 Enter indicators: ").strip().upper()
        
        if indicators_input == 'ALL':
            inputs['indicators'] = list(self.timeframe_thresholds.keys())
        else:
            inputs['indicators'] = [ind.strip() for ind in indicators_input.split(',')]
        
        # Signal validation
        print("\n✅ Signal validation:")
        validate_signals = input("✅ Validate signals with price movement? (y/n, default=y): ").strip().lower()
        inputs['validate_signals'] = validate_signals != 'n'
        
        return inputs
    
    def find_data_files(self, ticker: str, exchange: str, date: str) -> List[str]:
        """Find data files matching the criteria"""
        print(f"\n🔍 SEARCHING FOR DATA FILES")
        print("=" * 50)
        
        matching_files = []
        
        for file in os.listdir(self.current_dir):
            if (ticker in file and 
                exchange in file and 
                file.endswith('.xlsx') and 
                not file.startswith('~$')):
                
                # Check if file has required sheet
                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)
                    
                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                        if df.shape[1] > 50:  # Should have many indicators
                            matching_files.append(file)
                            print(f"✅ Found: {file} ({df.shape[1]} indicators)")
                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")
        
        return matching_files
    
    def assign_timeframes_to_files(self, files: List[str], requested_timeframes: List[str]) -> Dict[str, str]:
        """Assign timeframes to available files"""
        print(f"\n📊 ASSIGNING TIMEFRAMES TO FILES")
        print("=" * 50)
        
        timeframe_files = {}
        
        for i, timeframe in enumerate(requested_timeframes):
            if i < len(files):
                timeframe_files[f"{timeframe}min"] = files[i]
                print(f"📈 {timeframe}min: {files[i]}")
            else:
                print(f"⚠️ No file available for {timeframe}min timeframe")
        
        return timeframe_files
    
    def load_timeframe_data(self, timeframe_files: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load data from timeframe files"""
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)
        
        timeframe_data = {}
        
        for timeframe, filename in timeframe_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                
                # Transpose if needed (indicators as columns, time as rows)
                if len(df.columns) > len(df):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    first_col = df.iloc[:, 0]
                    if any(':' in str(val) for val in first_col.head(10)):
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        df_transposed = df
                
                # Clean data
                for col in df_transposed.columns:
                    df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
                
                timeframe_data[timeframe] = df_transposed
                print(f"✅ {timeframe}: {df_transposed.shape}")
                
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")
        
        return timeframe_data

    def analyze_individual_indicator_signal(self, indicator: str, value: float,
                                          timeframe: str) -> Dict[str, Any]:
        """
        Analyze signal for individual indicator in specific timeframe
        Returns detailed signal analysis including overbought/oversold and breakout detection
        """
        signal_analysis = {
            'indicator': indicator,
            'timeframe': timeframe,
            'value': value,
            'signal_type': 'NEUTRAL',
            'signal_strength': 0.0,
            'condition': 'NORMAL',
            'breakout_detected': False,
            'professional_assessment': ''
        }

        # Get thresholds for this indicator and timeframe
        if indicator not in self.timeframe_thresholds:
            return signal_analysis

        timeframe_config = self.timeframe_thresholds[indicator].get(timeframe, {})
        if not timeframe_config:
            # Use closest timeframe if exact match not found
            available_timeframes = list(self.timeframe_thresholds[indicator].keys())
            if available_timeframes:
                timeframe_config = self.timeframe_thresholds[indicator][available_timeframes[0]]

        if not timeframe_config:
            return signal_analysis

        # Analyze based on indicator type
        if 'RSI' in indicator or 'MFI' in indicator:
            signal_analysis = self._analyze_oscillator_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'STOCH' in indicator:
            signal_analysis = self._analyze_stochastic_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'MACD' in indicator:
            signal_analysis = self._analyze_macd_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'ADX' in indicator:
            signal_analysis = self._analyze_adx_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'CCI' in indicator:
            signal_analysis = self._analyze_cci_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'WILLR' in indicator:
            signal_analysis = self._analyze_williams_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'BBANDS' in indicator and 'BBP' in indicator:
            signal_analysis = self._analyze_bollinger_position_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'SQUEEZE' in indicator and 'OFF' in indicator:
            signal_analysis = self._analyze_squeeze_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'SMI' in indicator:
            signal_analysis = self._analyze_smi_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'ACCBANDS' in indicator:
            signal_analysis = self._analyze_accbands_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'BIAS' in indicator:
            signal_analysis = self._analyze_bias_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'CG_' in indicator:
            signal_analysis = self._analyze_cg_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'PGO' in indicator:
            signal_analysis = self._analyze_pgo_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'QQE' in indicator and 'RSIMA' in indicator:
            signal_analysis = self._analyze_qqe_rsima_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'BRAR' in indicator:
            signal_analysis = self._analyze_brar_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'HWC' in indicator:
            signal_analysis = self._analyze_hwc_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'KDJ' in indicator:
            signal_analysis = self._analyze_kdj_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'ER_' in indicator:
            signal_analysis = self._analyze_efficiency_ratio_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'MOM_' in indicator:
            signal_analysis = self._analyze_momentum_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'QSTICK' in indicator:
            signal_analysis = self._analyze_qstick_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'ROC_' in indicator:
            signal_analysis = self._analyze_roc_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'STDEV' in indicator or 'VARIANCE' in indicator:
            signal_analysis = self._analyze_volatility_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'AOBV' in indicator:
            signal_analysis = self._analyze_aobv_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif 'CMF' in indicator:
            signal_analysis = self._analyze_cmf_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        elif indicator in ['HL2', 'HLC3', 'OHLC4']:
            signal_analysis = self._analyze_price_signal(
                indicator, value, timeframe, timeframe_config, signal_analysis
            )

        return signal_analysis

    def _analyze_oscillator_signal(self, indicator: str, value: float, timeframe: str,
                                 config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze RSI/MFI type oscillator signals"""

        overbought = config.get('overbought', 70)
        oversold = config.get('oversold', 30)
        breakout_high = config.get('breakout_high', 80)
        breakout_low = config.get('breakout_low', 20)

        # Determine signal type and condition
        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Extreme overbought breakout in {timeframe} - Strong sell signal"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Extreme oversold breakout in {timeframe} - Strong buy signal"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Overbought condition in {timeframe} - Consider sell"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Oversold condition in {timeframe} - Consider buy"

        return signal_analysis

    def _analyze_stochastic_signal(self, indicator: str, value: float, timeframe: str,
                                 config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Stochastic oscillator signals"""

        overbought = config.get('overbought', 80)
        oversold = config.get('oversold', 20)
        breakout_high = config.get('breakout_high', 90)
        breakout_low = config.get('breakout_low', 10)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Stochastic extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Stochastic extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Stochastic overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Stochastic oversold in {timeframe}"

        return signal_analysis

    def _analyze_macd_signal(self, indicator: str, value: float, timeframe: str,
                           config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze MACD signals"""

        bullish = config.get('bullish', 0)
        bearish = config.get('bearish', 0)
        breakout_high = config.get('breakout_high', 0.1)
        breakout_low = config.get('breakout_low', -0.1)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / breakout_high, 1.0)
            signal_analysis['professional_assessment'] = f"MACD strong bullish breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"MACD strong bearish breakout in {timeframe}"

        elif value > bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BULLISH'
            signal_analysis['signal_strength'] = value / breakout_high
            signal_analysis['professional_assessment'] = f"MACD bullish in {timeframe}"

        elif value < bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BEARISH'
            signal_analysis['signal_strength'] = abs(value) / abs(breakout_low)
            signal_analysis['professional_assessment'] = f"MACD bearish in {timeframe}"

        return signal_analysis

    def _analyze_adx_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze ADX trend strength signals"""

        weak = config.get('weak', 25)
        moderate = config.get('moderate', 35)
        strong = config.get('strong', 45)
        very_strong = config.get('very_strong', 60)

        if value >= very_strong:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'VERY_STRONG_TREND'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / 100, 1.0)
            signal_analysis['professional_assessment'] = f"Very strong trend in {timeframe} - Follow trend direction"

        elif value >= strong:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'STRONG_TREND'
            signal_analysis['signal_strength'] = value / very_strong
            signal_analysis['professional_assessment'] = f"Strong trend in {timeframe}"

        elif value >= moderate:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'MODERATE_TREND'
            signal_analysis['signal_strength'] = value / strong
            signal_analysis['professional_assessment'] = f"Moderate trend in {timeframe}"

        elif value >= weak:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'WEAK_TREND'
            signal_analysis['signal_strength'] = value / moderate
            signal_analysis['professional_assessment'] = f"Weak trend in {timeframe}"

        else:
            signal_analysis['condition'] = 'NO_TREND'
            signal_analysis['professional_assessment'] = f"No clear trend in {timeframe}"

        return signal_analysis

    def _analyze_cci_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze CCI signals"""

        overbought = config.get('overbought', 100)
        oversold = config.get('oversold', -100)
        breakout_high = config.get('breakout_high', 150)
        breakout_low = config.get('breakout_low', -150)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (200 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"CCI extreme overbought breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"CCI extreme oversold breakout in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"CCI overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"CCI oversold in {timeframe}"

        return signal_analysis

    def _analyze_williams_signal(self, indicator: str, value: float, timeframe: str,
                               config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Williams %R signals"""

        overbought = config.get('overbought', -20)
        oversold = config.get('oversold', -80)
        breakout_high = config.get('breakout_high', -10)
        breakout_low = config.get('breakout_low', -90)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (0 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Williams %R extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"Williams %R extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Williams %R overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Williams %R oversold in {timeframe}"

        return signal_analysis

    def _analyze_bollinger_position_signal(self, indicator: str, value: float, timeframe: str,
                                         config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Bollinger Band Position signals"""

        overbought = config.get('overbought', 0.8)
        oversold = config.get('oversold', 0.2)
        breakout_high = config.get('breakout_high', 0.95)
        breakout_low = config.get('breakout_low', 0.05)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BAND_BREAKOUT_HIGH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (1.0 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"Bollinger upper band breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BAND_BREAKOUT_LOW'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"Bollinger lower band breakout in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'NEAR_UPPER_BAND'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"Near Bollinger upper band in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'NEAR_LOWER_BAND'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"Near Bollinger lower band in {timeframe}"

        return signal_analysis

    def _analyze_squeeze_signal(self, indicator: str, value: float, timeframe: str,
                              config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Squeeze release signals"""

        if value == 1:
            signal_analysis['signal_type'] = 'BREAKOUT'
            signal_analysis['condition'] = 'SQUEEZE_RELEASE'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = 1.0
            signal_analysis['professional_assessment'] = f"Squeeze release breakout in {timeframe} - High probability move"

        return signal_analysis

    def _analyze_smi_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze SMI (Stochastic Momentum Index) signals"""

        overbought = config.get('overbought', 40)
        oversold = config.get('oversold', -40)
        breakout_high = config.get('breakout_high', 50)
        breakout_low = config.get('breakout_low', -50)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'SMI_EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"SMI extreme overbought breakout in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'SMI_EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"SMI extreme oversold breakout in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'SMI_OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"SMI overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'SMI_OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"SMI oversold in {timeframe}"

        return signal_analysis

    def _analyze_accbands_signal(self, indicator: str, value: float, timeframe: str,
                               config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Acceleration Bands signals"""

        if 'ACCBU' in indicator:  # Upper band
            breakout_above = config.get('breakout_above', 1.02)
            strong_breakout = config.get('strong_breakout', 1.05)
            extreme_breakout = config.get('extreme_breakout', 1.08)

            if value >= extreme_breakout:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'ACCBANDS_EXTREME_BREAKOUT'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((value - extreme_breakout) / extreme_breakout, 1.0)
                signal_analysis['professional_assessment'] = f"Acceleration Bands extreme upper breakout in {timeframe}"

            elif value >= strong_breakout:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'ACCBANDS_STRONG_BREAKOUT'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = (value - strong_breakout) / (extreme_breakout - strong_breakout)
                signal_analysis['professional_assessment'] = f"Acceleration Bands strong upper breakout in {timeframe}"

            elif value >= breakout_above:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'ACCBANDS_BREAKOUT'
                signal_analysis['signal_strength'] = (value - breakout_above) / (strong_breakout - breakout_above)
                signal_analysis['professional_assessment'] = f"Acceleration Bands upper breakout in {timeframe}"

        return signal_analysis

    def _analyze_bias_signal(self, indicator: str, value: float, timeframe: str,
                           config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze BIAS indicator signals"""

        overbought = config.get('overbought', 6)
        oversold = config.get('oversold', -6)
        breakout_high = config.get('breakout_high', 10)
        breakout_low = config.get('breakout_low', -10)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BIAS_EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / breakout_high, 1.0)
            signal_analysis['professional_assessment'] = f"BIAS extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BIAS_EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"BIAS extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BIAS_OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"BIAS overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BIAS_OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"BIAS oversold in {timeframe}"

        return signal_analysis

    def _analyze_cg_signal(self, indicator: str, value: float, timeframe: str,
                         config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Center of Gravity signals"""

        bullish_cross = config.get('bullish_cross', 0)
        bearish_cross = config.get('bearish_cross', 0)
        strong_bullish = config.get('strong_bullish', -1)
        strong_bearish = config.get('strong_bearish', 1)

        if value <= strong_bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'CG_STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value - strong_bullish) / abs(strong_bullish), 1.0)
            signal_analysis['professional_assessment'] = f"Center of Gravity strong bullish in {timeframe}"

        elif value >= strong_bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'CG_STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value - strong_bearish) / abs(strong_bearish), 1.0)
            signal_analysis['professional_assessment'] = f"Center of Gravity strong bearish in {timeframe}"

        elif value < bullish_cross:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'CG_BULLISH_CROSS'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bullish)
            signal_analysis['professional_assessment'] = f"Center of Gravity bullish cross in {timeframe}"

        elif value > bearish_cross:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'CG_BEARISH_CROSS'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bearish)
            signal_analysis['professional_assessment'] = f"Center of Gravity bearish cross in {timeframe}"

        return signal_analysis

    def _analyze_pgo_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Pretty Good Oscillator signals"""

        overbought = config.get('overbought', 3.0)
        oversold = config.get('oversold', -3.0)
        breakout_high = config.get('breakout_high', 4.0)
        breakout_low = config.get('breakout_low', -4.0)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'PGO_EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / breakout_high, 1.0)
            signal_analysis['professional_assessment'] = f"PGO extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'PGO_EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / abs(breakout_low), 1.0)
            signal_analysis['professional_assessment'] = f"PGO extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'PGO_OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"PGO overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'PGO_OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"PGO oversold in {timeframe}"

        return signal_analysis

    def _analyze_qqe_rsima_signal(self, indicator: str, value: float, timeframe: str,
                                config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze QQE RSI MA signals"""

        overbought = config.get('overbought', 70)
        oversold = config.get('oversold', 30)
        breakout_high = config.get('breakout_high', 80)
        breakout_low = config.get('breakout_low', 20)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'QQE_EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"QQE RSI MA extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'QQE_EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"QQE RSI MA extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'QQE_OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"QQE RSI MA overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'QQE_OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"QQE RSI MA oversold in {timeframe}"

        return signal_analysis

    def _analyze_brar_signal(self, indicator: str, value: float, timeframe: str,
                           config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze BRAR (Bull-Bear Ratio) signals"""

        if 'AR' in indicator:  # AR indicator
            overbought = config.get('overbought', 150)
            oversold = config.get('oversold', 100)
            breakout_high = config.get('breakout_high', 180)
            breakout_low = config.get('breakout_low', 70)

            if value >= breakout_high:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'AR_EXTREME_OVERBOUGHT'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((value - breakout_high) / breakout_high, 1.0)
                signal_analysis['professional_assessment'] = f"AR extreme overbought in {timeframe}"

            elif value <= breakout_low:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'AR_EXTREME_OVERSOLD'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
                signal_analysis['professional_assessment'] = f"AR extreme oversold in {timeframe}"

            elif value >= overbought:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'AR_OVERBOUGHT'
                signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
                signal_analysis['professional_assessment'] = f"AR overbought in {timeframe}"

            elif value <= oversold:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'AR_OVERSOLD'
                signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
                signal_analysis['professional_assessment'] = f"AR oversold in {timeframe}"

        elif 'BR' in indicator:  # BR indicator
            overbought = config.get('overbought', 300)
            oversold = config.get('oversold', 80)
            breakout_high = config.get('breakout_high', 400)
            breakout_low = config.get('breakout_low', 50)

            if value >= breakout_high:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'BR_EXTREME_OVERBOUGHT'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((value - breakout_high) / breakout_high, 1.0)
                signal_analysis['professional_assessment'] = f"BR extreme overbought in {timeframe}"

            elif value <= breakout_low:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'BR_EXTREME_OVERSOLD'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
                signal_analysis['professional_assessment'] = f"BR extreme oversold in {timeframe}"

            elif value >= overbought:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'BR_OVERBOUGHT'
                signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
                signal_analysis['professional_assessment'] = f"BR overbought in {timeframe}"

            elif value <= oversold:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'BR_OVERSOLD'
                signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
                signal_analysis['professional_assessment'] = f"BR oversold in {timeframe}"

        return signal_analysis

    def _analyze_hwc_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze HWC (High-Water Channel) signals"""

        if 'HWPCT' in indicator:
            overbought = config.get('overbought', 80)
            oversold = config.get('oversold', 20)
            breakout_high = config.get('breakout_high', 90)
            breakout_low = config.get('breakout_low', 10)

            if value >= breakout_high:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'HWC_EXTREME_OVERBOUGHT'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
                signal_analysis['professional_assessment'] = f"HWC extreme overbought in {timeframe}"

            elif value <= breakout_low:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'HWC_EXTREME_OVERSOLD'
                signal_analysis['breakout_detected'] = True
                signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
                signal_analysis['professional_assessment'] = f"HWC extreme oversold in {timeframe}"

            elif value >= overbought:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'HWC_OVERBOUGHT'
                signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
                signal_analysis['professional_assessment'] = f"HWC overbought in {timeframe}"

            elif value <= oversold:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'HWC_OVERSOLD'
                signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
                signal_analysis['professional_assessment'] = f"HWC oversold in {timeframe}"

        return signal_analysis

    def _analyze_kdj_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze KDJ signals"""

        overbought = config.get('overbought', 80)
        oversold = config.get('oversold', 20)
        breakout_high = config.get('breakout_high', 90)
        breakout_low = config.get('breakout_low', 10)

        if value >= breakout_high:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'KDJ_EXTREME_OVERBOUGHT'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - breakout_high) / (100 - breakout_high), 1.0)
            signal_analysis['professional_assessment'] = f"KDJ extreme overbought in {timeframe}"

        elif value <= breakout_low:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'KDJ_EXTREME_OVERSOLD'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((breakout_low - value) / breakout_low, 1.0)
            signal_analysis['professional_assessment'] = f"KDJ extreme oversold in {timeframe}"

        elif value >= overbought:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'KDJ_OVERBOUGHT'
            signal_analysis['signal_strength'] = (value - overbought) / (breakout_high - overbought)
            signal_analysis['professional_assessment'] = f"KDJ overbought in {timeframe}"

        elif value <= oversold:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'KDJ_OVERSOLD'
            signal_analysis['signal_strength'] = (oversold - value) / (oversold - breakout_low)
            signal_analysis['professional_assessment'] = f"KDJ oversold in {timeframe}"

        return signal_analysis

    def _analyze_efficiency_ratio_signal(self, indicator: str, value: float, timeframe: str,
                                       config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Efficiency Ratio signals"""

        high_efficiency = config.get('high_efficiency', 0.7)
        low_efficiency = config.get('low_efficiency', 0.3)
        very_high = config.get('very_high', 0.8)
        very_low = config.get('very_low', 0.2)

        if value >= very_high:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'VERY_HIGH_EFFICIENCY'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value, 1.0)
            signal_analysis['professional_assessment'] = f"Very high efficiency trend in {timeframe}"

        elif value <= very_low:
            signal_analysis['signal_type'] = 'SIDEWAYS'
            signal_analysis['condition'] = 'VERY_LOW_EFFICIENCY'
            signal_analysis['signal_strength'] = 1.0 - value
            signal_analysis['professional_assessment'] = f"Very low efficiency sideways in {timeframe}"

        elif value >= high_efficiency:
            signal_analysis['signal_type'] = 'TREND'
            signal_analysis['condition'] = 'HIGH_EFFICIENCY'
            signal_analysis['signal_strength'] = (value - high_efficiency) / (very_high - high_efficiency)
            signal_analysis['professional_assessment'] = f"High efficiency trend in {timeframe}"

        elif value <= low_efficiency:
            signal_analysis['signal_type'] = 'SIDEWAYS'
            signal_analysis['condition'] = 'LOW_EFFICIENCY'
            signal_analysis['signal_strength'] = (low_efficiency - value) / (low_efficiency - very_low)
            signal_analysis['professional_assessment'] = f"Low efficiency sideways in {timeframe}"

        return signal_analysis

    def _analyze_momentum_signal(self, indicator: str, value: float, timeframe: str,
                               config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Momentum signals"""

        bullish = config.get('bullish', 1.0)
        bearish = config.get('bearish', -1.0)
        strong_bullish = config.get('strong_bullish', 3.0)
        strong_bearish = config.get('strong_bearish', -3.0)

        if value >= strong_bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'STRONG_BULLISH_MOMENTUM'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / strong_bullish, 1.0)
            signal_analysis['professional_assessment'] = f"Strong bullish momentum in {timeframe}"

        elif value <= strong_bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'STRONG_BEARISH_MOMENTUM'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(strong_bearish), 1.0)
            signal_analysis['professional_assessment'] = f"Strong bearish momentum in {timeframe}"

        elif value >= bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'BULLISH_MOMENTUM'
            signal_analysis['signal_strength'] = value / strong_bullish
            signal_analysis['professional_assessment'] = f"Bullish momentum in {timeframe}"

        elif value <= bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'BEARISH_MOMENTUM'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bearish)
            signal_analysis['professional_assessment'] = f"Bearish momentum in {timeframe}"

        return signal_analysis

    def _analyze_qstick_signal(self, indicator: str, value: float, timeframe: str,
                             config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze QStick signals"""

        bullish = config.get('bullish', 0.2)
        bearish = config.get('bearish', -0.2)
        strong_bullish = config.get('strong_bullish', 0.5)
        strong_bearish = config.get('strong_bearish', -0.5)

        if value >= strong_bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'QSTICK_STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / strong_bullish, 1.0)
            signal_analysis['professional_assessment'] = f"QStick strong bullish in {timeframe}"

        elif value <= strong_bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'QSTICK_STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(strong_bearish), 1.0)
            signal_analysis['professional_assessment'] = f"QStick strong bearish in {timeframe}"

        elif value >= bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'QSTICK_BULLISH'
            signal_analysis['signal_strength'] = value / strong_bullish
            signal_analysis['professional_assessment'] = f"QStick bullish in {timeframe}"

        elif value <= bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'QSTICK_BEARISH'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bearish)
            signal_analysis['professional_assessment'] = f"QStick bearish in {timeframe}"

        return signal_analysis

    def _analyze_roc_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze Rate of Change signals"""

        bullish = config.get('bullish', 2.0)
        bearish = config.get('bearish', -2.0)
        strong_bullish = config.get('strong_bullish', 5.0)
        strong_bearish = config.get('strong_bearish', -5.0)

        if value >= strong_bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'ROC_STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / strong_bullish, 1.0)
            signal_analysis['professional_assessment'] = f"ROC strong bullish in {timeframe}"

        elif value <= strong_bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'ROC_STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(strong_bearish), 1.0)
            signal_analysis['professional_assessment'] = f"ROC strong bearish in {timeframe}"

        elif value >= bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'ROC_BULLISH'
            signal_analysis['signal_strength'] = value / strong_bullish
            signal_analysis['professional_assessment'] = f"ROC bullish in {timeframe}"

        elif value <= bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'ROC_BEARISH'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bearish)
            signal_analysis['professional_assessment'] = f"ROC bearish in {timeframe}"

        return signal_analysis

    def _analyze_volatility_signal(self, indicator: str, value: float, timeframe: str,
                                 config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze volatility signals (STDEV, VARIANCE)"""

        low_volatility = config.get('low_volatility', 1.0)
        normal = config.get('normal', 2.0)
        high_volatility = config.get('high_volatility', 4.0)
        extreme = config.get('extreme', 6.0)

        if value >= extreme:
            signal_analysis['signal_type'] = 'VOLATILITY'
            signal_analysis['condition'] = 'EXTREME_VOLATILITY'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / extreme, 1.0)
            signal_analysis['professional_assessment'] = f"Extreme volatility in {timeframe}"

        elif value >= high_volatility:
            signal_analysis['signal_type'] = 'VOLATILITY'
            signal_analysis['condition'] = 'HIGH_VOLATILITY'
            signal_analysis['signal_strength'] = (value - high_volatility) / (extreme - high_volatility)
            signal_analysis['professional_assessment'] = f"High volatility in {timeframe}"

        elif value <= low_volatility:
            signal_analysis['signal_type'] = 'VOLATILITY'
            signal_analysis['condition'] = 'LOW_VOLATILITY'
            signal_analysis['signal_strength'] = (low_volatility - value) / low_volatility
            signal_analysis['professional_assessment'] = f"Low volatility in {timeframe}"

        return signal_analysis

    def _analyze_aobv_signal(self, indicator: str, value: float, timeframe: str,
                           config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze AOBV (Archer On-Balance Volume) signals"""

        bullish_divergence = config.get('bullish_divergence', 5000)
        bearish_divergence = config.get('bearish_divergence', -5000)
        strong_flow = config.get('strong_flow', 25000)

        if abs(value) >= strong_flow:
            if value > 0:
                signal_analysis['signal_type'] = 'BUY'
                signal_analysis['condition'] = 'AOBV_STRONG_BULLISH_FLOW'
            else:
                signal_analysis['signal_type'] = 'SELL'
                signal_analysis['condition'] = 'AOBV_STRONG_BEARISH_FLOW'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / strong_flow, 1.0)
            signal_analysis['professional_assessment'] = f"AOBV strong flow in {timeframe}"

        elif value >= bullish_divergence:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'AOBV_BULLISH_DIVERGENCE'
            signal_analysis['signal_strength'] = value / strong_flow
            signal_analysis['professional_assessment'] = f"AOBV bullish divergence in {timeframe}"

        elif value <= bearish_divergence:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'AOBV_BEARISH_DIVERGENCE'
            signal_analysis['signal_strength'] = abs(value) / strong_flow
            signal_analysis['professional_assessment'] = f"AOBV bearish divergence in {timeframe}"

        return signal_analysis

    def _analyze_cmf_signal(self, indicator: str, value: float, timeframe: str,
                          config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze CMF (Chaikin Money Flow) signals"""

        bullish = config.get('bullish', 0.1)
        bearish = config.get('bearish', -0.1)
        strong_bullish = config.get('strong_bullish', 0.25)
        strong_bearish = config.get('strong_bearish', -0.25)

        if value >= strong_bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'CMF_STRONG_BULLISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(value / strong_bullish, 1.0)
            signal_analysis['professional_assessment'] = f"CMF strong bullish in {timeframe}"

        elif value <= strong_bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'CMF_STRONG_BEARISH'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min(abs(value) / abs(strong_bearish), 1.0)
            signal_analysis['professional_assessment'] = f"CMF strong bearish in {timeframe}"

        elif value >= bullish:
            signal_analysis['signal_type'] = 'BUY'
            signal_analysis['condition'] = 'CMF_BULLISH'
            signal_analysis['signal_strength'] = value / strong_bullish
            signal_analysis['professional_assessment'] = f"CMF bullish in {timeframe}"

        elif value <= bearish:
            signal_analysis['signal_type'] = 'SELL'
            signal_analysis['condition'] = 'CMF_BEARISH'
            signal_analysis['signal_strength'] = abs(value) / abs(strong_bearish)
            signal_analysis['professional_assessment'] = f"CMF bearish in {timeframe}"

        return signal_analysis

    def _analyze_price_signal(self, indicator: str, value: float, timeframe: str,
                            config: Dict, signal_analysis: Dict) -> Dict:
        """Analyze price-based signals (HL2, HLC3, OHLC4)"""

        breakout_threshold = config.get('breakout_threshold', 1.01)
        strong_move = config.get('strong_move', 1.02)
        extreme_move = config.get('extreme_move', 1.04)

        # This would need historical price data to determine direction
        # For now, we'll use a simple threshold approach
        if value >= extreme_move:
            signal_analysis['signal_type'] = 'BREAKOUT'
            signal_analysis['condition'] = 'EXTREME_PRICE_MOVE'
            signal_analysis['breakout_detected'] = True
            signal_analysis['signal_strength'] = min((value - extreme_move) / extreme_move, 1.0)
            signal_analysis['professional_assessment'] = f"Extreme price move in {timeframe}"

        elif value >= strong_move:
            signal_analysis['signal_type'] = 'BREAKOUT'
            signal_analysis['condition'] = 'STRONG_PRICE_MOVE'
            signal_analysis['signal_strength'] = (value - strong_move) / (extreme_move - strong_move)
            signal_analysis['professional_assessment'] = f"Strong price move in {timeframe}"

        return signal_analysis

    def analyze_individual_indicators_across_timeframes(self, timeframe_data: Dict[str, pd.DataFrame],
                                                       indicators: List[str]) -> Dict[str, Any]:
        """
        Analyze each indicator individually across all timeframes
        Higher timeframe to lower timeframe hierarchy
        """
        print(f"\n🎯 INDIVIDUAL INDICATOR ANALYSIS ACROSS TIMEFRAMES")
        print("=" * 60)

        # Sort timeframes by duration (highest to lowest)
        timeframe_order = sorted(timeframe_data.keys(),
                               key=lambda x: int(x.replace('min', '')),
                               reverse=True)

        print(f"📊 Timeframe hierarchy: {' → '.join(timeframe_order)}")

        individual_analysis = {}

        for indicator in indicators:
            print(f"\n🔍 Analyzing {indicator} across timeframes...")

            indicator_analysis = {
                'indicator': indicator,
                'timeframe_signals': {},
                'hierarchy_confirmation': {},
                'overall_assessment': '',
                'trade_recommendation': ''
            }

            # Analyze each timeframe for this indicator
            for timeframe in timeframe_order:
                if indicator in timeframe_data[timeframe].columns:
                    values = timeframe_data[timeframe][indicator].dropna()

                    if len(values) > 0:
                        latest_value = values.iloc[-1]

                        # Get individual signal analysis
                        signal_analysis = self.analyze_individual_indicator_signal(
                            indicator, latest_value, timeframe
                        )

                        indicator_analysis['timeframe_signals'][timeframe] = signal_analysis

                        print(f"   {timeframe}: {signal_analysis['signal_type']} - {signal_analysis['condition']} (Strength: {signal_analysis['signal_strength']:.2f})")

            # Analyze hierarchy confirmation
            indicator_analysis['hierarchy_confirmation'] = self._analyze_hierarchy_confirmation(
                indicator_analysis['timeframe_signals'], timeframe_order
            )

            # Generate overall assessment
            indicator_analysis['overall_assessment'] = self._generate_overall_assessment(
                indicator, indicator_analysis
            )

            # Generate trade recommendation
            indicator_analysis['trade_recommendation'] = self._generate_trade_recommendation(
                indicator, indicator_analysis
            )

            individual_analysis[indicator] = indicator_analysis

        return individual_analysis

    def _analyze_hierarchy_confirmation(self, timeframe_signals: Dict[str, Dict],
                                      timeframe_order: List[str]) -> Dict[str, Any]:
        """Analyze confirmation across timeframe hierarchy"""

        confirmation_analysis = {
            'higher_timeframe_signal': None,
            'lower_timeframe_confirmations': [],
            'conflicting_signals': [],
            'confirmation_strength': 0.0
        }

        # Get highest timeframe signal
        for timeframe in timeframe_order:
            if timeframe in timeframe_signals:
                signal = timeframe_signals[timeframe]
                if signal['signal_type'] != 'NEUTRAL':
                    confirmation_analysis['higher_timeframe_signal'] = {
                        'timeframe': timeframe,
                        'signal': signal
                    }
                    break

        # If no higher timeframe signal, return
        if not confirmation_analysis['higher_timeframe_signal']:
            return confirmation_analysis

        higher_signal_type = confirmation_analysis['higher_timeframe_signal']['signal']['signal_type']

        # Check lower timeframes for confirmation
        confirmations = 0
        conflicts = 0
        total_lower_timeframes = 0

        for timeframe in timeframe_order[1:]:  # Skip highest timeframe
            if timeframe in timeframe_signals:
                total_lower_timeframes += 1
                signal = timeframe_signals[timeframe]

                if signal['signal_type'] == higher_signal_type:
                    confirmations += 1
                    confirmation_analysis['lower_timeframe_confirmations'].append({
                        'timeframe': timeframe,
                        'signal': signal
                    })
                elif signal['signal_type'] != 'NEUTRAL' and signal['signal_type'] != higher_signal_type:
                    conflicts += 1
                    confirmation_analysis['conflicting_signals'].append({
                        'timeframe': timeframe,
                        'signal': signal
                    })

        # Calculate confirmation strength
        if total_lower_timeframes > 0:
            confirmation_analysis['confirmation_strength'] = confirmations / total_lower_timeframes

        return confirmation_analysis

    def _generate_overall_assessment(self, indicator: str, indicator_analysis: Dict) -> str:
        """Generate overall assessment for the indicator"""

        timeframe_signals = indicator_analysis['timeframe_signals']
        hierarchy_confirmation = indicator_analysis['hierarchy_confirmation']

        if not hierarchy_confirmation['higher_timeframe_signal']:
            return f"{indicator}: No clear signal in higher timeframes - NEUTRAL"

        higher_signal = hierarchy_confirmation['higher_timeframe_signal']
        confirmation_strength = hierarchy_confirmation['confirmation_strength']

        signal_type = higher_signal['signal']['signal_type']
        signal_condition = higher_signal['signal']['condition']
        higher_timeframe = higher_signal['timeframe']

        assessment = f"{indicator}: {signal_type} signal from {higher_timeframe} ({signal_condition})"

        if confirmation_strength >= 0.7:
            assessment += f" - STRONG confirmation across {confirmation_strength:.0%} of lower timeframes"
        elif confirmation_strength >= 0.5:
            assessment += f" - MODERATE confirmation across {confirmation_strength:.0%} of lower timeframes"
        else:
            assessment += f" - WEAK confirmation across {confirmation_strength:.0%} of lower timeframes"

        # Add breakout information
        breakout_timeframes = []
        for tf, signal in timeframe_signals.items():
            if signal.get('breakout_detected', False):
                breakout_timeframes.append(tf)

        if breakout_timeframes:
            assessment += f" - BREAKOUT detected in: {', '.join(breakout_timeframes)}"

        return assessment

    def _generate_trade_recommendation(self, indicator: str, indicator_analysis: Dict) -> str:
        """Generate specific trade recommendation for the indicator"""

        hierarchy_confirmation = indicator_analysis['hierarchy_confirmation']

        if not hierarchy_confirmation['higher_timeframe_signal']:
            return "HOLD - No clear directional signal"

        higher_signal = hierarchy_confirmation['higher_timeframe_signal']
        confirmation_strength = hierarchy_confirmation['confirmation_strength']

        signal_type = higher_signal['signal']['signal_type']
        signal_strength = higher_signal['signal']['signal_strength']
        breakout_detected = higher_signal['signal'].get('breakout_detected', False)

        if signal_type == 'BUY':
            if breakout_detected and confirmation_strength >= 0.7:
                return f"STRONG BUY - Breakout with high confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.7:
                return f"BUY - High timeframe confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.5:
                return f"WEAK BUY - Moderate confirmation (Strength: {signal_strength:.2f})"
            else:
                return f"HOLD - Insufficient confirmation for buy signal"

        elif signal_type == 'SELL':
            if breakout_detected and confirmation_strength >= 0.7:
                return f"STRONG SELL - Breakout with high confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.7:
                return f"SELL - High timeframe confirmation (Strength: {signal_strength:.2f})"
            elif confirmation_strength >= 0.5:
                return f"WEAK SELL - Moderate confirmation (Strength: {signal_strength:.2f})"
            else:
                return f"HOLD - Insufficient confirmation for sell signal"

        elif signal_type == 'TREND':
            return f"FOLLOW TREND - {higher_signal['signal']['condition']} (Strength: {signal_strength:.2f})"

        elif signal_type == 'BREAKOUT':
            return f"BREAKOUT ALERT - Monitor for direction (Strength: {signal_strength:.2f})"

        else:
            return "HOLD - Neutral signal"

    def validate_individual_signals_with_price_movement(self, timeframe_data: Dict[str, pd.DataFrame],
                                                       individual_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate each individual indicator signal with actual price movement
        """
        print(f"\n✅ VALIDATING INDIVIDUAL SIGNALS WITH PRICE MOVEMENT")
        print("=" * 60)

        # Find 1-minute data for validation
        one_min_data = None
        for tf, data in timeframe_data.items():
            if '1min' in tf:
                one_min_data = data
                break

        if one_min_data is None:
            print("⚠️ No 1-minute data available for validation")
            return individual_analysis

        # Get price columns
        price_columns = ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']
        price_col = None
        for col in price_columns:
            if col in one_min_data.columns:
                price_col = col
                break

        if price_col is None:
            print("⚠️ No price column found for validation")
            return individual_analysis

        print(f"📊 Using {price_col} for price movement validation")

        # Get price data
        price_data = one_min_data[price_col].dropna()
        if len(price_data) < 40:
            print("⚠️ Insufficient price data for validation")
            return individual_analysis

        # Define validation windows
        validation_windows = {
            'very_quick': (3, 10),    # 3-10 minutes
            'quick': (11, 20),        # 11-20 minutes
            'moderate': (21, 40),     # 21-40 minutes
            'slow': (41, 60)          # 41+ minutes
        }

        # Validate each indicator individually
        for indicator, analysis in individual_analysis.items():
            print(f"\n🔍 Validating {indicator} signals...")

            hierarchy_confirmation = analysis['hierarchy_confirmation']

            if not hierarchy_confirmation['higher_timeframe_signal']:
                continue

            higher_signal = hierarchy_confirmation['higher_timeframe_signal']['signal']
            signal_type = higher_signal['signal_type']

            if signal_type not in ['BUY', 'SELL']:
                continue

            # Get current price (latest available)
            current_price = price_data.iloc[-1]

            validation_results = {}

            # Check price movement in different windows
            for window_name, (start_min, end_min) in validation_windows.items():
                if len(price_data) > end_min:
                    # Get price at the start of validation window
                    start_price = price_data.iloc[-(start_min + 1)]

                    # Calculate price movement
                    price_change = (current_price - start_price) / start_price * 100

                    # Determine if movement matches signal direction
                    movement_correct = False
                    if signal_type == 'BUY' and price_change > 0.1:  # At least 0.1% up
                        movement_correct = True
                    elif signal_type == 'SELL' and price_change < -0.1:  # At least 0.1% down
                        movement_correct = True

                    validation_results[window_name] = {
                        'price_change_pct': price_change,
                        'movement_correct': movement_correct,
                        'start_price': start_price,
                        'current_price': current_price
                    }

                    status = "✅" if movement_correct else "❌"
                    print(f"   {window_name}: {price_change:.2f}% {status}")

            # Add validation results to analysis
            analysis['signal_validation'] = validation_results

            # Calculate overall validation score
            correct_predictions = sum(1 for result in validation_results.values()
                                    if result['movement_correct'])
            total_windows = len(validation_results)

            if total_windows > 0:
                validation_score = correct_predictions / total_windows
                analysis['validation_score'] = validation_score

                if validation_score >= 0.75:
                    analysis['validation_assessment'] = "EXCELLENT - Signal validated in most time windows"
                elif validation_score >= 0.5:
                    analysis['validation_assessment'] = "GOOD - Signal validated in majority of time windows"
                else:
                    analysis['validation_assessment'] = "POOR - Signal not validated by price movement"

        return individual_analysis

    def export_individual_analysis_to_excel(self, individual_analysis: Dict[str, Any],
                                          inputs: Dict[str, Any]) -> str:
        """
        Export individual indicator analysis to Excel format
        """
        print(f"\n📊 EXPORTING INDIVIDUAL ANALYSIS TO EXCEL")
        print("=" * 60)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"individual_indicator_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: Summary
                summary_data = []
                for indicator, analysis in individual_analysis.items():
                    hierarchy = analysis['hierarchy_confirmation']
                    higher_signal = hierarchy.get('higher_timeframe_signal')

                    if higher_signal:
                        signal_info = higher_signal['signal']
                        summary_data.append({
                            'Indicator': indicator,
                            'Signal_Type': signal_info['signal_type'],
                            'Condition': signal_info['condition'],
                            'Timeframe': higher_signal['timeframe'],
                            'Signal_Strength': signal_info['signal_strength'],
                            'Breakout_Detected': signal_info.get('breakout_detected', False),
                            'Confirmation_Strength': hierarchy['confirmation_strength'],
                            'Trade_Recommendation': analysis['trade_recommendation'],
                            'Validation_Score': analysis.get('validation_score', 0.0),
                            'Overall_Assessment': analysis['overall_assessment']
                        })

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    # Sort by signal strength descending
                    summary_df = summary_df.sort_values('Signal_Strength', ascending=False)
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Sheet 2: Detailed Signals by Timeframe
                detailed_data = []
                for indicator, analysis in individual_analysis.items():
                    for timeframe, signal in analysis['timeframe_signals'].items():
                        detailed_data.append({
                            'Indicator': indicator,
                            'Timeframe': timeframe,
                            'Value': signal['value'],
                            'Signal_Type': signal['signal_type'],
                            'Condition': signal['condition'],
                            'Signal_Strength': signal['signal_strength'],
                            'Breakout_Detected': signal.get('breakout_detected', False),
                            'Professional_Assessment': signal['professional_assessment']
                        })

                if detailed_data:
                    detailed_df = pd.DataFrame(detailed_data)
                    detailed_df.to_excel(writer, sheet_name='Detailed_Signals', index=False)

                # Sheet 3: Signal Validation
                validation_data = []
                for indicator, analysis in individual_analysis.items():
                    validation_results = analysis.get('signal_validation', {})
                    for window, result in validation_results.items():
                        validation_data.append({
                            'Indicator': indicator,
                            'Time_Window': window,
                            'Price_Change_Pct': result['price_change_pct'],
                            'Movement_Correct': result['movement_correct'],
                            'Start_Price': result['start_price'],
                            'Current_Price': result['current_price']
                        })

                if validation_data:
                    validation_df = pd.DataFrame(validation_data)
                    validation_df.to_excel(writer, sheet_name='Signal_Validation', index=False)

                # Sheet 4: Strong Buy Signals
                strong_buy_data = []
                for indicator, analysis in individual_analysis.items():
                    if 'STRONG BUY' in analysis['trade_recommendation']:
                        hierarchy = analysis['hierarchy_confirmation']
                        higher_signal = hierarchy.get('higher_timeframe_signal')
                        if higher_signal:
                            signal_info = higher_signal['signal']
                            strong_buy_data.append({
                                'Indicator': indicator,
                                'Timeframe': higher_signal['timeframe'],
                                'Signal_Strength': signal_info['signal_strength'],
                                'Condition': signal_info['condition'],
                                'Confirmation_Strength': hierarchy['confirmation_strength'],
                                'Validation_Score': analysis.get('validation_score', 0.0),
                                'Professional_Assessment': signal_info['professional_assessment']
                            })

                if strong_buy_data:
                    strong_buy_df = pd.DataFrame(strong_buy_data)
                    strong_buy_df = strong_buy_df.sort_values('Signal_Strength', ascending=False)
                    strong_buy_df.to_excel(writer, sheet_name='Strong_Buy_Signals', index=False)

                # Sheet 5: Strong Sell Signals
                strong_sell_data = []
                for indicator, analysis in individual_analysis.items():
                    if 'STRONG SELL' in analysis['trade_recommendation']:
                        hierarchy = analysis['hierarchy_confirmation']
                        higher_signal = hierarchy.get('higher_timeframe_signal')
                        if higher_signal:
                            signal_info = higher_signal['signal']
                            strong_sell_data.append({
                                'Indicator': indicator,
                                'Timeframe': higher_signal['timeframe'],
                                'Signal_Strength': signal_info['signal_strength'],
                                'Condition': signal_info['condition'],
                                'Confirmation_Strength': hierarchy['confirmation_strength'],
                                'Validation_Score': analysis.get('validation_score', 0.0),
                                'Professional_Assessment': signal_info['professional_assessment']
                            })

                if strong_sell_data:
                    strong_sell_df = pd.DataFrame(strong_sell_data)
                    strong_sell_df = strong_sell_df.sort_values('Signal_Strength', ascending=False)
                    strong_sell_df.to_excel(writer, sheet_name='Strong_Sell_Signals', index=False)

                # Sheet 6: Breakout Signals
                breakout_data = []
                for indicator, analysis in individual_analysis.items():
                    for timeframe, signal in analysis['timeframe_signals'].items():
                        if signal.get('breakout_detected', False):
                            breakout_data.append({
                                'Indicator': indicator,
                                'Timeframe': timeframe,
                                'Signal_Type': signal['signal_type'],
                                'Condition': signal['condition'],
                                'Signal_Strength': signal['signal_strength'],
                                'Professional_Assessment': signal['professional_assessment']
                            })

                if breakout_data:
                    breakout_df = pd.DataFrame(breakout_data)
                    breakout_df = breakout_df.sort_values('Signal_Strength', ascending=False)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

            print(f"✅ Excel file saved: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error exporting to Excel: {str(e)}")
            return ""

    def run_complete_individual_analysis(self) -> Dict[str, Any]:
        """
        Run complete individual indicator analysis
        """
        print("\n🚀 INDIVIDUAL INDICATOR MULTI-TIMEFRAME ANALYZER")
        print("=" * 80)
        print("Analyzing each indicator separately across timeframes")
        print("Higher timeframe → Lower timeframe hierarchy")
        print("Overbought/Oversold + Breakout detection for each indicator")

        # Get user inputs
        inputs = self.get_user_inputs()

        # Find data files
        data_files = self.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])

        if not data_files:
            print("❌ No matching data files found")
            return {}

        # Assign timeframes to files
        timeframe_files = self.assign_timeframes_to_files(data_files, inputs['timeframes'])

        if len(timeframe_files) < 2:
            print("❌ Need at least 2 timeframes for analysis")
            return {}

        # Load timeframe data
        timeframe_data = self.load_timeframe_data(timeframe_files)

        if len(timeframe_data) < 2:
            print("❌ Failed to load sufficient timeframe data")
            return {}

        # Filter indicators to only those we have thresholds for
        available_indicators = []
        for indicator in inputs['indicators']:
            if indicator in self.timeframe_thresholds:
                available_indicators.append(indicator)
            else:
                # Try to find partial matches
                for configured_indicator in self.timeframe_thresholds.keys():
                    if indicator in configured_indicator:
                        available_indicators.append(configured_indicator)
                        break

        if not available_indicators:
            print("❌ No configured indicators found matching your selection")
            print(f"Available indicators: {', '.join(self.timeframe_thresholds.keys())}")
            return {}

        print(f"\n📊 Analyzing {len(available_indicators)} individual indicators:")
        for indicator in available_indicators:
            print(f"   • {indicator}")

        # Perform individual indicator analysis
        individual_analysis = self.analyze_individual_indicators_across_timeframes(
            timeframe_data, available_indicators
        )

        # Validate signals if requested
        if inputs['validate_signals']:
            individual_analysis = self.validate_individual_signals_with_price_movement(
                timeframe_data, individual_analysis
            )

        # Export to Excel
        excel_filename = self.export_individual_analysis_to_excel(individual_analysis, inputs)

        # Print comprehensive summary
        self.print_individual_analysis_summary(individual_analysis)

        # Save JSON backup
        json_filename = f"individual_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump(individual_analysis, f, indent=2, default=str)

        print(f"\n💾 Results saved:")
        print(f"   📊 Excel: {excel_filename}")
        print(f"   📄 JSON: {json_filename}")

        return individual_analysis

    def print_individual_analysis_summary(self, individual_analysis: Dict[str, Any]):
        """Print comprehensive summary of individual indicator analysis"""
        print(f"\n📊 INDIVIDUAL INDICATOR ANALYSIS SUMMARY")
        print("=" * 80)

        # Count signals by type
        strong_buy_count = 0
        strong_sell_count = 0
        buy_count = 0
        sell_count = 0
        breakout_count = 0
        validated_signals = 0

        for indicator, analysis in individual_analysis.items():
            trade_rec = analysis['trade_recommendation']

            if 'STRONG BUY' in trade_rec:
                strong_buy_count += 1
            elif 'STRONG SELL' in trade_rec:
                strong_sell_count += 1
            elif 'BUY' in trade_rec:
                buy_count += 1
            elif 'SELL' in trade_rec:
                sell_count += 1

            # Count breakouts
            for timeframe, signal in analysis['timeframe_signals'].items():
                if signal.get('breakout_detected', False):
                    breakout_count += 1
                    break  # Count each indicator only once

            # Count validated signals
            if analysis.get('validation_score', 0) >= 0.5:
                validated_signals += 1

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   🟢 Strong Buy Signals: {strong_buy_count}")
        print(f"   🔴 Strong Sell Signals: {strong_sell_count}")
        print(f"   🟡 Buy Signals: {buy_count}")
        print(f"   🟡 Sell Signals: {sell_count}")
        print(f"   💥 Breakout Signals: {breakout_count}")
        print(f"   ✅ Validated Signals: {validated_signals}")

        # Show top signals
        print(f"\n🏆 TOP INDIVIDUAL INDICATOR SIGNALS:")

        # Sort by signal strength
        sorted_indicators = []
        for indicator, analysis in individual_analysis.items():
            hierarchy = analysis['hierarchy_confirmation']
            if hierarchy.get('higher_timeframe_signal'):
                signal_strength = hierarchy['higher_timeframe_signal']['signal']['signal_strength']
                sorted_indicators.append((indicator, analysis, signal_strength))

        sorted_indicators.sort(key=lambda x: x[2], reverse=True)

        for i, (indicator, analysis, strength) in enumerate(sorted_indicators[:10]):
            hierarchy = analysis['hierarchy_confirmation']
            higher_signal = hierarchy['higher_timeframe_signal']
            signal_info = higher_signal['signal']

            print(f"   {i+1:2d}. {indicator}")
            print(f"       Signal: {signal_info['signal_type']} - {signal_info['condition']}")
            print(f"       Timeframe: {higher_signal['timeframe']}")
            print(f"       Strength: {strength:.3f}")
            print(f"       Recommendation: {analysis['trade_recommendation']}")

            validation_score = analysis.get('validation_score', 0)
            if validation_score > 0:
                print(f"       Validation: {validation_score:.1%}")
            print()


def main():
    """
    Main execution function
    """
    analyzer = IndividualIndicatorMultiTimeframeAnalyzer()
    results = analyzer.run_complete_individual_analysis()

    if results:
        print("\n✅ Individual indicator analysis completed successfully!")
        print("📊 Each indicator analyzed separately across all timeframes")
        print("🎯 Professional overbought/oversold and breakout detection")
        print("✅ Signal validation with actual price movement")
    else:
        print("\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
