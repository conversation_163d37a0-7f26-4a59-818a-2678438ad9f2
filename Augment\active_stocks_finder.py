"""
Active Stocks Finder - Find stocks with good movement patterns

This script analyzes stocks to find those with consistent good movement throughout the day.
It uses candle analysis and technical indicators to identify stocks that are actively moving
rather than just having sudden spikes.

Features:
- Analyzes candle height and movement consistency
- Uses technical indicators to measure volatility and momentum
- Filters stocks based on movement criteria
- Validates against known good stocks (A-G letters)
- Exports results to Excel for analysis

Usage:
python active_stocks_finder.py --date 30-06-2025 --mode live
python active_stocks_finder.py --date 30-06-2025 --mode historical
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import argparse
import json
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import required modules
from shared_api_manager import get_api
from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('active_stocks_finder.log')
    ]
)
logger = logging.getLogger(__name__)

# Complete stock list A-Z
ALL_STOCKS = [
    '360ONE', 'AARTIIND', 'ABB', 'ABCAPITAL', 'ABFRL', 'ACC', 'ADANIENSOL', 'ADANIENT', 
    'ADANIGREEN', 'ADANIPORTS', 'ALKEM', 'AMBUJACEM', 'ANGELONE', 'APOLLOHOSP', 'APOLLOTYRE', 
    'ASHOKLEY', 'ASIANPAINT', 'ASTRAL', 'ATUL', 'AUBANK', 'AUROPHARMA', 'AXISBANK', 
    'BAJAJ-AUTO', 'BAJAJFINSV', 'BAJFINANCE', 'BALKRISIND', 'BALRAMCHIN', 'BANDHANBNK', 
    'BANKBARODA', 'BATAINDIA', 'BEL', 'BERGEPAINT', 'BHARATFORG', 'BHARTIARTL', 'BHEL', 
    'BIOCON', 'BOSCHLTD', 'BPCL', 'BRITANNIA', 'BSOFT', 'CANFINHOME', 'CANBK', 'CDSL', 
    'CENTURYTEX', 'CESC', 'CGPOWER', 'CHAMBLFERT', 'CHOLAFIN', 'CIPLA', 'COALINDIA', 
    'COFORGE', 'COLPAL', 'CONCOR', 'CUB', 'CUMMINSIND', 'DABUR', 'DEEPAKNTR', 'DELTACORP', 
    'DIVISLAB', 'DIXON', 'DLF', 'DRREDDY', 'EICHERMOT', 'ESCORTS', 'EXIDEIND', 'FEDERALBNK', 
    'GAIL', 'GLENMARK', 'GMRINFRA', 'GNFC', 'GODREJCP', 'GODREJPROP', 'GRANULES', 'GRASIM', 
    'GUJGASLTD', 'HAL', 'HAVELLS', 'HCLTECH', 'HDFCAMC', 'HDFCBANK', 'HDFCLIFE', 'HEROMOTOCO', 
    'HINDALCO', 'HINDCOPPER', 'HINDPETRO', 'HINDUNILVR', 'HONAUT', 'ICICIBANK', 'ICICIGI', 
    'ICICIPRULI', 'IDEA', 'IDFC', 'IDFCFIRSTB', 'IEX', 'IGL', 'INDHOTEL', 'INDIAMART', 
    'INDIGO', 'INDUSINDBK', 'INDUSTOWER', 'INFY', 'IOC', 'IPCALAB', 'IRCTC', 'ITC', 
    'JINDALSTEL', 'JKCEMENT', 'JSWSTEEL', 'JUBLFOOD', 'KPITTECH', 'KOTAKBANK', 'L&TFH', 
    'LALPATHLAB', 'LAURUSLABS', 'LICHSGFIN', 'LT', 'LTI', 'LTTS', 'LUPIN', 'M&M', 'M&MFIN', 
    'MANAPPURAM', 'MARICO', 'MARUTI', 'MCDOWELL-N', 'MCX', 'METROPOLIS', 'MFSL', 'MGL', 
    'MOTHERSON', 'MPHASIS', 'MRF', 'MUTHOOTFIN', 'NATIONALUM', 'NAUKRI', 'NAVINFLUOR', 
    'NESTLEIND', 'NMDC', 'NTPC', 'OBEROIRLTY', 'OFSS', 'ONGC', 'PAGEIND', 'PEL', 'PERSISTENT', 
    'PETRONET', 'PFC', 'PIDILITIND', 'PIIND', 'PNB', 'POLYCAB', 'POWERGRID', 'PVRINOX', 
    'RAMCOCEM', 'RBLBANK', 'RECLTD', 'RELIANCE', 'SAIL', 'SBICARD', 'SBILIFE', 'SBIN', 
    'SHREECEM', 'SIEMENS', 'SRF', 'SUNPHARMA', 'SUNTV', 'SYNGENE', 'TATACHEM', 'TATACOMM', 
    'TATACONSUM', 'TATAMOTORS', 'TATAPOWER', 'TATASTEEL', 'TCS', 'TECHM', 'TITAN', 'TORNTPHARM', 
    'TRENT', 'TVSMOTOR', 'UBL', 'ULTRACEMCO', 'UPL', 'VEDL', 'VOLTAS', 'WIPRO', 'ZEEL', 'ZYDUSLIFE'
]

# Known good moving stocks (A-G) for validation
KNOWN_GOOD_STOCKS = ['ACC', 'BEL', 'BHARTIARTL', 'BHEL', 'BSOFT', 'CAMS', 'CANBK', 'COLPAL', 'CONCOR', 'DALBHARAT', 'DLF', 'EICHERMOT']

# Stocks starting with A-G for testing
A_TO_G_STOCKS = [stock for stock in ALL_STOCKS if stock[0] in 'ABCDEFG']

class ActiveStocksFinder:
    """
    Find stocks with good movement patterns using candle analysis and technical indicators
    """
    
    def __init__(self, date: str, exchange: str = 'NSE'):
        self.date = date
        self.exchange = exchange
        self.api = get_api()
        self.results = {}
        
    def get_token_info(self, ticker: str) -> Optional[Dict]:
        """Get token information for a ticker"""
        try:
            ret = self.api.searchscrip(exchange=self.exchange, searchtext=ticker)
            
            if ret and ret.get('stat') == 'Ok' and ret.get('values'):
                # For equity exchanges, prefer EQ instruments
                if self.exchange in ['NSE', 'BSE']:
                    for item in ret['values']:
                        if item.get('instname') == 'EQ':
                            return {
                                'token': item.get('token'),
                                'tsym': item.get('tsym'),
                                'symname': item.get('symname'),
                                'cname': item.get('cname'),
                                'exchange': self.exchange
                            }
                
                # Return first match if no EQ found
                first_match = ret['values'][0]
                return {
                    'token': first_match.get('token'),
                    'tsym': first_match.get('tsym'),
                    'symname': first_match.get('symname'),
                    'cname': first_match.get('cname'),
                    'exchange': self.exchange
                }
            else:
                logger.warning(f"No results found for ticker: {ticker}")
                return None
                
        except Exception as e:
            logger.error(f"Error searching for ticker {ticker}: {str(e)}")
            return None
    
    def fetch_stock_data(self, token_info: Dict, start_time: str = "09:15", end_time: str = "15:30") -> Optional[pd.DataFrame]:
        """Fetch stock data for the given date and time range"""
        try:
            start_timestamp, end_timestamp = get_start_end_timestamps(self.date, start_time, end_time)
            
            data = self.api.get_time_price_series(
                exchange=self.exchange,
                token=token_info['token'],
                starttime=start_timestamp,
                endtime=end_timestamp,
                interval=1
            )
            
            if data is None:
                logger.warning(f"No data received for {token_info['tsym']}")
                return None
            
            data_df = live_data(data)
            
            if data_df is None or data_df.empty:
                logger.warning(f"No valid data after processing for {token_info['tsym']}")
                return None
            
            return data_df.sort_values(by='time')
            
        except Exception as e:
            logger.error(f"Error fetching data for {token_info['tsym']}: {str(e)}")
            return None

    def analyze_candle_movement(self, data_df: pd.DataFrame) -> Dict:
        """
        Analyze candle movement patterns to identify good moving stocks
        """
        try:
            if data_df.empty or len(data_df) < 10:
                return {'error': 'Insufficient data'}

            # Calculate candle metrics
            data_df['candle_height'] = data_df['High'] - data_df['Low']
            data_df['body_size'] = abs(data_df['Close'] - data_df['Open'])
            data_df['upper_wick'] = data_df['High'] - data_df[['Open', 'Close']].max(axis=1)
            data_df['lower_wick'] = data_df[['Open', 'Close']].min(axis=1) - data_df['Low']
            data_df['price_change'] = data_df['Close'].pct_change()

            # Movement analysis metrics
            avg_candle_height = data_df['candle_height'].mean()
            avg_body_size = data_df['body_size'].mean()
            avg_price = data_df['Close'].mean()

            # Calculate relative metrics (as percentage of price)
            height_to_price_ratio = (avg_candle_height / avg_price) * 100
            body_to_price_ratio = (avg_body_size / avg_price) * 100

            # Consistency metrics
            height_std = data_df['candle_height'].std()
            height_consistency = 1 - (height_std / avg_candle_height) if avg_candle_height > 0 else 0

            # Movement frequency (how many candles show good movement)
            good_movement_threshold = avg_candle_height * 0.7  # 70% of average height
            good_movement_candles = (data_df['candle_height'] >= good_movement_threshold).sum()
            movement_frequency = good_movement_candles / len(data_df)

            # Volatility analysis
            price_volatility = data_df['price_change'].std() * 100  # As percentage

            # Trend consistency (not too choppy)
            consecutive_moves = 0
            max_consecutive = 0
            for i in range(1, len(data_df)):
                if (data_df.iloc[i]['Close'] > data_df.iloc[i-1]['Close']) == (data_df.iloc[i-1]['Close'] > data_df.iloc[i-2]['Close'] if i > 1 else True):
                    consecutive_moves += 1
                    max_consecutive = max(max_consecutive, consecutive_moves)
                else:
                    consecutive_moves = 0

            trend_consistency = max_consecutive / len(data_df) if len(data_df) > 0 else 0

            return {
                'total_candles': len(data_df),
                'avg_candle_height': avg_candle_height,
                'avg_body_size': avg_body_size,
                'height_to_price_ratio': height_to_price_ratio,
                'body_to_price_ratio': body_to_price_ratio,
                'height_consistency': height_consistency,
                'movement_frequency': movement_frequency,
                'price_volatility': price_volatility,
                'trend_consistency': trend_consistency,
                'good_movement_candles': good_movement_candles,
                'avg_price': avg_price
            }

        except Exception as e:
            logger.error(f"Error in candle movement analysis: {str(e)}")
            return {'error': str(e)}

    def calculate_technical_indicators(self, data_df: pd.DataFrame) -> Dict:
        """
        Calculate key technical indicators for movement analysis
        """
        try:
            import pandas_ta as ta

            # Ensure we have enough data
            if len(data_df) < 20:
                return {'error': 'Insufficient data for technical indicators'}

            # Calculate key indicators
            indicators = {}

            # ATR (Average True Range) - measures volatility
            atr = ta.atr(data_df['High'], data_df['Low'], data_df['Close'], length=14)
            indicators['atr_avg'] = atr.mean() if not atr.empty else 0
            indicators['atr_current'] = atr.iloc[-1] if not atr.empty else 0

            # RSI (Relative Strength Index)
            rsi = ta.rsi(data_df['Close'], length=14)
            indicators['rsi_avg'] = rsi.mean() if not rsi.empty else 50
            indicators['rsi_current'] = rsi.iloc[-1] if not rsi.empty else 50

            # Bollinger Bands
            bb = ta.bbands(data_df['Close'], length=20)
            if bb is not None and not bb.empty:
                indicators['bb_width_avg'] = ((bb['BBU_20_2.0'] - bb['BBL_20_2.0']) / bb['BBM_20_2.0']).mean()
                indicators['bb_position'] = (data_df['Close'].iloc[-1] - bb['BBL_20_2.0'].iloc[-1]) / (bb['BBU_20_2.0'].iloc[-1] - bb['BBL_20_2.0'].iloc[-1])
            else:
                indicators['bb_width_avg'] = 0
                indicators['bb_position'] = 0.5

            # MACD
            macd = ta.macd(data_df['Close'])
            if macd is not None and not macd.empty:
                indicators['macd_signal_strength'] = abs(macd['MACD_12_26_9'].iloc[-1]) if 'MACD_12_26_9' in macd.columns else 0
            else:
                indicators['macd_signal_strength'] = 0

            # Volume analysis (if available)
            if 'Volume' in data_df.columns:
                indicators['avg_volume'] = data_df['Volume'].mean()
                indicators['volume_consistency'] = 1 - (data_df['Volume'].std() / data_df['Volume'].mean()) if data_df['Volume'].mean() > 0 else 0
            else:
                indicators['avg_volume'] = 0
                indicators['volume_consistency'] = 0

            return indicators

        except Exception as e:
            logger.error(f"Error calculating technical indicators: {str(e)}")
            return {'error': str(e)}

    def calculate_movement_score(self, candle_analysis: Dict, technical_indicators: Dict) -> Dict:
        """
        Calculate a comprehensive movement score for the stock
        """
        try:
            if 'error' in candle_analysis or 'error' in technical_indicators:
                return {'movement_score': 0, 'error': 'Analysis error'}

            score = 0
            score_breakdown = {}

            # 1. Candle Height Score (25 points) - Good consistent height
            height_ratio = candle_analysis.get('height_to_price_ratio', 0)
            if height_ratio >= 2.0:  # 2% or more of price
                height_score = 25
            elif height_ratio >= 1.5:
                height_score = 20
            elif height_ratio >= 1.0:
                height_score = 15
            elif height_ratio >= 0.5:
                height_score = 10
            else:
                height_score = 0

            score += height_score
            score_breakdown['height_score'] = height_score

            # 2. Movement Frequency Score (20 points) - Consistent movement
            movement_freq = candle_analysis.get('movement_frequency', 0)
            if movement_freq >= 0.8:  # 80% of candles show good movement
                freq_score = 20
            elif movement_freq >= 0.6:
                freq_score = 15
            elif movement_freq >= 0.4:
                freq_score = 10
            else:
                freq_score = 0

            score += freq_score
            score_breakdown['frequency_score'] = freq_score

            # 3. Consistency Score (20 points) - Not too erratic
            consistency = candle_analysis.get('height_consistency', 0)
            if consistency >= 0.7:
                consistency_score = 20
            elif consistency >= 0.5:
                consistency_score = 15
            elif consistency >= 0.3:
                consistency_score = 10
            else:
                consistency_score = 0

            score += consistency_score
            score_breakdown['consistency_score'] = consistency_score

            # 4. ATR Score (15 points) - Good volatility but not excessive
            atr_ratio = technical_indicators.get('atr_avg', 0) / candle_analysis.get('avg_price', 1) * 100
            if 1.0 <= atr_ratio <= 3.0:  # Sweet spot for good movement
                atr_score = 15
            elif 0.5 <= atr_ratio <= 4.0:
                atr_score = 10
            elif atr_ratio > 0:
                atr_score = 5
            else:
                atr_score = 0

            score += atr_score
            score_breakdown['atr_score'] = atr_score

            # 5. Bollinger Band Width Score (10 points) - Good expansion
            bb_width = technical_indicators.get('bb_width_avg', 0)
            if bb_width >= 0.08:  # Good expansion
                bb_score = 10
            elif bb_width >= 0.05:
                bb_score = 7
            elif bb_width >= 0.03:
                bb_score = 5
            else:
                bb_score = 0

            score += bb_score
            score_breakdown['bb_score'] = bb_score

            # 6. Volume Consistency Score (10 points) - If volume data available
            volume_consistency = technical_indicators.get('volume_consistency', 0)
            if volume_consistency >= 0.5:
                volume_score = 10
            elif volume_consistency >= 0.3:
                volume_score = 7
            else:
                volume_score = 5  # Default score if no volume data

            score += volume_score
            score_breakdown['volume_score'] = volume_score

            return {
                'movement_score': score,
                'score_breakdown': score_breakdown,
                'grade': self._get_grade(score)
            }

        except Exception as e:
            logger.error(f"Error calculating movement score: {str(e)}")
            return {'movement_score': 0, 'error': str(e)}

    def _get_grade(self, score: float) -> str:
        """Convert score to grade"""
        if score >= 85:
            return 'A+'
        elif score >= 75:
            return 'A'
        elif score >= 65:
            return 'B+'
        elif score >= 55:
            return 'B'
        elif score >= 45:
            return 'C+'
        elif score >= 35:
            return 'C'
        else:
            return 'D'

    def analyze_stock(self, ticker: str, start_time: str = "09:15", end_time: str = "15:30") -> Dict:
        """
        Analyze a single stock for movement patterns
        """
        try:
            logger.info(f"Analyzing {ticker}...")

            # Get token info
            token_info = self.get_token_info(ticker)
            if not token_info:
                return {'ticker': ticker, 'error': 'Token not found'}

            # Fetch data
            data_df = self.fetch_stock_data(token_info, start_time, end_time)
            if data_df is None:
                return {'ticker': ticker, 'error': 'No data available'}

            # Analyze candle movement
            candle_analysis = self.analyze_candle_movement(data_df)
            if 'error' in candle_analysis:
                return {'ticker': ticker, 'error': candle_analysis['error']}

            # Calculate technical indicators
            technical_indicators = self.calculate_technical_indicators(data_df)
            if 'error' in technical_indicators:
                return {'ticker': ticker, 'error': technical_indicators['error']}

            # Calculate movement score
            movement_score = self.calculate_movement_score(candle_analysis, technical_indicators)

            # Compile results
            result = {
                'ticker': ticker,
                'token_info': token_info,
                'candle_analysis': candle_analysis,
                'technical_indicators': technical_indicators,
                'movement_score': movement_score,
                'data_points': len(data_df)
            }

            return result

        except Exception as e:
            logger.error(f"Error analyzing {ticker}: {str(e)}")
            return {'ticker': ticker, 'error': str(e)}

    def analyze_multiple_stocks(self, stock_list: List[str], start_time: str = "09:15", end_time: str = "15:30") -> Dict:
        """
        Analyze multiple stocks and return results
        """
        results = {}
        total_stocks = len(stock_list)

        logger.info(f"Starting analysis of {total_stocks} stocks...")

        for i, ticker in enumerate(stock_list, 1):
            logger.info(f"Progress: {i}/{total_stocks} - Analyzing {ticker}")

            result = self.analyze_stock(ticker, start_time, end_time)
            results[ticker] = result

            # Show progress every 10 stocks
            if i % 10 == 0:
                logger.info(f"Completed {i}/{total_stocks} stocks")

        logger.info(f"Analysis complete for {total_stocks} stocks")
        return results

    def filter_good_stocks(self, results: Dict, min_score: float = 60.0) -> List[Dict]:
        """
        Filter stocks that meet the good movement criteria
        """
        good_stocks = []

        for ticker, result in results.items():
            if 'error' in result:
                continue

            movement_score = result.get('movement_score', {}).get('movement_score', 0)
            if movement_score >= min_score:
                good_stocks.append(result)

        # Sort by movement score (highest first)
        good_stocks.sort(key=lambda x: x.get('movement_score', {}).get('movement_score', 0), reverse=True)

        return good_stocks

    def export_to_excel(self, results: Dict, filename: str = None) -> str:
        """
        Export analysis results to Excel
        """
        try:
            if filename is None:
                filename = f"active_stocks_analysis_{self.date.replace('-', '_')}.xlsx"

            # Prepare data for Excel
            excel_data = []

            for ticker, result in results.items():
                if 'error' in result:
                    excel_data.append({
                        'Ticker': ticker,
                        'Status': 'Error',
                        'Error': result['error'],
                        'Movement_Score': 0,
                        'Grade': 'N/A'
                    })
                    continue

                candle_analysis = result.get('candle_analysis', {})
                technical_indicators = result.get('technical_indicators', {})
                movement_score = result.get('movement_score', {})

                row = {
                    'Ticker': ticker,
                    'Status': 'Success',
                    'Movement_Score': movement_score.get('movement_score', 0),
                    'Grade': movement_score.get('grade', 'N/A'),
                    'Total_Candles': candle_analysis.get('total_candles', 0),
                    'Avg_Candle_Height': candle_analysis.get('avg_candle_height', 0),
                    'Height_to_Price_Ratio': candle_analysis.get('height_to_price_ratio', 0),
                    'Movement_Frequency': candle_analysis.get('movement_frequency', 0),
                    'Height_Consistency': candle_analysis.get('height_consistency', 0),
                    'Price_Volatility': candle_analysis.get('price_volatility', 0),
                    'ATR_Avg': technical_indicators.get('atr_avg', 0),
                    'RSI_Current': technical_indicators.get('rsi_current', 0),
                    'BB_Width_Avg': technical_indicators.get('bb_width_avg', 0),
                    'MACD_Signal_Strength': technical_indicators.get('macd_signal_strength', 0),
                    'Volume_Consistency': technical_indicators.get('volume_consistency', 0),
                    'Is_Known_Good': ticker in KNOWN_GOOD_STOCKS,
                    'First_Letter': ticker[0] if ticker else ''
                }

                # Add score breakdown
                score_breakdown = movement_score.get('score_breakdown', {})
                for key, value in score_breakdown.items():
                    row[f'Score_{key}'] = value

                excel_data.append(row)

            # Create DataFrame and export
            df = pd.DataFrame(excel_data)
            df = df.sort_values('Movement_Score', ascending=False)

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # Main results
                df.to_excel(writer, sheet_name='All_Stocks', index=False)

                # Good stocks only (score >= 60)
                good_stocks_df = df[df['Movement_Score'] >= 60]
                good_stocks_df.to_excel(writer, sheet_name='Good_Stocks', index=False)

                # A-G stocks for validation
                a_to_g_df = df[df['First_Letter'].isin(list('ABCDEFG'))]
                a_to_g_df.to_excel(writer, sheet_name='A_to_G_Validation', index=False)

                # Known good stocks validation
                known_good_df = df[df['Is_Known_Good'] == True]
                known_good_df.to_excel(writer, sheet_name='Known_Good_Validation', index=False)

            logger.info(f"Results exported to {filename}")
            return filename

        except Exception as e:
            logger.error(f"Error exporting to Excel: {str(e)}")
            return None

def get_current_time_info():
    """Get current date and time information"""
    now = datetime.now()
    current_date = now.strftime('%d-%m-%Y')
    current_time = now.strftime('%H:%M')
    return current_date, current_time

def is_market_live():
    """Check if market is currently live"""
    now = datetime.now()
    current_time = now.time()

    # Market hours: 9:15 AM to 3:30 PM on weekdays
    market_start = datetime.strptime("09:15", "%H:%M").time()
    market_end = datetime.strptime("15:30", "%H:%M").time()

    is_weekday = now.weekday() < 5  # Monday = 0, Sunday = 6
    is_market_hours = market_start <= current_time <= market_end

    return is_weekday and is_market_hours

def run_technical_analysis_on_good_stocks(good_stocks: List[Dict], date: str):
    """
    Run comprehensive technical analysis on confirmed good stocks
    """
    try:
        logger.info("Running comprehensive technical analysis on good stocks...")

        # Import technical analyzer
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer

        analyzer = IntegratedTechnicalAnalyzer()

        for stock_result in good_stocks:
            ticker = stock_result['ticker']
            token_info = stock_result['token_info']

            logger.info(f"Running technical analysis for {ticker}...")

            # Run full technical analysis
            try:
                analysis_result = analyzer.run_full_analysis(
                    ticker=ticker,
                    exchange='NSE',
                    date=date,
                    start_time="09:15",
                    end_time="15:30",
                    method='all',
                    categories=['all'],
                    export_excel=True
                )

                logger.info(f"Technical analysis completed for {ticker}")

            except Exception as e:
                logger.error(f"Error in technical analysis for {ticker}: {str(e)}")

        logger.info("Comprehensive technical analysis completed")

    except Exception as e:
        logger.error(f"Error running technical analysis: {str(e)}")

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Find stocks with good movement patterns')
    parser.add_argument('--date', type=str, help='Date in DD-MM-YYYY format (default: today)')
    parser.add_argument('--mode', choices=['live', 'historical'], default='auto',
                       help='Analysis mode (default: auto-detect)')
    parser.add_argument('--stocks', choices=['all', 'a_to_g', 'test'], default='a_to_g',
                       help='Stock list to analyze (default: a_to_g for testing)')
    parser.add_argument('--min_score', type=float, default=60.0,
                       help='Minimum movement score for good stocks (default: 60.0)')
    parser.add_argument('--technical_analysis', action='store_true',
                       help='Run comprehensive technical analysis on good stocks')

    args = parser.parse_args()

    # Determine date and mode
    if args.date:
        analysis_date = args.date
    else:
        analysis_date = get_current_time_info()[0]

    if args.mode == 'auto':
        mode = 'live' if is_market_live() else 'historical'
    else:
        mode = args.mode

    # Determine stock list
    if args.stocks == 'all':
        stock_list = ALL_STOCKS
    elif args.stocks == 'a_to_g':
        stock_list = A_TO_G_STOCKS
    else:  # test
        stock_list = KNOWN_GOOD_STOCKS[:5]  # First 5 known good stocks for testing

    # Determine time range
    if mode == 'live':
        current_time = get_current_time_info()[1]
        start_time = "09:15"
        end_time = current_time
        logger.info(f"Live mode: Analyzing from {start_time} to {current_time}")
    else:
        start_time = "09:15"
        end_time = "15:30"
        logger.info(f"Historical mode: Analyzing full day {start_time} to {end_time}")

    logger.info(f"Starting Active Stocks Finder...")
    logger.info(f"Date: {analysis_date}")
    logger.info(f"Mode: {mode}")
    logger.info(f"Stocks to analyze: {len(stock_list)}")
    logger.info(f"Minimum score: {args.min_score}")

    # Initialize analyzer
    finder = ActiveStocksFinder(date=analysis_date)

    # Analyze stocks
    results = finder.analyze_multiple_stocks(stock_list, start_time, end_time)

    # Filter good stocks
    good_stocks = finder.filter_good_stocks(results, args.min_score)

    # Display results
    logger.info(f"\n{'='*80}")
    logger.info(f"ANALYSIS RESULTS")
    logger.info(f"{'='*80}")
    logger.info(f"Total stocks analyzed: {len(results)}")
    logger.info(f"Good stocks found (score >= {args.min_score}): {len(good_stocks)}")

    if good_stocks:
        logger.info(f"\nTop Good Stocks:")
        for i, stock in enumerate(good_stocks[:10], 1):
            ticker = stock['ticker']
            score = stock['movement_score']['movement_score']
            grade = stock['movement_score']['grade']
            is_known = ticker in KNOWN_GOOD_STOCKS
            logger.info(f"{i:2d}. {ticker:12s} - Score: {score:5.1f} ({grade}) {'✓ Known Good' if is_known else ''}")

    # Validation check
    known_good_found = [s['ticker'] for s in good_stocks if s['ticker'] in KNOWN_GOOD_STOCKS]
    logger.info(f"\nValidation: Found {len(known_good_found)}/{len(KNOWN_GOOD_STOCKS)} known good stocks")
    logger.info(f"Known good stocks found: {', '.join(known_good_found)}")

    # Export results
    excel_file = finder.export_to_excel(results)
    if excel_file:
        logger.info(f"Results exported to: {excel_file}")

    # Run technical analysis if requested
    if args.technical_analysis and good_stocks:
        run_technical_analysis_on_good_stocks(good_stocks, analysis_date)

    logger.info("Analysis completed!")

if __name__ == "__main__":
    main()
