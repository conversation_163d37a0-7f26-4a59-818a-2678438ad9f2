{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d8e714b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.14b0\n"]}], "source": ["import pandas_ta as ta\n", "import pandas as pd\n", "print(ta.__version__)\n"]}, {"cell_type": "markdown", "id": "84bfb98a", "metadata": {}, "source": ["## Fetching data"]}, {"cell_type": "code", "execution_count": 3, "id": "18cb5a85", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'yfinance' has no attribute 'pdr_override'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame()\n\u001b[1;32m----> 2\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mta\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mticker\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mspy\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\pandas_ta\\core.py:851\u001b[0m, in \u001b[0;36mAnalysisIndicators.ticker\u001b[1;34m(self, ticker, **kwargs)\u001b[0m\n\u001b[0;32m    849\u001b[0m ds \u001b[38;5;241m=\u001b[39m ds\u001b[38;5;241m.\u001b[39mlower() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ds, \u001b[38;5;28mstr\u001b[39m)\n\u001b[0;32m    850\u001b[0m \u001b[38;5;66;03m# df = av(ticker, **kwargs) if ds and ds == \"av\" else yf(ticker, **kwargs)\u001b[39;00m\n\u001b[1;32m--> 851\u001b[0m df \u001b[38;5;241m=\u001b[39m yf(ticker, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    853\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m df \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m: \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[0;32m    854\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m df\u001b[38;5;241m.\u001b[39mempty:\n", "File \u001b[1;32m~\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\pandas_ta\\utils\\data\\yahoofinance.py:86\u001b[0m, in \u001b[0;36myf\u001b[1;34m(ticker, **kwargs)\u001b[0m\n\u001b[0;32m     84\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m Imports[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myfinance\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mand\u001b[39;00m ticker \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m     85\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01myfinance\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01myfra\u001b[39;00m\n\u001b[1;32m---> 86\u001b[0m     \u001b[43myfra\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpdr_override\u001b[49m()\n\u001b[0;32m     88\u001b[0m     \u001b[38;5;66;03m# Ticker Info & Chart History\u001b[39;00m\n\u001b[0;32m     89\u001b[0m     yfd \u001b[38;5;241m=\u001b[39m yfra\u001b[38;5;241m.\u001b[39mTicker(ticker)\n", "\u001b[1;31mAttributeError\u001b[0m: module 'yfinance' has no attribute 'pdr_override'"]}], "source": ["df = pd.DataFrame()\n", "df = df.ta.ticker(\"spy\")"]}, {"cell_type": "code", "execution_count": null, "id": "278d8839-114a-441c-94e6-a23df8975ad1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "7efb0c2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>123.470612</td>\n", "      <td>129.226052</td>\n", "      <td>122.582119</td>\n", "      <td>128.613985</td>\n", "      <td>112117500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>124.744102</td>\n", "      <td>127.014693</td>\n", "      <td>123.480472</td>\n", "      <td>125.267324</td>\n", "      <td>89113600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>123.421249</td>\n", "      <td>126.136083</td>\n", "      <td>123.164580</td>\n", "      <td>125.504267</td>\n", "      <td>80962700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>127.962425</td>\n", "      <td>128.623856</td>\n", "      <td>123.292916</td>\n", "      <td>124.398597</td>\n", "      <td>87754700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>128.485641</td>\n", "      <td>131.703962</td>\n", "      <td>128.228972</td>\n", "      <td>128.801557</td>\n", "      <td>70790800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>201.500000</td>\n", "      <td>202.300003</td>\n", "      <td>198.960007</td>\n", "      <td>201.630005</td>\n", "      <td>55814300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>200.300003</td>\n", "      <td>203.440002</td>\n", "      <td>200.199997</td>\n", "      <td>202.589996</td>\n", "      <td>54064000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>201.559998</td>\n", "      <td>203.669998</td>\n", "      <td>200.619995</td>\n", "      <td>201.449997</td>\n", "      <td>39525700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>201.000000</td>\n", "      <td>202.639999</td>\n", "      <td>199.460007</td>\n", "      <td>201.429993</td>\n", "      <td>50799100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27</th>\n", "      <td>201.080002</td>\n", "      <td>203.220001</td>\n", "      <td>200.000000</td>\n", "      <td>201.889999</td>\n", "      <td>73114100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>623 rows × 5 columns</p>\n", "</div>"], "text/plain": ["Price            Close        High         Low        Open     Volume\n", "Ticker            AAPL        AAPL        AAPL        AAPL       AAPL\n", "Date                                                                 \n", "2023-01-03  123.470612  129.226052  122.582119  128.613985  112117500\n", "2023-01-04  124.744102  127.014693  123.480472  125.267324   89113600\n", "2023-01-05  123.421249  126.136083  123.164580  125.504267   80962700\n", "2023-01-06  127.962425  128.623856  123.292916  124.398597   87754700\n", "2023-01-09  128.485641  131.703962  128.228972  128.801557   70790800\n", "...                ...         ...         ...         ...        ...\n", "2025-06-23  201.500000  202.300003  198.960007  201.630005   55814300\n", "2025-06-24  200.300003  203.440002  200.199997  202.589996   54064000\n", "2025-06-25  201.559998  203.669998  200.619995  201.449997   39525700\n", "2025-06-26  201.000000  202.639999  199.460007  201.429993   50799100\n", "2025-06-27  201.080002  203.220001  200.000000  201.889999   73114100\n", "\n", "[623 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "id": "639c8062", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'yfinance' has no attribute 'pdr_override'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 5\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Valid periods: 1d,5d,1mo,3mo,6mo,1y,2y,5y,10y,ytd,max\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Valid intervals: 1m,2m,5m,15m,30m,60m,90m,1h,1d,5d,1wk,1mo,3mo\u001b[39;00m\n\u001b[1;32m----> 5\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mta\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mticker\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43maapl\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mperiod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m1mo\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minterval\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m1h\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\pandas_ta\\core.py:851\u001b[0m, in \u001b[0;36mAnalysisIndicators.ticker\u001b[1;34m(self, ticker, **kwargs)\u001b[0m\n\u001b[0;32m    849\u001b[0m ds \u001b[38;5;241m=\u001b[39m ds\u001b[38;5;241m.\u001b[39mlower() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ds, \u001b[38;5;28mstr\u001b[39m)\n\u001b[0;32m    850\u001b[0m \u001b[38;5;66;03m# df = av(ticker, **kwargs) if ds and ds == \"av\" else yf(ticker, **kwargs)\u001b[39;00m\n\u001b[1;32m--> 851\u001b[0m df \u001b[38;5;241m=\u001b[39m yf(ticker, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    853\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m df \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m: \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[0;32m    854\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m df\u001b[38;5;241m.\u001b[39mempty:\n", "File \u001b[1;32m~\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\pandas_ta\\utils\\data\\yahoofinance.py:86\u001b[0m, in \u001b[0;36myf\u001b[1;34m(ticker, **kwargs)\u001b[0m\n\u001b[0;32m     84\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m Imports[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myfinance\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mand\u001b[39;00m ticker \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m     85\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01myfinance\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01myfra\u001b[39;00m\n\u001b[1;32m---> 86\u001b[0m     \u001b[43myfra\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpdr_override\u001b[49m()\n\u001b[0;32m     88\u001b[0m     \u001b[38;5;66;03m# Ticker Info & Chart History\u001b[39;00m\n\u001b[0;32m     89\u001b[0m     yfd \u001b[38;5;241m=\u001b[39m yfra\u001b[38;5;241m.\u001b[39mTicker(ticker)\n", "\u001b[1;31mAttributeError\u001b[0m: module 'yfinance' has no attribute 'pdr_override'"]}], "source": ["# Valid periods: 1d,5d,1mo,3mo,6mo,1y,2y,5y,10y,ytd,max\n", "\n", "# Valid intervals: 1m,2m,5m,15m,30m,60m,90m,1h,1d,5d,1wk,1mo,3mo\n", "\n", "df = df.ta.ticker(\"aapl\", period=\"1mo\", interval=\"1h\")"]}, {"cell_type": "code", "execution_count": 5, "id": "6668f1c5", "metadata": {"scrolled": true}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 1)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[5], line 1\u001b[1;36m\u001b[0m\n\u001b[1;33m    df.\u001b[0m\n\u001b[1;37m       ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["df."]}, {"cell_type": "markdown", "id": "da7a37a4", "metadata": {}, "source": ["## Indicators"]}, {"cell_type": "code", "execution_count": 11, "id": "914c25f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pandas TA - Technical Analysis Indicators - v0.3.14b0\n", "Total Indicators & Utilities: 205\n", "Abbreviations:\n", "    aberration, above, above_value, accbands, ad, adosc, adx, alma, amat, ao, aobv, apo, aroon, atr, bbands, below, below_value, bias, bop, brar, cci, cdl_pattern, cdl_z, cfo, cg, chop, cksp, cmf, cmo, coppock, cross, cross_value, cti, decay, decreasing, dema, dm, donchian, dpo, ebsw, efi, ema, entropy, eom, er, eri, fisher, fwma, ha, hilo, hl2, hlc3, hma, hwc, hwma, ichimoku, increasing, inertia, jma, kama, kc, kdj, kst, kurtosis, kvo, linreg, log_return, long_run, macd, mad, massi, mcgd, median, mfi, midpoint, midprice, mom, natr, nvi, obv, ohlc4, pdist, percent_return, pgo, ppo, psar, psl, pvi, pvo, pvol, pvr, pvt, pwma, qqe, qstick, quantile, rma, roc, rsi, rsx, rvgi, rvi, short_run, sinwma, skew, slope, sma, smi, squeeze, squeeze_pro, ssf, stc, stdev, stoch, stochrsi, supertrend, swma, t3, td_seq, tema, thermo, tos_stdevall, trima, trix, true_range, tsi, tsignals, ttm_trend, ui, uo, variance, vhf, vidya, vortex, vp, vwap, vwma, wcp, willr, wma, xsignals, zlma, zscore\n", "\n", "Candle Patterns:\n", "    2crows, 3blackcrows, 3inside, 3linestrike, 3outside, 3starsinsouth, 3whitesoldiers, abandonedbaby, advanceblock, belthold, breakaway, closingmar<PERSON>ozu, concealbabyswall, counterattack, darkcloudcover, doji, dojistar, dragonflydoji, engulfing, eveningdojistar, eveningstar, gapsidesidewhite, gravestonedoji, hammer, hangingman, harami, haramicross, highwave, hikkake, hikkakemod, homingpigeon, identical3crows, inneck, inside, invertedhammer, kicking, kickingbylength, ladderbottom, longleggeddoji, longline, marubozu, matchinglow, mathold, morningdojistar, morningstar, onneck, piercing, rickshawman, risefall3methods, separatinglines, shootingstar, shortline, spinningtop, stalledpattern, sticksandwich, takuri, tasukigap, thrusting, tristar, unique3river, upsidegap2crows, xsidegap3methods\n", "Help on NoneType object:\n", "\n", "class NoneType(object)\n", " |  Methods defined here:\n", " |  \n", " |  __bool__(self, /)\n", " |      True if self else False\n", " |  \n", " |  __repr__(self, /)\n", " |      Return repr(self).\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods defined here:\n", " |  \n", " |  __new__(*args, **kwargs) from builtins.type\n", " |      Create and return a new object.  See help(type) for accurate signature.\n", "\n"]}], "source": ["help(df.ta.indicators())"]}, {"cell_type": "code", "execution_count": 20, "id": "ba60a3a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function brar in module pandas_ta.momentum.brar:\n", "\n", "brar(open_, high, low, close, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", "    BRAR (BRAR)\n", "    \n", "    BR and AR\n", "    \n", "    Sources:\n", "        No internet resources on definitive definition.\n", "        Request by Github user homily, issue #46\n", "    \n", "    Calculation:\n", "        Default Inputs:\n", "            length=26, scalar=100\n", "        SUM = Sum\n", "    \n", "        HO_Diff = high - open\n", "        OL_Diff = open - low\n", "        HCY = high - close[-1]\n", "        CYL = close[-1] - low\n", "        HCY[HCY < 0] = 0\n", "        CYL[CYL < 0] = 0\n", "        AR = scalar * SUM(HO, length) / SUM(OL, length)\n", "        BR = scalar * SUM(HCY, length) / SUM(CYL, length)\n", "    \n", "    Args:\n", "        open_ (pd.Series): Series of 'open's\n", "        high (pd.Series): Series of 'high's\n", "        low (pd.Series): Series of 'low's\n", "        close (pd.Series): Series of 'close's\n", "        length (int): The period. Default: 26\n", "        scalar (float): How much to magnify. Default: 100\n", "        drift (int): The difference period. Default: 1\n", "        offset (int): How many periods to offset the result. Default: 0\n", "    \n", "    Kwargs:\n", "        fillna (value, optional): pd.DataFrame.fillna(value)\n", "        fill_method (value, optional): Type of fill method\n", "    \n", "    Returns:\n", "        pd.DataFrame: ar, br columns.\n", "\n"]}], "source": ["help(ta.brar)"]}, {"cell_type": "code", "execution_count": 25, "id": "00a1a2ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on package pandas_ta.statistics in pandas_ta:\n", "\n", "NAME\n", "    pandas_ta.statistics - # -*- coding: utf-8 -*-\n", "\n", "PACKAGE CONTENTS\n", "    entropy\n", "    kurtosis\n", "    mad\n", "    median\n", "    quantile\n", "    skew\n", "    stdev\n", "    tos_stdevall\n", "    variance\n", "    zscore\n", "\n", "FILE\n", "    c:\\users\\<USER>\\anaconda3\\envs\\shoonya1\\lib\\site-packages\\pandas_ta\\statistics\\__init__.py\n", "\n", "\n"]}], "source": ["help(ta.statistics)"]}, {"cell_type": "markdown", "id": "027e7fa6", "metadata": {}, "source": ["# Three Layers of Abstraction"]}, {"cell_type": "markdown", "id": "eacac5c7", "metadata": {}, "source": ["## 1. Directly calling function"]}, {"cell_type": "code", "execution_count": 16, "id": "1fe51fd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function pgo in module pandas_ta.momentum.pgo:\n", "\n", "pgo(high, low, close, length=None, offset=None, **kwargs)\n", "    Pretty Good Oscillator (PGO)\n", "    \n", "    The Pretty Good Oscillator indicator was created by <PERSON> to measure the distance of the current close from its N-day Simple Moving Average, expressed in terms of an average true range over a similar period. <PERSON>'s approach was to\n", "    use it as a breakout system for longer term trades. Long if greater than 3.0 and\n", "    short if less than -3.0.\n", "    \n", "    Sources:\n", "        https://library.tradingtechnologies.com/trade/chrt-ti-pretty-good-oscillator.html\n", "    \n", "    Calculation:\n", "        Default Inputs:\n", "            length=14\n", "        ATR = Average True Range\n", "        SMA = Simple Moving Average\n", "        EMA = Exponential Moving Average\n", "    \n", "        PGO = (close - SMA(close, length)) / EMA(ATR(high, low, close, length), length)\n", "    \n", "    Args:\n", "        high (pd.Series): Series of 'high's\n", "        low (pd.Series): Series of 'low's\n", "        close (pd.Series): Series of 'close's\n", "        length (int): It's period. Default: 14\n", "        offset (int): How many periods to offset the result. Default: 0\n", "    \n", "    Kwargs:\n", "        fillna (value, optional): pd.DataFrame.fillna(value)\n", "        fill_method (value, optional): Type of fill method\n", "    \n", "    Returns:\n", "        pd.Series: New feature generated.\n", "\n"]}], "source": ["help(ta.pgo)"]}, {"cell_type": "code", "execution_count": 10, "id": "bc9c0c78", "metadata": {}, "outputs": [], "source": ["rsi = ta.rsi(close = df[\"Volume\"], length = 40)\n", "rsi"]}, {"cell_type": "code", "execution_count": 8, "id": "611b94b2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "      <th>rsi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>123.470612</td>\n", "      <td>129.226052</td>\n", "      <td>122.582119</td>\n", "      <td>128.613985</td>\n", "      <td>112117500</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>124.744102</td>\n", "      <td>127.014693</td>\n", "      <td>123.480472</td>\n", "      <td>125.267324</td>\n", "      <td>89113600</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>123.421249</td>\n", "      <td>126.136083</td>\n", "      <td>123.164580</td>\n", "      <td>125.504267</td>\n", "      <td>80962700</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>127.962425</td>\n", "      <td>128.623856</td>\n", "      <td>123.292916</td>\n", "      <td>124.398597</td>\n", "      <td>87754700</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>128.485641</td>\n", "      <td>131.703962</td>\n", "      <td>128.228972</td>\n", "      <td>128.801557</td>\n", "      <td>70790800</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>201.500000</td>\n", "      <td>202.300003</td>\n", "      <td>198.960007</td>\n", "      <td>201.630005</td>\n", "      <td>55814300</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>200.300003</td>\n", "      <td>203.440002</td>\n", "      <td>200.199997</td>\n", "      <td>202.589996</td>\n", "      <td>54064000</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>201.559998</td>\n", "      <td>203.669998</td>\n", "      <td>200.619995</td>\n", "      <td>201.449997</td>\n", "      <td>39525700</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>201.000000</td>\n", "      <td>202.639999</td>\n", "      <td>199.460007</td>\n", "      <td>201.429993</td>\n", "      <td>50799100</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27</th>\n", "      <td>201.080002</td>\n", "      <td>203.220001</td>\n", "      <td>200.000000</td>\n", "      <td>201.889999</td>\n", "      <td>73114100</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>623 rows × 6 columns</p>\n", "</div>"], "text/plain": ["Price            Close        High         Low        Open     Volume   rsi\n", "Ticker            AAPL        AAPL        AAPL        AAPL       AAPL      \n", "Date                                                                       \n", "2023-01-03  123.470612  129.226052  122.582119  128.613985  112117500  None\n", "2023-01-04  124.744102  127.014693  123.480472  125.267324   89113600  None\n", "2023-01-05  123.421249  126.136083  123.164580  125.504267   80962700  None\n", "2023-01-06  127.962425  128.623856  123.292916  124.398597   87754700  None\n", "2023-01-09  128.485641  131.703962  128.228972  128.801557   70790800  None\n", "...                ...         ...         ...         ...        ...   ...\n", "2025-06-23  201.500000  202.300003  198.960007  201.630005   55814300  None\n", "2025-06-24  200.300003  203.440002  200.199997  202.589996   54064000  None\n", "2025-06-25  201.559998  203.669998  200.619995  201.449997   39525700  None\n", "2025-06-26  201.000000  202.639999  199.460007  201.429993   50799100  None\n", "2025-06-27  201.080002  203.220001  200.000000  201.889999   73114100  None\n", "\n", "[623 rows x 6 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"rsi\"] = ta.rsi(close = df[\"Close\"], length = 40)\n", "df"]}, {"cell_type": "markdown", "id": "469d22d5", "metadata": {}, "source": ["## 2. Using dataframe accessor"]}, {"cell_type": "code", "execution_count": 10, "id": "630506f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on AnalysisIndicators in module pandas_ta.core object:\n", "\n", "class AnalysisIndicators(BasePandasObject)\n", " |  AnalysisIndicators(pandas_obj)\n", " |  \n", " |  This Pandas Extension is named 'ta' for Technical Analysis. In other words,\n", " |  it is a Numerical Time Series Feature Generator where the Time Series data\n", " |  is biased towards Financial Market data; typical data includes columns\n", " |  named :\"open\", \"high\", \"low\", \"close\", \"volume\".\n", " |  \n", " |  This TA Library hopefully allows you to apply familiar and unique Technical\n", " |  Analysis Indicators easily with the DataFrame Extension named 'ta'. Even\n", " |  though 'ta' is a Pandas DataFrame Extension, you can still call Technical\n", " |  Analysis indicators individually if you are more comfortable with that\n", " |  approach or it allows you to easily and automatically apply the indicators\n", " |  with the strategy method. See: help(ta.strategy).\n", " |  \n", " |  By default, the 'ta' extension uses lower case column names: open, high,\n", " |  low, close, and volume. You can override the defaults by providing the it's\n", " |  replacement name when calling the indicator. For example, to call the\n", " |  indicator hl2().\n", " |  \n", " |  With 'default' columns: open, high, low, close, and volume.\n", " |  >>> df.ta.hl2()\n", " |  >>> df.ta(kind=\"hl2\")\n", " |  \n", " |  With DataFrame columns: Open, High, Low, Close, and Volume.\n", " |  >>> df.ta.hl2(high=\"High\", low=\"Low\")\n", " |  >>> df.ta(kind=\"hl2\", high=\"High\", low=\"Low\")\n", " |  \n", " |  If you do not want to use a DataFrame Extension, just call it normally.\n", " |  >>> sma10 = ta.sma(df[\"Close\"]) # Default length=10\n", " |  >>> sma50 = ta.sma(df[\"Close\"], length=50)\n", " |  >>> ichi<PERSON><PERSON>, span = ta.ichimoku(df[\"High\"], df[\"Low\"], df[\"Close\"])\n", " |  \n", " |  Args:\n", " |      kind (str, optional): Default: None. Kind is the 'name' of the indicator.\n", " |          It converts kind to lowercase before calling.\n", " |      timed (bool, optional): Default: False. Curious about the execution\n", " |          speed?\n", " |      kwargs: Extension specific modifiers.\n", " |          append (bool, optional): Default: False. When True, it appends the\n", " |          resultant column(s) to the DataFrame.\n", " |  \n", " |  Returns:\n", " |      Most Indicators will return a Pandas Series. Others like MACD, BBANDS,\n", " |      KC, et al will return a Pandas DataFrame. Ichimoku on the other hand\n", " |      will return two DataFrames, the Ichimoku DataFrame for the known period\n", " |      and a Span DataFrame for the future of the Span values.\n", " |  \n", " |  Let's get started!\n", " |  \n", " |  1. Loading the 'ta' module:\n", " |  >>> import pandas as pd\n", " |  >>> import ta as ta\n", " |  \n", " |  2. Load some data:\n", " |  >>> df = pd.read_csv(\"AAPL.csv\", index_col=\"date\", parse_dates=True)\n", " |  \n", " |  3. Help!\n", " |  3a. General Help:\n", " |  >>> help(df.ta)\n", " |  >>> df.ta()\n", " |  3b. Indicator Help:\n", " |  >>> help(ta.apo)\n", " |  3c. Indicator Extension Help:\n", " |  >>> help(df.ta.apo)\n", " |  \n", " |  4. Ways of calling an indicator.\n", " |  4a. Standard: Calling just the APO indicator without \"ta\" DataFrame extension.\n", " |  >>> ta.apo(df[\"close\"])\n", " |  4b. DataFrame Extension: Calling just the APO indicator with \"ta\" DataFrame extension.\n", " |  >>> df.ta.apo()\n", " |  4c. DataFrame Extension (kind): Calling APO using 'kind'\n", " |  >>> df.ta(kind=\"apo\")\n", " |  4d. Strategy:\n", " |  >>> df.ta.strategy(\"All\") # Default\n", " |  >>> df.ta.strategy(ta.Strategy(\"My Strat\", ta=[{\"kind\": \"apo\"}])) # Custom\n", " |  \n", " |  5. Working with kwargs\n", " |  5a. Append the result to the working df.\n", " |  >>> df.ta.apo(append=True)\n", " |  5b. Timing an indicator.\n", " |  >>> apo = df.ta(kind=\"apo\", timed=True)\n", " |  >>> print(apo.timed)\n", " |  \n", " |  Method resolution order:\n", " |      AnalysisIndicators\n", " |      BasePandasObject\n", " |      pandas.core.base.PandasObject\n", " |      pandas.core.accessor.DirNamesMixin\n", " |      builtins.object\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  __call__(self, kind: str = None, timed: bool = False, version: bool = False, **kwargs)\n", " |      Call self as a function.\n", " |  \n", " |  __init__(self, pandas_obj)\n", " |      Initialize self.  See help(type(self)) for accurate signature.\n", " |  \n", " |  aberration(self, length=None, atr_length=None, offset=None, **kwargs)\n", " |      # Volatility\n", " |  \n", " |  above(self, asint=True, offset=None, **kwargs)\n", " |      # Utility\n", " |  \n", " |  above_value(self, value=None, asint=True, offset=None, **kwargs)\n", " |  \n", " |  accbands(self, length=None, c=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  ad(self, open_=None, signed=True, offset=None, **kwargs)\n", " |      # Volume\n", " |  \n", " |  adosc(self, open_=None, fast=None, slow=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  adx(self, length=None, lensig=None, mamode=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |      # Trend\n", " |  \n", " |  alma(self, length=None, sigma=None, distribution_offset=None, offset=None, **kwargs)\n", " |      # Overlap\n", " |  \n", " |  amat(self, fast=None, slow=None, mamode=None, lookback=None, offset=None, **kwargs)\n", " |  \n", " |  ao(self, fast=None, slow=None, offset=None, **kwargs)\n", " |      # Momentum\n", " |  \n", " |  aobv(self, fast=None, slow=None, mamode=None, max_lookback=None, min_lookback=None, offset=None, **kwargs)\n", " |  \n", " |  apo(self, fast=None, slow=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  aroon(self, length=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  atr(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  bbands(self, length=None, std=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  below(self, asint=True, offset=None, **kwargs)\n", " |  \n", " |  below_value(self, value=None, asint=True, offset=None, **kwargs)\n", " |  \n", " |  bias(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  bop(self, percentage=False, offset=None, **kwargs)\n", " |  \n", " |  brar(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  cci(self, length=None, c=None, offset=None, **kwargs)\n", " |  \n", " |  cdl_pattern(self, name='all', offset=None, **kwargs)\n", " |      # Public DataFrame Methods: Indicators and Utilities\n", " |      # Candles\n", " |  \n", " |  cdl_z(self, full=None, offset=None, **kwargs)\n", " |  \n", " |  cfo(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  cg(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  chop(self, length=None, atr_length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  cksp(self, p=None, x=None, q=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  cmf(self, open_=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  cmo(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  constants(self, append: bool, values: list)\n", " |      Constants\n", " |      \n", " |      Add or remove constants to the DataFrame easily with <PERSON><PERSON><PERSON>'s arrays or\n", " |      lists. Useful when you need easily accessible horizontal lines for\n", " |      charting.\n", " |      \n", " |      Add constant '1' to the DataFrame\n", " |      >>> df.ta.constants(True, [1])\n", " |      Remove constant '1' to the DataFrame\n", " |      >>> df.ta.constants(False, [1])\n", " |      \n", " |      Adding constants for charting\n", " |      >>> import numpy as np\n", " |      >>> chart_lines = np.append(np.arange(-4, 5, 1), np.arange(-100, 110, 10))\n", " |      >>> df.ta.constants(True, chart_lines)\n", " |      Removing some constants from the DataFrame\n", " |      >>> df.ta.constants(False, np.array([-60, -40, 40, 60]))\n", " |      \n", " |      Args:\n", " |          append (bool): If True, appends a Numpy range of constants to the\n", " |              working DataFrame.  If False, it removes the constant range from\n", " |              the working DataFrame. Default: None.\n", " |      \n", " |      Returns:\n", " |          Returns the appended constants\n", " |          Returns nothing to the user.  Either adds or removes constant ranges\n", " |          from the working DataFrame.\n", " |  \n", " |  coppock(self, length=None, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  cross(self, above=True, asint=True, offset=None, **kwargs)\n", " |  \n", " |  cross_value(self, value=None, above=True, asint=True, offset=None, **kwargs)\n", " |  \n", " |  cti(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  decay(self, length=None, mode=None, offset=None, **kwargs)\n", " |  \n", " |  decreasing(self, length=None, strict=None, asint=None, offset=None, **kwargs)\n", " |  \n", " |  dema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  dm(self, drift=None, offset=None, mamode=None, **kwargs)\n", " |  \n", " |  donchian(self, lower_length=None, upper_length=None, offset=None, **kwargs)\n", " |  \n", " |  dpo(self, length=None, centered=True, offset=None, **kwargs)\n", " |  \n", " |  ebsw(self, close=None, length=None, bars=None, offset=None, **kwargs)\n", " |      # Cycles\n", " |  \n", " |  efi(self, length=None, mamode=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  ema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  entropy(self, length=None, base=None, offset=None, **kwargs)\n", " |      # Statistics\n", " |  \n", " |  eom(self, length=None, divisor=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  er(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  eri(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  fisher(self, length=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  fwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ha(self, offset=None, **kwargs)\n", " |  \n", " |  hilo(self, high_length=None, low_length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  hl2(self, offset=None, **kwargs)\n", " |  \n", " |  hlc3(self, offset=None, **kwargs)\n", " |  \n", " |  hma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  hwc(self, na=None, nb=None, nc=None, nd=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  hwma(self, na=None, nb=None, nc=None, offset=None, **kwargs)\n", " |  \n", " |  ichi<PERSON>ku(self, tenkan=None, kijun=None, senkou=None, include_chikou=True, offset=None, **kwargs)\n", " |  \n", " |  increasing(self, length=None, strict=None, asint=None, offset=None, **kwargs)\n", " |  \n", " |  indicators(self, **kwargs)\n", " |      List of Indicators\n", " |      \n", " |      kwargs:\n", " |          as_list (bool, optional): When True, it returns a list of the\n", " |              indicators. Default: False.\n", " |          exclude (list, optional): The passed in list will be excluded\n", " |              from the indicators list. Default: None.\n", " |      \n", " |      Returns:\n", " |          Prints the list of indicators. If as_list=True, then a list.\n", " |  \n", " |  inertia(self, length=None, rvi_length=None, scalar=None, refined=None, thirds=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  jma(self, length=None, phase=None, offset=None, **kwargs)\n", " |  \n", " |  kama(self, length=None, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  kc(self, length=None, scalar=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  kdj(self, length=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  kst(self, roc1=None, roc2=None, roc3=None, roc4=None, sma1=None, sma2=None, sma3=None, sma4=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  kurtosis(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  kvo(self, fast=None, slow=None, length_sig=None, mamode=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  linreg(self, length=None, offset=None, adjust=None, **kwargs)\n", " |  \n", " |  log_return(self, length=None, cumulative=False, percent=False, offset=None, **kwargs)\n", " |      # Performance\n", " |  \n", " |  long_run(self, fast=None, slow=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  macd(self, fast=None, slow=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  mad(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  massi(self, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  mcgd(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  median(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  mfi(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  midpoint(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  midprice(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  mom(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  natr(self, length=None, mamode=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  nvi(self, length=None, initial=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  obv(self, offset=None, **kwargs)\n", " |  \n", " |  ohlc4(self, offset=None, **kwargs)\n", " |  \n", " |  pdist(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  percent_return(self, length=None, cumulative=False, percent=False, offset=None, **kwargs)\n", " |  \n", " |  pgo(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ppo(self, fast=None, slow=None, scalar=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  psar(self, af0=None, af=None, max_af=None, offset=None, **kwargs)\n", " |  \n", " |  psl(self, open_=None, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  pvi(self, length=None, initial=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  pvo(self, fast=None, slow=None, signal=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  pvol(self, volume=None, offset=None, **kwargs)\n", " |  \n", " |  pvr(self, **kwargs)\n", " |  \n", " |  pvt(self, offset=None, **kwargs)\n", " |  \n", " |  pwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  qqe(self, length=None, smooth=None, factor=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  qstick(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  quantile(self, length=None, q=None, offset=None, **kwargs)\n", " |  \n", " |  rma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  roc(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  rsi(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  rsx(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  rvgi(self, length=None, swma_length=None, offset=None, **kwargs)\n", " |  \n", " |  rvi(self, length=None, scalar=None, refined=None, thirds=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  short_run(self, fast=None, slow=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  sinwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  skew(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  slope(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  sma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  smi(self, fast=None, slow=None, signal=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  squeeze(self, bb_length=None, bb_std=None, kc_length=None, kc_scalar=None, mom_length=None, mom_smooth=None, use_tr=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  squeeze_pro(self, bb_length=None, bb_std=None, kc_length=None, kc_scalar_wide=None, kc_scalar_normal=None, kc_scalar_narrow=None, mom_length=None, mom_smooth=None, use_tr=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  ssf(self, length=None, poles=None, offset=None, **kwargs)\n", " |  \n", " |  stc(self, ma1=None, ma2=None, osc=None, tclength=None, fast=None, slow=None, factor=None, offset=None, **kwargs)\n", " |  \n", " |  stdev(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  stoch(self, fast_k=None, slow_k=None, slow_d=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  stochrsi(self, length=None, rsi_length=None, k=None, d=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  strategy(self, *args, **kwargs)\n", " |      Strategy Method\n", " |      \n", " |      An experimental method that by default runs all applicable indicators.\n", " |      Future implementations will allow more specific indicator generation\n", " |      with possibly as json, yaml config file or an sqlite3 table.\n", " |      \n", " |      \n", " |      Kwargs:\n", " |          chunksize (bool): Adjust the chunksize for the Multiprocessing Pool.\n", " |              Default: Number of cores of the OS\n", " |          exclude (list): List of indicator names to exclude. Some are\n", " |              excluded by default for various reasons; they require additional\n", " |              sources, performance (td_seq), not a ohlcv chart (vp) etc.\n", " |          name (str): Select all indicators or indicators by\n", " |              Category such as: \"candles\", \"cycles\", \"momentum\", \"overlap\",\n", " |              \"performance\", \"statistics\", \"trend\", \"volatility\", \"volume\", or\n", " |              \"all\". De<PERSON>ult: \"all\"\n", " |          ordered (bool): Whether to run \"all\" in order. Default: True\n", " |          timed (bool): Show the process time of the strategy().\n", " |              Default: False\n", " |          verbose (bool): Provide some additional insight on the progress of\n", " |              the strategy() execution. Default: False\n", " |  \n", " |  supertrend(self, period=None, multiplier=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  swma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  t3(self, length=None, a=None, offset=None, **kwargs)\n", " |  \n", " |  td_seq(self, asint=None, offset=None, show_all=None, **kwargs)\n", " |  \n", " |  tema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  thermo(self, long=None, short=None, length=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  ticker(self, ticker: str, **kwargs)\n", " |      ticker\n", " |      \n", " |      This method downloads Historical Data if the package yfinance is installed.\n", " |      Additionally it can run a ta.Strategy; Builtin or Custom. It returns a\n", " |      DataFrame if there the DataFrame is not empty, otherwise it exits. For\n", " |      additional yfinance arguments, use help(ta.yf).\n", " |      \n", " |      Historical Data\n", " |      >>> df = df.ta.ticker(\"aapl\")\n", " |      More specifically\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"max\", interval=\"1d\", kind=None)\n", " |      \n", " |      Changing the period of Historical Data\n", " |      Period is used instead of start/end\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1y\")\n", " |      \n", " |      Changing the period and interval of Historical Data\n", " |      Retrieves the past year in weeks\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1y\", interval=\"1wk\")\n", " |      Retrieves the past month in hours\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1mo\", interval=\"1h\")\n", " |      \n", " |      Show everything\n", " |      >>> df = df.ta.ticker(\"aapl\", kind=\"all\")\n", " |      \n", " |      Args:\n", " |          ticker (str): Any string for a ticker you would use with yfinance.\n", " |              Default: \"SPY\"\n", " |      Kwargs:\n", " |          kind (str): Options see above. Default: \"history\"\n", " |          ds (str): Data Source to use. Default: \"yahoo\"\n", " |          strategy (str | ta.Strategy): Which strategy to apply after\n", " |              downloading chart history. Default: None\n", " |      \n", " |          See help(ta.yf) for additional kwargs\n", " |      \n", " |      Returns:\n", " |          Exits if the DataFrame is empty or None\n", " |          Otherwise it returns a DataFrame\n", " |  \n", " |  tos_stdevall(self, length=None, stds=None, offset=None, **kwargs)\n", " |  \n", " |  trima(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  trix(self, length=None, signal=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  true_range(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  tsi(self, fast=None, slow=None, drift=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  tsignals(self, trend=None, asbool=None, trend_reset=None, trend_offset=None, offset=None, **kwargs)\n", " |  \n", " |  ttm_trend(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ui(self, length=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  uo(self, fast=None, medium=None, slow=None, fast_w=None, medium_w=None, slow_w=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  variance(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  vhf(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  vidya(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  vortex(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  vp(self, width=None, percent=None, **kwargs)\n", " |  \n", " |  vwap(self, anchor=None, offset=None, **kwargs)\n", " |  \n", " |  vwma(self, volume=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  wcp(self, offset=None, **kwargs)\n", " |  \n", " |  willr(self, length=None, percentage=True, offset=None, **kwargs)\n", " |  \n", " |  wma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  xsignals(self, signal=None, xa=None, xb=None, above=None, long=None, asbool=None, trend_reset=None, trend_offset=None, offset=None, **kwargs)\n", " |  \n", " |  zlma(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  zscore(self, length=None, std=None, offset=None, **kwargs)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties defined here:\n", " |  \n", " |  categories\n", " |      Returns the categories.\n", " |  \n", " |  datetime_ordered\n", " |      Returns True if the index is a datetime and ordered.\n", " |  \n", " |  last_run\n", " |      Returns the time when the DataFrame was last run.\n", " |  \n", " |  reverse\n", " |      Reverses the DataFrame. Simply: df.iloc[::-1]\n", " |  \n", " |  to_utc\n", " |      Sets the DataFrame index to UTC format\n", " |  \n", " |  version\n", " |      Returns the version.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors defined here:\n", " |  \n", " |  adjusted\n", " |      property: df.ta.adjusted\n", " |  \n", " |  cores\n", " |      Returns the categories.\n", " |  \n", " |  exchange\n", " |      Returns the current Exchange. Default: \"NYSE\".\n", " |  \n", " |  time_range\n", " |      Returns the time ranges of the DataFrame as a float. Default is in \"years\". help(ta.toal_time)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from pandas.core.base.PandasObject:\n", " |  \n", " |  __repr__(self) -> 'str'\n", " |      Return a string representation for a particular object.\n", " |  \n", " |  __sizeof__(self) -> 'int'\n", " |      Generates the total memory usage for an object that returns\n", " |      either a value or Series of values\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes inherited from pandas.core.base.PandasObject:\n", " |  \n", " |  __annotations__ = {'_cache': 'dict[str, Any]'}\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from pandas.core.accessor.DirNamesMixin:\n", " |  \n", " |  __dir__(self) -> 'list[str]'\n", " |      Provide method name lookup and completion.\n", " |      \n", " |      Notes\n", " |      -----\n", " |      Only provide 'public' methods.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors inherited from pandas.core.accessor.DirNamesMixin:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", "\n"]}], "source": ["help(df.ta)"]}, {"cell_type": "code", "execution_count": 8, "id": "5645cef2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[X] Ooops!!! It's True, the series 'close' was not found in \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.bbands()"]}, {"cell_type": "code", "execution_count": null, "id": "2da22d82-3889-4ac2-8736-5e8a1fef0e2c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "b419e87a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close_aapl</th>\n", "      <th>High_aapl</th>\n", "      <th>Low_aapl</th>\n", "      <th>Open_aapl</th>\n", "      <th>Volume_aapl</th>\n", "      <th>SMA_20</th>\n", "      <th>RSI_14</th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>123.470619</td>\n", "      <td>129.226060</td>\n", "      <td>122.582127</td>\n", "      <td>128.613993</td>\n", "      <td>112117500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>124.744118</td>\n", "      <td>127.014709</td>\n", "      <td>123.480487</td>\n", "      <td>125.267339</td>\n", "      <td>89113600</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>123.421249</td>\n", "      <td>126.136083</td>\n", "      <td>123.164580</td>\n", "      <td>125.504267</td>\n", "      <td>80962700</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>127.962418</td>\n", "      <td>128.623848</td>\n", "      <td>123.292909</td>\n", "      <td>124.398589</td>\n", "      <td>87754700</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>128.485641</td>\n", "      <td>131.703962</td>\n", "      <td>128.228972</td>\n", "      <td>128.801557</td>\n", "      <td>70790800</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>121.242312</td>\n", "      <td>125.616809</td>\n", "      <td>129.991306</td>\n", "      <td>6.964828</td>\n", "      <td>0.827904</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-20</th>\n", "      <td>201.000000</td>\n", "      <td>201.699997</td>\n", "      <td>196.860001</td>\n", "      <td>198.240005</td>\n", "      <td>96813500</td>\n", "      <td>200.029501</td>\n", "      <td>49.674578</td>\n", "      <td>193.777657</td>\n", "      <td>197.617999</td>\n", "      <td>201.458341</td>\n", "      <td>3.886632</td>\n", "      <td>0.940325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>201.500000</td>\n", "      <td>202.300003</td>\n", "      <td>198.960007</td>\n", "      <td>201.630005</td>\n", "      <td>55814300</td>\n", "      <td>200.036501</td>\n", "      <td>50.441048</td>\n", "      <td>193.976937</td>\n", "      <td>198.628000</td>\n", "      <td>203.279062</td>\n", "      <td>4.683189</td>\n", "      <td>0.808747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>200.300003</td>\n", "      <td>203.440002</td>\n", "      <td>200.199997</td>\n", "      <td>202.589996</td>\n", "      <td>54064000</td>\n", "      <td>200.288000</td>\n", "      <td>48.530671</td>\n", "      <td>194.180233</td>\n", "      <td>199.004001</td>\n", "      <td>203.827769</td>\n", "      <td>4.847911</td>\n", "      <td>0.634335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>201.559998</td>\n", "      <td>203.669998</td>\n", "      <td>200.619995</td>\n", "      <td>201.449997</td>\n", "      <td>39525700</td>\n", "      <td>200.355500</td>\n", "      <td>50.644378</td>\n", "      <td>196.468510</td>\n", "      <td>200.188000</td>\n", "      <td>203.907491</td>\n", "      <td>3.715997</td>\n", "      <td>0.684433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>201.000000</td>\n", "      <td>202.639999</td>\n", "      <td>199.460007</td>\n", "      <td>201.429993</td>\n", "      <td>50799100</td>\n", "      <td>200.384500</td>\n", "      <td>49.668095</td>\n", "      <td>200.165282</td>\n", "      <td>201.072000</td>\n", "      <td>201.978719</td>\n", "      <td>0.901884</td>\n", "      <td>0.460296</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>622 rows × 12 columns</p>\n", "</div>"], "text/plain": ["            Close_aapl   High_aapl    Low_aapl   Open_aapl  Volume_aapl  \\\n", "Date                                                                      \n", "2023-01-03  123.470619  129.226060  122.582127  128.613993    112117500   \n", "2023-01-04  124.744118  127.014709  123.480487  125.267339     89113600   \n", "2023-01-05  123.421249  126.136083  123.164580  125.504267     80962700   \n", "2023-01-06  127.962418  128.623848  123.292909  124.398589     87754700   \n", "2023-01-09  128.485641  131.703962  128.228972  128.801557     70790800   \n", "...                ...         ...         ...         ...          ...   \n", "2025-06-20  201.000000  201.699997  196.860001  198.240005     96813500   \n", "2025-06-23  201.500000  202.300003  198.960007  201.630005     55814300   \n", "2025-06-24  200.300003  203.440002  200.199997  202.589996     54064000   \n", "2025-06-25  201.559998  203.669998  200.619995  201.449997     39525700   \n", "2025-06-26  201.000000  202.639999  199.460007  201.429993     50799100   \n", "\n", "                SMA_20     RSI_14   BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  \\\n", "Date                                                                    \n", "2023-01-03         NaN        NaN         NaN         NaN         NaN   \n", "2023-01-04         NaN        NaN         NaN         NaN         NaN   \n", "2023-01-05         NaN        NaN         NaN         NaN         NaN   \n", "2023-01-06         NaN        NaN         NaN         NaN         NaN   \n", "2023-01-09         NaN        NaN  121.242312  125.616809  129.991306   \n", "...                ...        ...         ...         ...         ...   \n", "2025-06-20  200.029501  49.674578  193.777657  197.617999  201.458341   \n", "2025-06-23  200.036501  50.441048  193.976937  198.628000  203.279062   \n", "2025-06-24  200.288000  48.530671  194.180233  199.004001  203.827769   \n", "2025-06-25  200.355500  50.644378  196.468510  200.188000  203.907491   \n", "2025-06-26  200.384500  49.668095  200.165282  201.072000  201.978719   \n", "\n", "            BBB_5_2.0  BBP_5_2.0  \n", "Date                              \n", "2023-01-03        NaN        NaN  \n", "2023-01-04        NaN        NaN  \n", "2023-01-05        NaN        NaN  \n", "2023-01-06        NaN        NaN  \n", "2023-01-09   6.964828   0.827904  \n", "...               ...        ...  \n", "2025-06-20   3.886632   0.940325  \n", "2025-06-23   4.683189   0.808747  \n", "2025-06-24   4.847911   0.634335  \n", "2025-06-25   3.715997   0.684433  \n", "2025-06-26   0.901884   0.460296  \n", "\n", "[622 rows x 12 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 23, "id": "ec088c0d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>121.242301</td>\n", "      <td>125.616812</td>\n", "      <td>129.991324</td>\n", "      <td>6.964850</td>\n", "      <td>0.827905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-20</th>\n", "      <td>193.777657</td>\n", "      <td>197.617999</td>\n", "      <td>201.458341</td>\n", "      <td>3.886632</td>\n", "      <td>0.940325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>193.976937</td>\n", "      <td>198.628000</td>\n", "      <td>203.279062</td>\n", "      <td>4.683189</td>\n", "      <td>0.808747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>194.180233</td>\n", "      <td>199.004001</td>\n", "      <td>203.827769</td>\n", "      <td>4.847911</td>\n", "      <td>0.634335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>196.468510</td>\n", "      <td>200.188000</td>\n", "      <td>203.907491</td>\n", "      <td>3.715997</td>\n", "      <td>0.684433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>200.165282</td>\n", "      <td>201.072000</td>\n", "      <td>201.978719</td>\n", "      <td>0.901884</td>\n", "      <td>0.460296</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>622 rows × 5 columns</p>\n", "</div>"], "text/plain": ["             BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  BBB_5_2.0  BBP_5_2.0\n", "Date                                                                \n", "2023-01-03         NaN         NaN         NaN        NaN        NaN\n", "2023-01-04         NaN         NaN         NaN        NaN        NaN\n", "2023-01-05         NaN         NaN         NaN        NaN        NaN\n", "2023-01-06         NaN         NaN         NaN        NaN        NaN\n", "2023-01-09  121.242301  125.616812  129.991324   6.964850   0.827905\n", "...                ...         ...         ...        ...        ...\n", "2025-06-20  193.777657  197.617999  201.458341   3.886632   0.940325\n", "2025-06-23  193.976937  198.628000  203.279062   4.683189   0.808747\n", "2025-06-24  194.180233  199.004001  203.827769   4.847911   0.634335\n", "2025-06-25  196.468510  200.188000  203.907491   3.715997   0.684433\n", "2025-06-26  200.165282  201.072000  201.978719   0.901884   0.460296\n", "\n", "[622 rows x 5 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.bbands(append=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "091188ab", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>rsi</th>\n", "      <th>RSI_14</th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>154.639999</td>\n", "      <td>155.839996</td>\n", "      <td>154.369995</td>\n", "      <td>154.919998</td>\n", "      <td>20423405</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>154.910004</td>\n", "      <td>156.360001</td>\n", "      <td>154.700195</td>\n", "      <td>155.990005</td>\n", "      <td>9445752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>155.985001</td>\n", "      <td>156.190002</td>\n", "      <td>152.679993</td>\n", "      <td>153.149902</td>\n", "      <td>12280244</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>153.149994</td>\n", "      <td>154.020004</td>\n", "      <td>152.695007</td>\n", "      <td>153.733505</td>\n", "      <td>10943346</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>153.740005</td>\n", "      <td>154.139999</td>\n", "      <td>153.229996</td>\n", "      <td>153.789993</td>\n", "      <td>7342717</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>152.288308</td>\n", "      <td>154.316681</td>\n", "      <td>156.345054</td>\n", "      <td>2.628845</td>\n", "      <td>0.370170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>140.669998</td>\n", "      <td>140.720001</td>\n", "      <td>139.940002</td>\n", "      <td>140.279999</td>\n", "      <td>7685803</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.853757</td>\n", "      <td>32.022693</td>\n", "      <td>137.967317</td>\n", "      <td>141.759000</td>\n", "      <td>145.550682</td>\n", "      <td>5.349477</td>\n", "      <td>0.304968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>140.145004</td>\n", "      <td>140.600006</td>\n", "      <td>140.035004</td>\n", "      <td>140.077896</td>\n", "      <td>6175166</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.607664</td>\n", "      <td>31.428617</td>\n", "      <td>139.773876</td>\n", "      <td>140.676578</td>\n", "      <td>141.579279</td>\n", "      <td>1.283371</td>\n", "      <td>0.168394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>140.070007</td>\n", "      <td>140.535004</td>\n", "      <td>139.600006</td>\n", "      <td>139.721893</td>\n", "      <td>8536199</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.170491</td>\n", "      <td>30.360166</td>\n", "      <td>139.360636</td>\n", "      <td>140.396957</td>\n", "      <td>141.433279</td>\n", "      <td>1.476274</td>\n", "      <td>0.174298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.720001</td>\n", "      <td>140.490005</td>\n", "      <td>139.445007</td>\n", "      <td>140.080002</td>\n", "      <td>13306374</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.827724</td>\n", "      <td>32.833747</td>\n", "      <td>139.544406</td>\n", "      <td>140.166959</td>\n", "      <td>140.789511</td>\n", "      <td>0.888301</td>\n", "      <td>0.430161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.846325</td>\n", "      <td>32.905376</td>\n", "      <td>139.687928</td>\n", "      <td>140.049957</td>\n", "      <td>140.411986</td>\n", "      <td>0.517000</td>\n", "      <td>0.555298</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "2022-09-08 09:30:00-04:00  154.639999  155.839996  154.369995  154.919998   \n", "2022-09-08 10:30:00-04:00  154.910004  156.360001  154.700195  155.990005   \n", "2022-09-08 11:30:00-04:00  155.985001  156.190002  152.679993  153.149902   \n", "2022-09-08 12:30:00-04:00  153.149994  154.020004  152.695007  153.733505   \n", "2022-09-08 13:30:00-04:00  153.740005  154.139999  153.229996  153.789993   \n", "...                               ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  140.669998  140.720001  139.940002  140.279999   \n", "2022-10-07 13:30:00-04:00  140.145004  140.600006  140.035004  140.077896   \n", "2022-10-07 14:30:00-04:00  140.070007  140.535004  139.600006  139.721893   \n", "2022-10-07 15:30:00-04:00  139.720001  140.490005  139.445007  140.080002   \n", "2022-10-07 16:00:00-04:00  140.089996  140.089996  140.089996  140.089996   \n", "\n", "                             Volume  Dividends  Stock Splits        rsi  \\\n", "2022-09-08 09:30:00-04:00  20423405          0             0        NaN   \n", "2022-09-08 10:30:00-04:00   9445752          0             0        NaN   \n", "2022-09-08 11:30:00-04:00  12280244          0             0        NaN   \n", "2022-09-08 12:30:00-04:00  10943346          0             0        NaN   \n", "2022-09-08 13:30:00-04:00   7342717          0             0        NaN   \n", "...                             ...        ...           ...        ...   \n", "2022-10-07 12:30:00-04:00   7685803          0             0  40.853757   \n", "2022-10-07 13:30:00-04:00   6175166          0             0  40.607664   \n", "2022-10-07 14:30:00-04:00   8536199          0             0  40.170491   \n", "2022-10-07 15:30:00-04:00  13306374          0             0  40.827724   \n", "2022-10-07 16:00:00-04:00         0          0             0  40.846325   \n", "\n", "                              RSI_14   BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  \\\n", "2022-09-08 09:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 10:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 11:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 12:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 13:30:00-04:00        NaN  152.288308  154.316681  156.345054   \n", "...                              ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  32.022693  137.967317  141.759000  145.550682   \n", "2022-10-07 13:30:00-04:00  31.428617  139.773876  140.676578  141.579279   \n", "2022-10-07 14:30:00-04:00  30.360166  139.360636  140.396957  141.433279   \n", "2022-10-07 15:30:00-04:00  32.833747  139.544406  140.166959  140.789511   \n", "2022-10-07 16:00:00-04:00  32.905376  139.687928  140.049957  140.411986   \n", "\n", "                           BBB_5_2.0  BBP_5_2.0  \n", "2022-09-08 09:30:00-04:00        NaN        NaN  \n", "2022-09-08 10:30:00-04:00        NaN        NaN  \n", "2022-09-08 11:30:00-04:00        NaN        NaN  \n", "2022-09-08 12:30:00-04:00        NaN        NaN  \n", "2022-09-08 13:30:00-04:00   2.628845   0.370170  \n", "...                              ...        ...  \n", "2022-10-07 12:30:00-04:00   5.349477   0.304968  \n", "2022-10-07 13:30:00-04:00   1.283371   0.168394  \n", "2022-10-07 14:30:00-04:00   1.476274   0.174298  \n", "2022-10-07 15:30:00-04:00   0.888301   0.430161  \n", "2022-10-07 16:00:00-04:00   0.517000   0.555298  \n", "\n", "[155 rows x 14 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "246dcd88", "metadata": {}, "source": ["## Column name formatting\n", "\n", "Pandas-ta has some flexibility with column names, see this dictionary from `core.py`\n", "\n", "```\n", "common_names = {\n", "    \"Date\": \"date\",\n", "    \"Time\": \"time\",\n", "    \"Timestamp\": \"timestamp\",\n", "    \"Datetime\": \"datetime\",\n", "    \"Open\": \"open\",\n", "    \"High\": \"high\",\n", "    \"Low\": \"low\",\n", "    \"Close\": \"close\",\n", "    \"Adj Close\": \"adj_close\",\n", "    \"Volume\": \"volume\",\n", "    \"Dividends\": \"dividends\",\n", "    \"Stock Splits\": \"split\",\n", "}\n", "```\n"]}, {"cell_type": "markdown", "id": "37435335", "metadata": {}, "source": ["### Chaining Indicators"]}, {"cell_type": "code", "execution_count": 24, "id": "62e2a482", "metadata": {}, "outputs": [{"data": {"text/plain": ["Date\n", "2023-01-03           NaN\n", "2023-01-04           NaN\n", "2023-01-05           NaN\n", "2023-01-06           NaN\n", "2023-01-09           NaN\n", "                 ...    \n", "2025-06-20    199.147900\n", "2025-06-23    199.502373\n", "2025-06-24    199.889669\n", "2025-06-25    200.241547\n", "2025-06-26    200.403538\n", "Name: test_EMA_10_OHLC4, Length: 622, dtype: float64"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# ohlc4 = (open + high + low + close)/4\n", "df.ta.ema(close=df.ta.ohlc4(), length=10, suffix=\"OHLC4\", prefix=\"test\", append = True)"]}, {"cell_type": "code", "execution_count": 28, "id": "0852fd16", "metadata": {}, "outputs": [{"data": {"text/plain": ["2022-09-08 09:30:00-04:00    154.942497\n", "2022-09-08 10:30:00-04:00    155.490051\n", "2022-09-08 11:30:00-04:00    154.501225\n", "2022-09-08 12:30:00-04:00    153.399628\n", "2022-09-08 13:30:00-04:00    153.724998\n", "                                ...    \n", "2022-10-07 12:30:00-04:00    140.402500\n", "2022-10-07 13:30:00-04:00    140.214478\n", "2022-10-07 14:30:00-04:00    139.981728\n", "2022-10-07 15:30:00-04:00    139.933754\n", "2022-10-07 16:00:00-04:00    140.089996\n", "Name: OHLC4, Length: 155, dtype: float64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.ohlc4()"]}, {"cell_type": "markdown", "id": "73bae70a", "metadata": {}, "source": ["## 3. Strategies"]}, {"cell_type": "code", "execution_count": 27, "id": "28c72213", "metadata": {}, "outputs": [], "source": ["df = df.ta.ticker(\"aapl\")"]}, {"cell_type": "code", "execution_count": 28, "id": "c50c2534", "metadata": {}, "outputs": [], "source": ["MyStrategy = ta.Strategy(\n", "    name=\"DCSMA10\",\n", "    ta=[\n", "        {\"kind\": \"ohlc4\"},\n", "        {\"kind\": \"sma\", \"length\": 10},\n", "        {\"kind\": \"donchian\", \"lower_length\": 10, \"upper_length\": 15},\n", "        {\"kind\": \"ema\", \"close\": \"SMA_10\", \"length\": 10, \"suffix\": \"OHLC4\"},\n", "    ]\n", ")\n", "\n", "# (2) Run the Strategy\n", "df.ta.strategy(MyStrategy)"]}, {"cell_type": "code", "execution_count": 29, "id": "7cad4929", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>OHLC4</th>\n", "      <th>SMA_10</th>\n", "      <th>DCL_10_15</th>\n", "      <th>DCM_10_15</th>\n", "      <th>DCU_10_15</th>\n", "      <th>EMA_10_OHLC4</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.100148</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.095038</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.088078</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.090144</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.092754</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>140.355003</td>\n", "      <td>148.928998</td>\n", "      <td>137.690002</td>\n", "      <td>149.114998</td>\n", "      <td>160.539993</td>\n", "      <td>152.575156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.402500</td>\n", "      <td>147.848999</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>151.715855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.215000</td>\n", "      <td>147.116998</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.879699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.999996</td>\n", "      <td>146.385997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.062662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>141.294998</td>\n", "      <td>145.351997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.206178</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits       OHLC4  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0    0.100148   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0    0.095038   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0    0.088078   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0    0.090144   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0    0.092754   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0  140.355003   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0  145.402500   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0  145.215000   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0  145.999996   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0  141.294998   \n", "\n", "                               SMA_10   DCL_10_15   DCM_10_15   DCU_10_15  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-15 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-16 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-17 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-18 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  148.928998  137.690002  149.114998  160.539993   \n", "2022-10-04 00:00:00-04:00  147.848999  137.690002  148.215004  158.740005   \n", "2022-10-05 00:00:00-04:00  147.116998  137.690002  148.215004  158.740005   \n", "2022-10-06 00:00:00-04:00  146.385997  137.690002  148.215004  158.740005   \n", "2022-10-07 00:00:00-04:00  145.351997  137.690002  148.215004  158.740005   \n", "\n", "                           EMA_10_OHLC4  \n", "Date                                     \n", "1980-12-12 00:00:00-05:00           NaN  \n", "1980-12-15 00:00:00-05:00           NaN  \n", "1980-12-16 00:00:00-05:00           NaN  \n", "1980-12-17 00:00:00-05:00           NaN  \n", "1980-12-18 00:00:00-05:00           NaN  \n", "...                                 ...  \n", "2022-10-03 00:00:00-04:00    152.575156  \n", "2022-10-04 00:00:00-04:00    151.715855  \n", "2022-10-05 00:00:00-04:00    150.879699  \n", "2022-10-06 00:00:00-04:00    150.062662  \n", "2022-10-07 00:00:00-04:00    149.206178  \n", "\n", "[10545 rows x 13 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "fc0f1ec6", "metadata": {}, "source": ["## All the indicators"]}, {"cell_type": "code", "execution_count": 8, "id": "c049cd26", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["131it [00:03, 34.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[i] Runtime: 3945.7338 ms (3.9457 s)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close_aapl</th>\n", "      <th>High_aapl</th>\n", "      <th>Low_aapl</th>\n", "      <th>Open_aapl</th>\n", "      <th>Volume_aapl</th>\n", "      <th>ABER_ZG_5_15</th>\n", "      <th>ABER_SG_5_15</th>\n", "      <th>ABER_XG_5_15</th>\n", "      <th>ABER_ATR_5_15</th>\n", "      <th>ACCBL_20</th>\n", "      <th>...</th>\n", "      <th>VIDYA_14</th>\n", "      <th>VTXP_14</th>\n", "      <th>VTXM_14</th>\n", "      <th>VWAP_D</th>\n", "      <th>VWMA_10</th>\n", "      <th>WCP</th>\n", "      <th>WILLR_14</th>\n", "      <th>WMA_10</th>\n", "      <th>ZL_EMA_10</th>\n", "      <th>ZS_30</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>123.470619</td>\n", "      <td>129.226060</td>\n", "      <td>122.582127</td>\n", "      <td>128.613993</td>\n", "      <td>112117500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>125.092935</td>\n", "      <td>NaN</td>\n", "      <td>124.687356</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>124.744118</td>\n", "      <td>127.014709</td>\n", "      <td>123.480487</td>\n", "      <td>125.267339</td>\n", "      <td>89113600</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>125.079771</td>\n", "      <td>NaN</td>\n", "      <td>124.995858</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>123.421249</td>\n", "      <td>126.136083</td>\n", "      <td>123.164580</td>\n", "      <td>125.504267</td>\n", "      <td>80962700</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>124.240637</td>\n", "      <td>NaN</td>\n", "      <td>124.035790</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>127.962418</td>\n", "      <td>128.623848</td>\n", "      <td>123.292909</td>\n", "      <td>124.398589</td>\n", "      <td>87754700</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>126.626391</td>\n", "      <td>NaN</td>\n", "      <td>126.960398</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>128.485641</td>\n", "      <td>131.703962</td>\n", "      <td>128.228972</td>\n", "      <td>128.801557</td>\n", "      <td>70790800</td>\n", "      <td>126.102519</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>129.472859</td>\n", "      <td>NaN</td>\n", "      <td>129.226054</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-20</th>\n", "      <td>201.000000</td>\n", "      <td>201.699997</td>\n", "      <td>196.860001</td>\n", "      <td>198.240005</td>\n", "      <td>96813500</td>\n", "      <td>197.614000</td>\n", "      <td>202.306473</td>\n", "      <td>192.921528</td>\n", "      <td>4.692473</td>\n", "      <td>190.765596</td>\n", "      <td>...</td>\n", "      <td>207.402320</td>\n", "      <td>0.967730</td>\n", "      <td>0.971031</td>\n", "      <td>199.853333</td>\n", "      <td>199.722914</td>\n", "      <td>200.139999</td>\n", "      <td>-46.911427</td>\n", "      <td>198.507817</td>\n", "      <td>197.936408</td>\n", "      <td>-0.278769</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>201.500000</td>\n", "      <td>202.300003</td>\n", "      <td>198.960007</td>\n", "      <td>201.630005</td>\n", "      <td>55814300</td>\n", "      <td>198.296668</td>\n", "      <td>202.898976</td>\n", "      <td>193.694361</td>\n", "      <td>4.602308</td>\n", "      <td>190.700066</td>\n", "      <td>...</td>\n", "      <td>207.396866</td>\n", "      <td>0.946304</td>\n", "      <td>0.964024</td>\n", "      <td>200.920003</td>\n", "      <td>199.551872</td>\n", "      <td>201.065002</td>\n", "      <td>-42.435150</td>\n", "      <td>198.887636</td>\n", "      <td>199.144334</td>\n", "      <td>-0.212184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>200.300003</td>\n", "      <td>203.440002</td>\n", "      <td>200.199997</td>\n", "      <td>202.589996</td>\n", "      <td>54064000</td>\n", "      <td>198.981335</td>\n", "      <td>203.492823</td>\n", "      <td>194.469848</td>\n", "      <td>4.511487</td>\n", "      <td>191.135068</td>\n", "      <td>...</td>\n", "      <td>207.298222</td>\n", "      <td>0.953819</td>\n", "      <td>0.973179</td>\n", "      <td>201.313334</td>\n", "      <td>199.372313</td>\n", "      <td>201.060001</td>\n", "      <td>-53.178186</td>\n", "      <td>199.093273</td>\n", "      <td>200.201729</td>\n", "      <td>-0.469761</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>201.559998</td>\n", "      <td>203.669998</td>\n", "      <td>200.619995</td>\n", "      <td>201.449997</td>\n", "      <td>39525700</td>\n", "      <td>200.088668</td>\n", "      <td>204.524056</td>\n", "      <td>195.653280</td>\n", "      <td>4.435388</td>\n", "      <td>191.320119</td>\n", "      <td>...</td>\n", "      <td>207.265320</td>\n", "      <td>0.934450</td>\n", "      <td>1.007384</td>\n", "      <td>201.949997</td>\n", "      <td>199.195232</td>\n", "      <td>201.852497</td>\n", "      <td>-40.622190</td>\n", "      <td>199.548909</td>\n", "      <td>201.354141</td>\n", "      <td>-0.161254</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>201.000000</td>\n", "      <td>202.639999</td>\n", "      <td>199.460007</td>\n", "      <td>201.429993</td>\n", "      <td>50799100</td>\n", "      <td>201.014000</td>\n", "      <td>205.365695</td>\n", "      <td>196.662306</td>\n", "      <td>4.351695</td>\n", "      <td>191.263646</td>\n", "      <td>...</td>\n", "      <td>207.254150</td>\n", "      <td>0.947330</td>\n", "      <td>0.999076</td>\n", "      <td>201.033335</td>\n", "      <td>199.420399</td>\n", "      <td>201.025002</td>\n", "      <td>-45.745685</td>\n", "      <td>199.922909</td>\n", "      <td>201.289752</td>\n", "      <td>-0.215061</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>622 rows × 283 columns</p>\n", "</div>"], "text/plain": ["            Close_aapl   High_aapl    Low_aapl   Open_aapl  Volume_aapl  \\\n", "Date                                                                      \n", "2023-01-03  123.470619  129.226060  122.582127  128.613993    112117500   \n", "2023-01-04  124.744118  127.014709  123.480487  125.267339     89113600   \n", "2023-01-05  123.421249  126.136083  123.164580  125.504267     80962700   \n", "2023-01-06  127.962418  128.623848  123.292909  124.398589     87754700   \n", "2023-01-09  128.485641  131.703962  128.228972  128.801557     70790800   \n", "...                ...         ...         ...         ...          ...   \n", "2025-06-20  201.000000  201.699997  196.860001  198.240005     96813500   \n", "2025-06-23  201.500000  202.300003  198.960007  201.630005     55814300   \n", "2025-06-24  200.300003  203.440002  200.199997  202.589996     54064000   \n", "2025-06-25  201.559998  203.669998  200.619995  201.449997     39525700   \n", "2025-06-26  201.000000  202.639999  199.460007  201.429993     50799100   \n", "\n", "            ABER_ZG_5_15  ABER_SG_5_15  ABER_XG_5_15  ABER_ATR_5_15  \\\n", "Date                                                                  \n", "2023-01-03           NaN           NaN           NaN            NaN   \n", "2023-01-04           NaN           NaN           NaN            NaN   \n", "2023-01-05           NaN           NaN           NaN            NaN   \n", "2023-01-06           NaN           NaN           NaN            NaN   \n", "2023-01-09    126.102519           NaN           NaN            NaN   \n", "...                  ...           ...           ...            ...   \n", "2025-06-20    197.614000    202.306473    192.921528       4.692473   \n", "2025-06-23    198.296668    202.898976    193.694361       4.602308   \n", "2025-06-24    198.981335    203.492823    194.469848       4.511487   \n", "2025-06-25    200.088668    204.524056    195.653280       4.435388   \n", "2025-06-26    201.014000    205.365695    196.662306       4.351695   \n", "\n", "              ACCBL_20  ...    VIDYA_14   VTXP_14   VTXM_14      VWAP_D  \\\n", "Date                    ...                                               \n", "2023-01-03         NaN  ...         NaN       NaN       NaN  125.092935   \n", "2023-01-04         NaN  ...         NaN       NaN       NaN  125.079771   \n", "2023-01-05         NaN  ...         NaN       NaN       NaN  124.240637   \n", "2023-01-06         NaN  ...         NaN       NaN       NaN  126.626391   \n", "2023-01-09         NaN  ...         NaN       NaN       NaN  129.472859   \n", "...                ...  ...         ...       ...       ...         ...   \n", "2025-06-20  190.765596  ...  207.402320  0.967730  0.971031  199.853333   \n", "2025-06-23  190.700066  ...  207.396866  0.946304  0.964024  200.920003   \n", "2025-06-24  191.135068  ...  207.298222  0.953819  0.973179  201.313334   \n", "2025-06-25  191.320119  ...  207.265320  0.934450  1.007384  201.949997   \n", "2025-06-26  191.263646  ...  207.254150  0.947330  0.999076  201.033335   \n", "\n", "               VWMA_10         WCP   WILLR_14      WMA_10   ZL_EMA_10  \\\n", "Date                                                                    \n", "2023-01-03         NaN  124.687356        NaN         NaN         NaN   \n", "2023-01-04         NaN  124.995858        NaN         NaN         NaN   \n", "2023-01-05         NaN  124.035790        NaN         NaN         NaN   \n", "2023-01-06         NaN  126.960398        NaN         NaN         NaN   \n", "2023-01-09         NaN  129.226054        NaN         NaN         NaN   \n", "...                ...         ...        ...         ...         ...   \n", "2025-06-20  199.722914  200.139999 -46.911427  198.507817  197.936408   \n", "2025-06-23  199.551872  201.065002 -42.435150  198.887636  199.144334   \n", "2025-06-24  199.372313  201.060001 -53.178186  199.093273  200.201729   \n", "2025-06-25  199.195232  201.852497 -40.622190  199.548909  201.354141   \n", "2025-06-26  199.420399  201.025002 -45.745685  199.922909  201.289752   \n", "\n", "               ZS_30  \n", "Date                  \n", "2023-01-03       NaN  \n", "2023-01-04       NaN  \n", "2023-01-05       NaN  \n", "2023-01-06       NaN  \n", "2023-01-09       NaN  \n", "...              ...  \n", "2025-06-20 -0.278769  \n", "2025-06-23 -0.212184  \n", "2025-06-24 -0.469761  \n", "2025-06-25 -0.161254  \n", "2025-06-26 -0.215061  \n", "\n", "[622 rows x 283 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.cores = 6\n", "df.ta.strategy(\"All\", timed = True)\n", "df"]}, {"cell_type": "code", "execution_count": 5, "id": "178c3beb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Close_aapl', 'High_aapl', 'Low_aapl', 'Open_aapl', 'Volume_aapl',\n", "       'SMA_20', 'RSI_14', 'BBL_5_2.0', 'BBM_5_2.0', 'BBU_5_2.0',\n", "       ...\n", "       'VIDYA_14', 'VTXP_14', 'VTXM_14', 'VWAP_D', 'VWMA_10', 'WCP',\n", "       'WILLR_14', 'WMA_10', 'ZL_EMA_10', 'ZS_30'],\n", "      dtype='object', length=284)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "markdown", "id": "3964fbdd", "metadata": {}, "source": ["## Custom Column names\n", "(No multi-processing)"]}, {"cell_type": "code", "execution_count": 5, "id": "37464d65", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>CDL_2CROWS</th>\n", "      <th>CDL_3BLACKCROWS</th>\n", "      <th>CDL_3INSIDE</th>\n", "      <th>...</th>\n", "      <th>CDL_UPSIDEGAP2CROWS</th>\n", "      <th>CDL_XSIDEGAP3METHODS</th>\n", "      <th>open_Z_30_1</th>\n", "      <th>high_Z_30_1</th>\n", "      <th>low_Z_30_1</th>\n", "      <th>close_Z_30_1</th>\n", "      <th>HA_open</th>\n", "      <th>HA_high</th>\n", "      <th>HA_low</th>\n", "      <th>HA_close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100094</td>\n", "      <td>0.100094</td>\n", "      <td>0.094820</td>\n", "      <td>0.095038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097566</td>\n", "      <td>0.097566</td>\n", "      <td>0.087861</td>\n", "      <td>0.088078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092822</td>\n", "      <td>0.092822</td>\n", "      <td>0.090035</td>\n", "      <td>0.090144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.091483</td>\n", "      <td>0.093081</td>\n", "      <td>0.091483</td>\n", "      <td>0.092754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.308389</td>\n", "      <td>-2.064321</td>\n", "      <td>-2.081569</td>\n", "      <td>-1.798987</td>\n", "      <td>143.589255</td>\n", "      <td>143.589255</td>\n", "      <td>137.690002</td>\n", "      <td>140.355003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.354988</td>\n", "      <td>-1.529249</td>\n", "      <td>-1.167332</td>\n", "      <td>-1.229065</td>\n", "      <td>141.972129</td>\n", "      <td>146.220001</td>\n", "      <td>141.972129</td>\n", "      <td>145.402500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.392314</td>\n", "      <td>-1.290141</td>\n", "      <td>-1.269122</td>\n", "      <td>-1.125067</td>\n", "      <td>143.687315</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>145.215000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.099441</td>\n", "      <td>-1.198773</td>\n", "      <td>-0.916395</td>\n", "      <td>-1.207937</td>\n", "      <td>144.451157</td>\n", "      <td>147.539993</td>\n", "      <td>144.451157</td>\n", "      <td>145.999996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-100.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.483835</td>\n", "      <td>-1.759172</td>\n", "      <td>-1.719395</td>\n", "      <td>-1.951891</td>\n", "      <td>145.225577</td>\n", "      <td>145.225577</td>\n", "      <td>139.449997</td>\n", "      <td>141.294998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 77 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  CDL_2CROWS  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0         0.0   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0         0.0   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0         0.0   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0         0.0   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0         0.0   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0         0.0   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0         0.0   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0         0.0   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0         0.0   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0         0.0   \n", "\n", "                           CDL_3BLACKCROWS  CDL_3INSIDE  ...  \\\n", "Date                                                     ...   \n", "1980-12-12 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-15 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-16 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-17 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-18 00:00:00-05:00              0.0          0.0  ...   \n", "...                                    ...          ...  ...   \n", "2022-10-03 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-04 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-05 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-06 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-07 00:00:00-04:00              0.0       -100.0  ...   \n", "\n", "                           CDL_UPSIDEGAP2CROWS  CDL_XSIDEGAP3METHODS  \\\n", "Date                                                                   \n", "1980-12-12 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-15 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-16 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-17 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-18 00:00:00-05:00                  0.0                   0.0   \n", "...                                        ...                   ...   \n", "2022-10-03 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-04 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-05 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-06 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-07 00:00:00-04:00                  0.0                   0.0   \n", "\n", "                           open_Z_30_1  high_Z_30_1  low_Z_30_1  close_Z_30_1  \\\n", "Date                                                                            \n", "1980-12-12 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-15 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-16 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-17 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-18 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "...                                ...          ...         ...           ...   \n", "2022-10-03 00:00:00-04:00    -2.308389    -2.064321   -2.081569     -1.798987   \n", "2022-10-04 00:00:00-04:00    -1.354988    -1.529249   -1.167332     -1.229065   \n", "2022-10-05 00:00:00-04:00    -1.392314    -1.290141   -1.269122     -1.125067   \n", "2022-10-06 00:00:00-04:00    -1.099441    -1.198773   -0.916395     -1.207937   \n", "2022-10-07 00:00:00-04:00    -1.483835    -1.759172   -1.719395     -1.951891   \n", "\n", "                              HA_open     HA_high      HA_low    HA_close  \n", "Date                                                                       \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100148  \n", "1980-12-15 00:00:00-05:00    0.100094    0.100094    0.094820    0.095038  \n", "1980-12-16 00:00:00-05:00    0.097566    0.097566    0.087861    0.088078  \n", "1980-12-17 00:00:00-05:00    0.092822    0.092822    0.090035    0.090144  \n", "1980-12-18 00:00:00-05:00    0.091483    0.093081    0.091483    0.092754  \n", "...                               ...         ...         ...         ...  \n", "2022-10-03 00:00:00-04:00  143.589255  143.589255  137.690002  140.355003  \n", "2022-10-04 00:00:00-04:00  141.972129  146.220001  141.972129  145.402500  \n", "2022-10-05 00:00:00-04:00  143.687315  147.380005  143.009995  145.215000  \n", "2022-10-06 00:00:00-04:00  144.451157  147.539993  144.451157  145.999996  \n", "2022-10-07 00:00:00-04:00  145.225577  145.225577  139.449997  141.294998  \n", "\n", "[10545 rows x 77 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "df.ta.strategy(\"candles\")\n", "df"]}, {"cell_type": "code", "execution_count": 35, "id": "02f7ebb0", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>OHLC4</th>\n", "      <th>SMA</th>\n", "      <th>DLC</th>\n", "      <th>DCM</th>\n", "      <th>DCU</th>\n", "      <th>EMA2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.100148</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.095038</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.088078</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.090144</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.092754</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>140.355003</td>\n", "      <td>152.086998</td>\n", "      <td>137.690002</td>\n", "      <td>149.114998</td>\n", "      <td>160.539993</td>\n", "      <td>151.337411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.402500</td>\n", "      <td>151.665498</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.772181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.215000</td>\n", "      <td>151.187498</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.242926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.999996</td>\n", "      <td>150.735997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.838837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>141.294998</td>\n", "      <td>149.871997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.025138</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits       OHLC4  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0    0.100148   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0    0.095038   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0    0.088078   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0    0.090144   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0    0.092754   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0  140.355003   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0  145.402500   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0  145.215000   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0  145.999996   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0  141.294998   \n", "\n", "                                  SMA         DLC         DCM         DCU  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-15 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-16 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-17 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-18 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  152.086998  137.690002  149.114998  160.539993   \n", "2022-10-04 00:00:00-04:00  151.665498  137.690002  148.215004  158.740005   \n", "2022-10-05 00:00:00-04:00  151.187498  137.690002  148.215004  158.740005   \n", "2022-10-06 00:00:00-04:00  150.735997  137.690002  148.215004  158.740005   \n", "2022-10-07 00:00:00-04:00  149.871997  137.690002  148.215004  158.740005   \n", "\n", "                                 EMA2  \n", "Date                                   \n", "1980-12-12 00:00:00-05:00         NaN  \n", "1980-12-15 00:00:00-05:00         NaN  \n", "1980-12-16 00:00:00-05:00         NaN  \n", "1980-12-17 00:00:00-05:00         NaN  \n", "1980-12-18 00:00:00-05:00         NaN  \n", "...                               ...  \n", "2022-10-03 00:00:00-04:00  151.337411  \n", "2022-10-04 00:00:00-04:00  150.772181  \n", "2022-10-05 00:00:00-04:00  150.242926  \n", "2022-10-06 00:00:00-04:00  149.838837  \n", "2022-10-07 00:00:00-04:00  149.025138  \n", "\n", "[10545 rows x 13 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "MyStrategy = ta.Strategy(\n", "    name=\"DCSMA10\",\n", "    ta=[\n", "        {\"kind\": \"ohlc4\"},\n", "        {\"kind\": \"sma\", \"length\": 10, \"col_names\":(\"SMA\",)},\n", "        {\"kind\": \"donchian\", \"lower_length\": 10, \"upper_length\": 15, \"col_names\":(\"DLC\",\"DCM\",\"DCU\")},\n", "        {\"kind\": \"ema\", \"close\": \"OHLC4\", \"length\": 10, \"suffix\": \"OHLC4\", \"col_names\":(\"EMA2\",)},\n", "    ]\n", ")\n", "\n", "# (2) Run the Strategy\n", "df.ta.strategy(MyStrategy, length= 20)\n", "df"]}, {"cell_type": "code", "execution_count": 37, "id": "c8b8c134", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>CDL_2CROWS</th>\n", "      <th>CDL_3BLACKCROWS</th>\n", "      <th>CDL_3INSIDE</th>\n", "      <th>...</th>\n", "      <th>CDL_UPSIDEGAP2CROWS</th>\n", "      <th>CDL_XSIDEGAP3METHODS</th>\n", "      <th>open_Z_30_1</th>\n", "      <th>high_Z_30_1</th>\n", "      <th>low_Z_30_1</th>\n", "      <th>close_Z_30_1</th>\n", "      <th>HA_open</th>\n", "      <th>HA_high</th>\n", "      <th>HA_low</th>\n", "      <th>HA_close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100094</td>\n", "      <td>0.100094</td>\n", "      <td>0.094820</td>\n", "      <td>0.095038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097566</td>\n", "      <td>0.097566</td>\n", "      <td>0.087861</td>\n", "      <td>0.088078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092822</td>\n", "      <td>0.092822</td>\n", "      <td>0.090035</td>\n", "      <td>0.090144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.091483</td>\n", "      <td>0.093081</td>\n", "      <td>0.091483</td>\n", "      <td>0.092754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.308389</td>\n", "      <td>-2.064321</td>\n", "      <td>-2.081569</td>\n", "      <td>-1.798987</td>\n", "      <td>143.589255</td>\n", "      <td>143.589255</td>\n", "      <td>137.690002</td>\n", "      <td>140.355003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.354988</td>\n", "      <td>-1.529249</td>\n", "      <td>-1.167332</td>\n", "      <td>-1.229065</td>\n", "      <td>141.972129</td>\n", "      <td>146.220001</td>\n", "      <td>141.972129</td>\n", "      <td>145.402500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.392314</td>\n", "      <td>-1.290141</td>\n", "      <td>-1.269122</td>\n", "      <td>-1.125067</td>\n", "      <td>143.687315</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>145.215000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.099441</td>\n", "      <td>-1.198773</td>\n", "      <td>-0.916395</td>\n", "      <td>-1.207937</td>\n", "      <td>144.451157</td>\n", "      <td>147.539993</td>\n", "      <td>144.451157</td>\n", "      <td>145.999996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-100.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.483835</td>\n", "      <td>-1.759172</td>\n", "      <td>-1.719395</td>\n", "      <td>-1.951891</td>\n", "      <td>145.225577</td>\n", "      <td>145.225577</td>\n", "      <td>139.449997</td>\n", "      <td>141.294998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 77 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  CDL_2CROWS  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0         0.0   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0         0.0   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0         0.0   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0         0.0   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0         0.0   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0         0.0   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0         0.0   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0         0.0   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0         0.0   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0         0.0   \n", "\n", "                           CDL_3BLACKCROWS  CDL_3INSIDE  ...  \\\n", "Date                                                     ...   \n", "1980-12-12 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-15 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-16 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-17 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-18 00:00:00-05:00              0.0          0.0  ...   \n", "...                                    ...          ...  ...   \n", "2022-10-03 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-04 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-05 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-06 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-07 00:00:00-04:00              0.0       -100.0  ...   \n", "\n", "                           CDL_UPSIDEGAP2CROWS  CDL_XSIDEGAP3METHODS  \\\n", "Date                                                                   \n", "1980-12-12 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-15 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-16 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-17 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-18 00:00:00-05:00                  0.0                   0.0   \n", "...                                        ...                   ...   \n", "2022-10-03 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-04 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-05 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-06 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-07 00:00:00-04:00                  0.0                   0.0   \n", "\n", "                           open_Z_30_1  high_Z_30_1  low_Z_30_1  close_Z_30_1  \\\n", "Date                                                                            \n", "1980-12-12 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-15 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-16 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-17 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-18 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "...                                ...          ...         ...           ...   \n", "2022-10-03 00:00:00-04:00    -2.308389    -2.064321   -2.081569     -1.798987   \n", "2022-10-04 00:00:00-04:00    -1.354988    -1.529249   -1.167332     -1.229065   \n", "2022-10-05 00:00:00-04:00    -1.392314    -1.290141   -1.269122     -1.125067   \n", "2022-10-06 00:00:00-04:00    -1.099441    -1.198773   -0.916395     -1.207937   \n", "2022-10-07 00:00:00-04:00    -1.483835    -1.759172   -1.719395     -1.951891   \n", "\n", "                              HA_open     HA_high      HA_low    HA_close  \n", "Date                                                                       \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100148  \n", "1980-12-15 00:00:00-05:00    0.100094    0.100094    0.094820    0.095038  \n", "1980-12-16 00:00:00-05:00    0.097566    0.097566    0.087861    0.088078  \n", "1980-12-17 00:00:00-05:00    0.092822    0.092822    0.090035    0.090144  \n", "1980-12-18 00:00:00-05:00    0.091483    0.093081    0.091483    0.092754  \n", "...                               ...         ...         ...         ...  \n", "2022-10-03 00:00:00-04:00  143.589255  143.589255  137.690002  140.355003  \n", "2022-10-04 00:00:00-04:00  141.972129  146.220001  141.972129  145.402500  \n", "2022-10-05 00:00:00-04:00  143.687315  147.380005  143.009995  145.215000  \n", "2022-10-06 00:00:00-04:00  144.451157  147.539993  144.451157  145.999996  \n", "2022-10-07 00:00:00-04:00  145.225577  145.225577  139.449997  141.294998  \n", "\n", "[10545 rows x 77 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "df.ta.strategy(\"candles\")\n", "df"]}, {"cell_type": "code", "execution_count": 9, "id": "66fb6642", "metadata": {}, "outputs": [{"data": {"text/plain": ["['candles',\n", " 'cycles',\n", " 'momentum',\n", " 'overlap',\n", " 'performance',\n", " 'statistics',\n", " 'trend',\n", " 'volatility',\n", " 'volume']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.categories"]}, {"cell_type": "code", "execution_count": null, "id": "9bfdf587", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 5}