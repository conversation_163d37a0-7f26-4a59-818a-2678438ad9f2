"""
Test Strategy 'All' Method

This script specifically tests the 'all' method that should include ALL indicators and candle patterns.
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from datetime import datetime, timedelta
import logging
from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_simple_test_data(num_candles=150):
    """
    Create simple test data for strategy all testing
    """
    logger.info(f"📊 Creating {num_candles} candles of test data")
    
    # Create time series
    start_time = datetime(2025, 6, 24, 9, 15)
    times = [start_time + timedelta(minutes=i * 5) for i in range(num_candles)]
    
    # Create simple trending data
    np.random.seed(42)
    base_price = 1200
    
    # Simple upward trend with noise
    trend = np.linspace(0, 100, num_candles)
    noise = np.random.normal(0, 5, num_candles)
    close_prices = base_price + trend + noise
    
    # Generate OHLC data
    data = []
    for i, close in enumerate(close_prices):
        open_price = close_prices[i-1] if i > 0 else base_price
        
        # Simple high/low calculation
        volatility = abs(np.random.normal(0, 3))
        high = max(open_price, close) + volatility
        low = min(open_price, close) - volatility
        
        # Simple volume
        volume = int(2000 + np.random.normal(0, 500))
        volume = max(volume, 500)
        
        data.append({
            'time': times[i],
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"✅ Created test data: {len(df)} candles from {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
    return df

def test_strategy_all_method():
    """
    Test the 'all' method that should include ALL indicators and candle patterns
    """
    print("🌟 TESTING 'ALL' METHOD (Strategy All)")
    print("=" * 80)
    
    # Create test data
    market_data = create_simple_test_data(num_candles=150)
    
    # Test times
    test_times = ['11:00', '13:00', '15:00']
    
    print(f"📊 Testing 'all' method with {len(test_times)} time periods: {test_times}")
    print("-" * 60)
    
    # Initialize calculator
    calculator = CompleteProgressiveIndicatorsCalculator()
    
    # Test 'all' method
    progressive_results = calculator.calculate_all_indicators_progressive(
        market_data, test_times, interval_minutes=5, categories=None, method='all'
    )
    
    if progressive_results:
        print(f"✅ Strategy 'all' method executed successfully!")
        
        # Analyze results
        all_indicators = set()
        for time_str, result_data in progressive_results.items():
            all_indicators.update(result_data['indicators'].keys())
        
        # Remove price-based indicators for analysis
        analysis_indicators = {ind for ind in all_indicators 
                             if ind not in ['CURRENT_PRICE', 'PRICE_CHANGE', 'PRICE_CHANGE_PCT', 'ERROR']}
        
        # Categorize indicators by type
        momentum_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['RSI', 'MACD', 'MOM', 'ROC', 'STOCH', 'CCI', 'CMO', 'WILLR'])}
        overlap_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['SMA', 'EMA', 'DEMA', 'TEMA', 'WMA', 'HMA', 'BBANDS', 'KC', 'DONCHIAN'])}
        volume_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['OBV', 'MFI', 'AD', 'CMF', 'EOM', 'PVT', 'AOBV'])}
        volatility_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['ATR', 'NATR', 'TRUE_RANGE', 'UI', 'ABERRATION'])}
        trend_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['ADX', 'AROON', 'PSAR', 'VORTEX', 'CHOP', 'VHF'])}
        statistics_indicators = {ind for ind in analysis_indicators if any(x in ind.upper() for x in ['ENTROPY', 'KURTOSIS', 'SKEW', 'STDEV', 'VARIANCE', 'ZSCORE'])}
        candle_indicators = {ind for ind in analysis_indicators if 'CDL' in ind.upper()}
        
        print(f"\n📊 STRATEGY 'ALL' RESULTS:")
        print(f"   Total indicators: {len(analysis_indicators)}")
        print(f"   Momentum indicators: {len(momentum_indicators)}")
        print(f"   Overlap indicators: {len(overlap_indicators)}")
        print(f"   Volume indicators: {len(volume_indicators)}")
        print(f"   Volatility indicators: {len(volatility_indicators)}")
        print(f"   Trend indicators: {len(trend_indicators)}")
        print(f"   Statistics indicators: {len(statistics_indicators)}")
        print(f"   Candle patterns: {len(candle_indicators)}")
        
        # Test variation in key indicators
        key_indicators_to_test = [
            ('RSI', 'RSI_14'),
            ('SMA', 'SMA_10'),
            ('EMA', 'EMA_10'),
            ('MACD', 'MACD_12_26_9'),
            ('ATR', 'ATR_14'),
            ('OBV', 'OBV'),
            ('ADX', 'ADX_14')
        ]
        
        print(f"\n🔍 KEY INDICATORS VARIATION TEST:")
        variation_count = 0
        
        for indicator_type, indicator_pattern in key_indicators_to_test:
            # Find matching indicators
            matching_indicators = [ind for ind in analysis_indicators if indicator_pattern in ind]
            
            if matching_indicators:
                test_indicator = matching_indicators[0]
                values = []
                
                for time_str in test_times:
                    if time_str in progressive_results:
                        indicators = progressive_results[time_str]['indicators']
                        if test_indicator in indicators:
                            value = indicators[test_indicator]
                            if value is not None and not pd.isna(value):
                                values.append(float(value))
                
                if len(values) >= 2:
                    unique_values = len(set([round(v, 6) for v in values]))
                    if unique_values > 1:
                        print(f"   ✅ {indicator_type}: Different values - {[f'{v:.3f}' for v in values]}")
                        variation_count += 1
                    else:
                        print(f"   ❌ {indicator_type}: Same values - {values[0]:.3f}")
                else:
                    print(f"   ⚠️ {indicator_type}: Insufficient data")
            else:
                print(f"   ❌ {indicator_type}: Not found")
        
        # Summary
        total_key_indicators = len(key_indicators_to_test)
        success_rate = (variation_count / total_key_indicators) * 100 if total_key_indicators > 0 else 0
        
        print(f"\n📈 KEY INDICATORS SUMMARY:")
        print(f"   Indicators tested: {total_key_indicators}")
        print(f"   With variation: {variation_count}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print(f"   🎯 EXCELLENT: Strategy 'all' method working perfectly!")
        elif success_rate >= 60:
            print(f"   ✅ GOOD: Strategy 'all' method working well")
        else:
            print(f"   ⚠️ NEEDS IMPROVEMENT: Strategy 'all' method needs refinement")
        
        # Show sample of each category
        print(f"\n📋 SAMPLE INDICATORS BY CATEGORY:")
        categories = [
            ("Momentum", momentum_indicators),
            ("Overlap", overlap_indicators),
            ("Volume", volume_indicators),
            ("Volatility", volatility_indicators),
            ("Trend", trend_indicators),
            ("Statistics", statistics_indicators)
        ]
        
        for category_name, category_indicators in categories:
            if category_indicators:
                sample_indicators = sorted(list(category_indicators))[:5]  # Show first 5
                print(f"   {category_name}: {', '.join(sample_indicators)}")
            else:
                print(f"   {category_name}: None found")
        
        if candle_indicators:
            sample_candles = sorted(list(candle_indicators))[:5]
            print(f"   Candle Patterns: {', '.join(sample_candles)}")
        
    else:
        print(f"❌ Strategy 'all' method failed - no results returned")
    
    return progressive_results

def test_integrated_analyzer_all_method():
    """
    Test the integrated analyzer with 'all' method
    """
    print(f"\n\n🔧 TESTING INTEGRATED ANALYZER WITH 'ALL' METHOD")
    print("=" * 80)
    
    try:
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        # Create analyzer
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Create test data and save to simulate API data
        market_data = create_simple_test_data(num_candles=150)
        
        # Test the complete calculator directly
        progressive_results = analyzer.complete_calculator.calculate_all_indicators_progressive(
            market_data, ['11:00', '13:00'], interval_minutes=5, categories=None, method='all'
        )
        
        if progressive_results:
            total_indicators = 0
            for time_str, result_data in progressive_results.items():
                indicators_count = len(result_data['indicators'])
                total_indicators = max(total_indicators, indicators_count)
                print(f"   🕐 {time_str}: {indicators_count} indicators calculated")
            
            print(f"   📊 Maximum indicators per time period: {total_indicators}")
            
            if total_indicators > 100:
                print(f"   🎯 EXCELLENT: Integrated analyzer 'all' method working!")
            elif total_indicators > 50:
                print(f"   ✅ GOOD: Integrated analyzer working with many indicators")
            else:
                print(f"   ⚠️ LIMITED: Integrated analyzer working with few indicators")
        else:
            print(f"   ❌ Integrated analyzer 'all' method failed")
            
    except Exception as e:
        print(f"   ❌ Error testing integrated analyzer: {str(e)}")

def main():
    """Main test function"""
    try:
        print("🚀 STRATEGY 'ALL' METHOD COMPREHENSIVE TEST")
        print("=" * 80)
        print("Testing the 'all' method that should include ALL indicators and candle patterns.")
        print()
        
        # Test strategy all method
        results = test_strategy_all_method()
        
        # Test integrated analyzer
        test_integrated_analyzer_all_method()
        
        print(f"\n\n🎯 STRATEGY 'ALL' METHOD ASSESSMENT:")
        print("=" * 80)
        if results:
            print("✅ The 'all' method is working and calculating multiple indicators!")
            print("✅ Progressive calculation is producing different values per time period!")
            print("✅ Ready for professional use with comprehensive indicator coverage!")
        else:
            print("❌ The 'all' method needs debugging and refinement.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
