#!/usr/bin/env python3
"""
🔄 AUTOMATED TECHNICAL DATA FETCHER
Fetches technical indicator data for multiple dates and intervals automatically
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutomatedTechnicalDataFetcher:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.analyzer_script = self.base_dir / "integrated_technical_analyzer.py"
        
        # Default intervals to fetch
        self.default_intervals = ['1', '3', '5', '15', '30', '60']
        
        # Verify analyzer script exists
        if not self.analyzer_script.exists():
            raise FileNotFoundError(f"Technical analyzer script not found: {self.analyzer_script}")
    
    def get_user_inputs(self):
        """Get all required inputs from user"""
        print("🔄 AUTOMATED TECHNICAL DATA FETCHER")
        print("=" * 50)
        
        # Get basic parameters
        ticker = input("📊 Enter ticker symbol (e.g., NATURALGAS26AUG25): ").strip().upper()
        exchange = input("🏢 Enter exchange (MCX/NSE/BSE/NFO): ").strip().upper()
        
        # Get date parameters
        start_date_str = input("📅 Enter start date (DD-MM-YYYY, e.g., 14-07-2025): ").strip()
        num_days = int(input("📆 Enter number of trading days to fetch (excluding weekends): ").strip())
        
        # Get technical analysis parameters
        print("\n🔧 Technical Analysis Parameters:")
        method = input("🔧 Enter method (extension/direct_call/strategy_all) [default: extension]: ").strip() or "extension"
        
        print("📊 Available functions: pgo, cci, cg, accbands, qqe, smi, bias, rsi, macd, bbands, etc.")
        functions = input("📊 Enter functions (comma-separated) [default: pgo,cci,cg,accbands,qqe,smi,bias]: ").strip()
        if not functions:
            functions = "pgo,cci,cg,accbands,qqe,smi,bias"
        
        print("⏱️ Available intervals: 1, 3, 5, 15, 30, 60 minutes")
        intervals_input = input("⏱️ Enter intervals (comma-separated) [default: 1,3,5,15,30,60]: ").strip()
        if intervals_input:
            intervals = [x.strip() for x in intervals_input.split(',')]
        else:
            intervals = self.default_intervals
        
        # Parse start date
        try:
            start_date = datetime.strptime(start_date_str, "%d-%m-%Y")
        except ValueError:
            raise ValueError(f"Invalid date format: {start_date_str}. Use DD-MM-YYYY")
        
        return {
            'ticker': ticker,
            'exchange': exchange,
            'start_date': start_date,
            'num_days': num_days,
            'method': method,
            'functions': functions,
            'intervals': intervals
        }
    
    def get_trading_dates(self, start_date: datetime, num_days: int):
        """Generate list of trading dates (excluding weekends)"""
        trading_dates = []
        current_date = start_date
        
        while len(trading_dates) < num_days:
            # Check if it's a weekday (Monday=0, Sunday=6)
            if current_date.weekday() < 5:  # Monday to Friday
                trading_dates.append(current_date)
            
            # Move to previous day
            current_date -= timedelta(days=1)
        
        return trading_dates
    
    def create_output_folder(self, ticker: str):
        """Create organized folder structure"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"{ticker}_TechnicalData_{timestamp}"
        output_folder = self.base_dir / folder_name
        
        # Create folder
        output_folder.mkdir(exist_ok=True)
        
        logger.info(f"📁 Created output folder: {output_folder}")
        return output_folder
    
    def run_technical_analyzer(self, ticker: str, exchange: str, date: datetime, 
                             method: str, functions: str, interval: str, output_folder: Path):
        """Run the technical analyzer for specific parameters"""
        
        date_str = date.strftime("%d-%m-%Y")
        
        # Prepare command
        cmd = [
            sys.executable,
            str(self.analyzer_script),
            "--mode", "analysis",
            "--analysis-type", "signals",
            "--ticker", ticker,
            "--exchange", exchange,
            "--date", date_str,
            "--method", method,
            "--functions", functions,
            "--interval", interval
        ]
        
        logger.info(f"🔄 Running: {' '.join(cmd)}")
        
        try:
            # Run the command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.base_dir)
            
            if result.returncode == 0:
                logger.info(f"✅ Successfully processed {ticker} {exchange} {date_str} {interval}min")
                
                # Move generated files to organized folder
                self.organize_output_files(ticker, exchange, date, interval, output_folder)
                
                return True
            else:
                logger.error(f"❌ Error processing {ticker} {exchange} {date_str} {interval}min:")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Exception running analyzer: {str(e)}")
            return False
    
    def organize_output_files(self, ticker: str, exchange: str, date: datetime, 
                            interval: str, output_folder: Path):
        """Move and rename output files to organized structure"""
        
        date_str = date.strftime("%Y%m%d")
        
        # Common file patterns that might be generated
        file_patterns = [
            f"*{ticker}*",
            f"*technical*",
            f"*analysis*",
            f"*signals*",
            "*.xlsx",
            "*.csv",
            "*.json",
            "*.md"
        ]
        
        moved_files = []
        
        for pattern in file_patterns:
            for file_path in self.base_dir.glob(pattern):
                if file_path.is_file() and file_path.parent == self.base_dir:
                    # Create new filename with date and interval
                    new_name = f"{ticker}_{exchange}_{date_str}_{interval}min_{file_path.name}"
                    new_path = output_folder / new_name
                    
                    try:
                        # Move file
                        file_path.rename(new_path)
                        moved_files.append(new_path.name)
                        logger.debug(f"📁 Moved: {file_path.name} → {new_name}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not move {file_path.name}: {str(e)}")
        
        if moved_files:
            logger.info(f"📁 Organized {len(moved_files)} files for {date_str} {interval}min")
        
        return moved_files
    
    def run_automated_fetch(self):
        """Main function to run automated fetching"""
        try:
            # Get user inputs
            params = self.get_user_inputs()
            
            # Generate trading dates
            trading_dates = self.get_trading_dates(params['start_date'], params['num_days'])
            
            # Create output folder
            output_folder = self.create_output_folder(params['ticker'])
            
            # Summary
            print(f"\n📋 FETCH SUMMARY:")
            print(f"📊 Ticker: {params['ticker']}")
            print(f"🏢 Exchange: {params['exchange']}")
            print(f"📅 Trading dates: {len(trading_dates)} days")
            print(f"⏱️ Intervals: {', '.join(params['intervals'])}")
            print(f"🔧 Method: {params['method']}")
            print(f"📊 Functions: {params['functions']}")
            print(f"📁 Output folder: {output_folder.name}")
            
            # Confirm before starting
            confirm = input(f"\n🚀 Start fetching {len(trading_dates) * len(params['intervals'])} datasets? (y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ Fetch cancelled by user")
                return
            
            # Start fetching
            print(f"\n🔄 Starting automated fetch...")
            
            total_tasks = len(trading_dates) * len(params['intervals'])
            completed_tasks = 0
            failed_tasks = 0
            
            for date in trading_dates:
                date_str = date.strftime("%d-%m-%Y")
                print(f"\n📅 Processing date: {date_str}")
                
                for interval in params['intervals']:
                    print(f"  ⏱️ Interval: {interval} minutes")
                    
                    success = self.run_technical_analyzer(
                        params['ticker'],
                        params['exchange'],
                        date,
                        params['method'],
                        params['functions'],
                        interval,
                        output_folder
                    )
                    
                    if success:
                        completed_tasks += 1
                    else:
                        failed_tasks += 1
                    
                    # Progress update
                    progress = ((completed_tasks + failed_tasks) / total_tasks) * 100
                    print(f"    📊 Progress: {progress:.1f}% ({completed_tasks + failed_tasks}/{total_tasks})")
            
            # Final summary
            print(f"\n🎉 AUTOMATED FETCH COMPLETED!")
            print(f"✅ Successful: {completed_tasks}/{total_tasks}")
            print(f"❌ Failed: {failed_tasks}/{total_tasks}")
            print(f"📁 All files saved in: {output_folder}")
            
            # Create summary file
            self.create_summary_file(output_folder, params, trading_dates, completed_tasks, failed_tasks)
            
        except KeyboardInterrupt:
            print(f"\n⚠️ Fetch interrupted by user")
        except Exception as e:
            logger.error(f"❌ Error in automated fetch: {str(e)}")
            raise
    
    def create_summary_file(self, output_folder: Path, params: dict, 
                          trading_dates: list, completed: int, failed: int):
        """Create a summary file with fetch details"""
        
        summary_content = f"""# 📊 AUTOMATED TECHNICAL DATA FETCH SUMMARY

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📋 FETCH PARAMETERS
- **Ticker:** {params['ticker']}
- **Exchange:** {params['exchange']}
- **Method:** {params['method']}
- **Functions:** {params['functions']}
- **Intervals:** {', '.join(params['intervals'])} minutes

## 📅 TRADING DATES PROCESSED
Total dates: {len(trading_dates)}

"""
        
        for i, date in enumerate(trading_dates, 1):
            summary_content += f"{i}. {date.strftime('%d-%m-%Y (%A)')}\n"
        
        summary_content += f"""
## 📊 RESULTS SUMMARY
- **Total tasks:** {completed + failed}
- **Successful:** {completed}
- **Failed:** {failed}
- **Success rate:** {(completed/(completed+failed)*100):.1f}%

## 📁 OUTPUT ORGANIZATION
Files are organized with naming pattern:
`{{TICKER}}_{{EXCHANGE}}_{{YYYYMMDD}}_{{INTERVAL}}min_{{ORIGINAL_FILENAME}}`

Example: `{params['ticker']}_{params['exchange']}_20250714_1min_technical_analysis.xlsx`
"""
        
        summary_file = output_folder / "FETCH_SUMMARY.md"
        summary_file.write_text(summary_content, encoding='utf-8')
        
        logger.info(f"📄 Summary saved: {summary_file}")

def main():
    """Main entry point"""
    try:
        fetcher = AutomatedTechnicalDataFetcher()
        fetcher.run_automated_fetch()
    except Exception as e:
        logger.error(f"❌ Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
