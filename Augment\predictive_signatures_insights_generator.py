"""
Predictive Signatures Insights Generator

This script analyzes the results from the advanced predictive signatures analyzer
and generates actionable insights for traders and market analysts.

Key Features:
- Analyzes all 200+ technical indicators results
- Identifies the most predictive indicators for each event type
- Generates trading rules and signals
- Creates multi-timeframe analysis recommendations
- Provides institutional-level market insights
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

class PredictiveSignaturesInsightsGenerator:
    """
    Generate actionable insights from predictive signatures analysis
    """
    
    def __init__(self):
        """Initialize the insights generator"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.analysis_files = self._find_analysis_files()
        
        print("🧠 Predictive Signatures Insights Generator initialized")
        print(f"📁 Found {len(self.analysis_files)} analysis files")
    
    def _find_analysis_files(self) -> List[str]:
        """Find all analysis result files"""
        files = []
        for file in os.listdir(self.current_dir):
            if file.startswith('advanced_predictive_signatures_') and file.endswith('.json'):
                files.append(file)
        return sorted(files)
    
    def load_latest_analysis(self) -> Dict[str, Any]:
        """Load the latest analysis results"""
        if not self.analysis_files:
            print("❌ No analysis files found")
            return {}
        
        latest_file = self.analysis_files[-1]
        filepath = os.path.join(self.current_dir, latest_file)
        
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            print(f"✅ Loaded analysis from: {latest_file}")
            return data
        except Exception as e:
            print(f"❌ Error loading analysis file: {str(e)}")
            return {}
    
    def analyze_indicator_effectiveness(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze which indicators are most effective for predicting each event type
        """
        print("\n🔍 ANALYZING INDICATOR EFFECTIVENESS")
        print("=" * 60)
        
        indicator_scores = {}
        event_types = ['breakdown', 'breakout', 'falling_knife']
        
        for event_name, event_data in analysis_data.items():
            if not isinstance(event_data, dict):
                continue
            
            # Determine event type
            event_type = self._classify_event_type(event_name)
            if event_type not in indicator_scores:
                indicator_scores[event_type] = {}
            
            # Analyze each category
            categories = ['squeeze_analysis', 'trend_analysis', 'momentum_analysis', 'volume_analysis']
            
            for category in categories:
                if category in event_data:
                    category_data = event_data[category]
                    if isinstance(category_data, dict):
                        for indicator, indicator_data in category_data.items():
                            if indicator not in indicator_scores[event_type]:
                                indicator_scores[event_type][indicator] = []
                            
                            # Calculate effectiveness score for this indicator
                            effectiveness = self._calculate_indicator_effectiveness(indicator_data)
                            indicator_scores[event_type][indicator].append(effectiveness)
        
        # Calculate average effectiveness for each indicator per event type
        effectiveness_summary = {}
        for event_type, indicators in indicator_scores.items():
            effectiveness_summary[event_type] = {}
            for indicator, scores in indicators.items():
                if scores:
                    avg_score = np.mean(scores)
                    effectiveness_summary[event_type][indicator] = {
                        'avg_effectiveness': avg_score,
                        'consistency': 1.0 - np.std(scores) if len(scores) > 1 else 1.0,
                        'sample_size': len(scores)
                    }
        
        return effectiveness_summary
    
    def _classify_event_type(self, event_name: str) -> str:
        """Classify event type from event name"""
        if 'breakdown' in event_name.lower():
            return 'breakdown'
        elif 'breakout' in event_name.lower():
            return 'breakout'
        elif 'falling_knife' in event_name.lower():
            return 'falling_knife'
        else:
            return 'unknown'
    
    def _calculate_indicator_effectiveness(self, indicator_data: Any) -> float:
        """Calculate effectiveness score for an indicator"""
        if not isinstance(indicator_data, dict):
            return 0.0
        
        score = 0.0
        
        # Check for strong signals
        if 'squeeze_on' in str(indicator_data):
            score += 0.3
        if 'squeeze_off' in str(indicator_data):
            score += 0.5
        if 'release_signal' in indicator_data and indicator_data.get('release_signal'):
            score += 0.4
        if 'breakout_probability' in indicator_data:
            prob = indicator_data.get('breakout_probability', 0)
            score += prob * 0.01  # Convert percentage to 0-1 scale
        if 'trend_strength' in indicator_data:
            strength = indicator_data.get('trend_strength', 0)
            score += min(strength, 1.0) * 0.3
        
        return min(score, 1.0)  # Cap at 1.0
    
    def generate_trading_rules(self, effectiveness_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Generate specific trading rules based on indicator effectiveness
        """
        print("\n📋 GENERATING TRADING RULES")
        print("=" * 60)
        
        trading_rules = {}
        
        for event_type, indicators in effectiveness_data.items():
            rules = []
            
            # Sort indicators by effectiveness
            sorted_indicators = sorted(indicators.items(), 
                                     key=lambda x: x[1]['avg_effectiveness'], 
                                     reverse=True)
            
            # Get top 5 most effective indicators
            top_indicators = sorted_indicators[:5]
            
            if event_type == 'breakdown':
                rules.extend(self._generate_breakdown_rules(top_indicators))
            elif event_type == 'breakout':
                rules.extend(self._generate_breakout_rules(top_indicators))
            elif event_type == 'falling_knife':
                rules.extend(self._generate_falling_knife_rules(top_indicators))
            
            trading_rules[event_type] = rules
        
        return trading_rules
    
    def _generate_breakdown_rules(self, top_indicators: List[Tuple[str, Dict]]) -> List[str]:
        """Generate breakdown-specific trading rules"""
        rules = []
        
        rules.append("🔴 BREAKDOWN TRADING RULES:")
        rules.append("1. Wait for squeeze release (SQUEEZE_SQZ_OFF = 1 or SQZPRO_OFF = 1)")
        rules.append("2. Confirm with bearish momentum indicators (RSI declining, MACD histogram negative)")
        rules.append("3. Look for volume confirmation (increasing volume on breakdown)")
        rules.append("4. Entry: Break below recent support with volume")
        rules.append("5. Stop Loss: Above recent resistance or squeeze level")
        rules.append("6. Target: Next major support level or 2x ATR")
        
        # Add specific indicator rules
        for indicator, data in top_indicators:
            effectiveness = data['avg_effectiveness']
            if effectiveness > 0.6:
                if 'SQUEEZE' in indicator:
                    rules.append(f"   • High Priority: Monitor {indicator} (Effectiveness: {effectiveness:.2f})")
                elif 'RSI' in indicator:
                    rules.append(f"   • Momentum Confirm: {indicator} < 50 (Effectiveness: {effectiveness:.2f})")
                elif 'MACD' in indicator:
                    rules.append(f"   • Trend Confirm: {indicator} histogram negative (Effectiveness: {effectiveness:.2f})")
        
        return rules
    
    def _generate_breakout_rules(self, top_indicators: List[Tuple[str, Dict]]) -> List[str]:
        """Generate breakout-specific trading rules"""
        rules = []
        
        rules.append("🟢 BREAKOUT TRADING RULES:")
        rules.append("1. Identify squeeze conditions (low volatility, tight Bollinger Bands)")
        rules.append("2. Wait for squeeze release signal")
        rules.append("3. Confirm direction with momentum indicators")
        rules.append("4. Entry: Break above/below squeeze level with volume")
        rules.append("5. Stop Loss: Opposite side of squeeze range")
        rules.append("6. Target: Measured move equal to squeeze range")
        
        # Add specific indicator rules
        for indicator, data in top_indicators:
            effectiveness = data['avg_effectiveness']
            if effectiveness > 0.6:
                if 'SQUEEZE' in indicator:
                    rules.append(f"   • Primary Signal: {indicator} release (Effectiveness: {effectiveness:.2f})")
                elif 'BBANDS' in indicator:
                    rules.append(f"   • Setup Confirm: {indicator} contraction (Effectiveness: {effectiveness:.2f})")
                elif 'ATR' in indicator:
                    rules.append(f"   • Volatility Filter: {indicator} below average (Effectiveness: {effectiveness:.2f})")
        
        return rules
    
    def _generate_falling_knife_rules(self, top_indicators: List[Tuple[str, Dict]]) -> List[str]:
        """Generate falling knife-specific trading rules"""
        rules = []
        
        rules.append("🔪 FALLING KNIFE TRADING RULES:")
        rules.append("1. AVOID catching falling knives - wait for stabilization")
        rules.append("2. Look for oversold conditions in multiple timeframes")
        rules.append("3. Wait for momentum divergence signals")
        rules.append("4. Entry: Only after clear reversal pattern")
        rules.append("5. Stop Loss: Below recent low")
        rules.append("6. Target: Conservative - first resistance level")
        
        # Add specific indicator rules
        for indicator, data in top_indicators:
            effectiveness = data['avg_effectiveness']
            if effectiveness > 0.5:
                if 'RSI' in indicator:
                    rules.append(f"   • Oversold Signal: {indicator} < 30 (Effectiveness: {effectiveness:.2f})")
                elif 'STOCH' in indicator:
                    rules.append(f"   • Momentum Signal: {indicator} oversold (Effectiveness: {effectiveness:.2f})")
        
        return rules
    
    def generate_multi_timeframe_analysis(self) -> Dict[str, str]:
        """
        Generate multi-timeframe analysis recommendations
        """
        print("\n⏰ MULTI-TIMEFRAME ANALYSIS RECOMMENDATIONS")
        print("=" * 60)
        
        recommendations = {
            "1_minute": "Primary timeframe for entry signals and squeeze detection",
            "5_minute": "Trend confirmation and momentum validation",
            "15_minute": "Major support/resistance levels and trend direction",
            "60_minute": "Overall market structure and key levels",
            "daily": "Long-term trend and major reversal patterns"
        }
        
        analysis_steps = [
            "1. Start with daily chart to identify overall trend",
            "2. Use 60-minute for key support/resistance levels",
            "3. Switch to 15-minute for trend confirmation",
            "4. Use 5-minute for momentum validation",
            "5. Execute on 1-minute for precise entry/exit"
        ]
        
        return {
            "timeframe_roles": recommendations,
            "analysis_workflow": analysis_steps
        }
    
    def create_comprehensive_report(self) -> str:
        """
        Create a comprehensive insights report
        """
        print("\n📊 CREATING COMPREHENSIVE INSIGHTS REPORT")
        print("=" * 60)
        
        # Load analysis data
        analysis_data = self.load_latest_analysis()
        if not analysis_data:
            return "No analysis data available"
        
        # Generate insights
        effectiveness = self.analyze_indicator_effectiveness(analysis_data)
        trading_rules = self.generate_trading_rules(effectiveness)
        timeframe_analysis = self.generate_multi_timeframe_analysis()
        
        # Create report
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""
# PREDICTIVE SIGNATURES INSIGHTS REPORT
Generated: {timestamp}

## EXECUTIVE SUMMARY
This report analyzes 200+ technical indicators across multiple market events to identify
the most predictive signatures for breakouts, breakdowns, and trending moves.

## KEY FINDINGS

### Most Effective Indicators by Event Type:
"""
        
        for event_type, indicators in effectiveness.items():
            if indicators:
                report += f"\n#### {event_type.upper()} EVENTS:\n"
                sorted_indicators = sorted(indicators.items(), 
                                         key=lambda x: x[1]['avg_effectiveness'], 
                                         reverse=True)
                
                for i, (indicator, data) in enumerate(sorted_indicators[:5]):
                    effectiveness_score = data['avg_effectiveness']
                    consistency = data['consistency']
                    report += f"{i+1}. {indicator}: {effectiveness_score:.3f} (Consistency: {consistency:.3f})\n"
        
        report += "\n## TRADING RULES\n"
        for event_type, rules in trading_rules.items():
            report += f"\n### {event_type.upper()}\n"
            for rule in rules:
                report += f"{rule}\n"
        
        report += "\n## MULTI-TIMEFRAME ANALYSIS\n"
        report += "\n### Timeframe Roles:\n"
        for timeframe, role in timeframe_analysis["timeframe_roles"].items():
            report += f"- **{timeframe}**: {role}\n"
        
        report += "\n### Analysis Workflow:\n"
        for step in timeframe_analysis["analysis_workflow"]:
            report += f"{step}\n"
        
        report += """
## IMPLEMENTATION RECOMMENDATIONS

1. **Indicator Priority**: Focus on squeeze indicators (highest effectiveness)
2. **Confirmation Required**: Use multiple indicator categories for confirmation
3. **Risk Management**: Always use stop losses based on volatility measures
4. **Volume Confirmation**: Include volume analysis for all signals
5. **Market Context**: Consider overall market conditions and news events

## NEXT STEPS

1. Implement these rules in your trading system
2. Backtest on additional datasets
3. Monitor performance and adjust parameters
4. Consider machine learning models for pattern recognition
5. Develop automated alert systems for high-probability setups
"""
        
        # Save report
        report_filename = f"predictive_signatures_insights_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(os.path.join(self.current_dir, report_filename), 'w') as f:
            f.write(report)
        
        print(f"📄 Report saved to: {report_filename}")
        return report


def main():
    """
    Main execution function
    """
    print("🧠 Predictive Signatures Insights Generator")
    print("=" * 60)
    
    # Initialize generator
    generator = PredictiveSignaturesInsightsGenerator()
    
    # Create comprehensive report
    report = generator.create_comprehensive_report()
    
    if report and "No analysis data available" not in report:
        print("\n✅ Insights generation completed successfully!")
        print("📊 Comprehensive report with trading rules and recommendations generated.")
    else:
        print("\n❌ Insights generation failed. Please run the advanced analyzer first.")


if __name__ == "__main__":
    main()
