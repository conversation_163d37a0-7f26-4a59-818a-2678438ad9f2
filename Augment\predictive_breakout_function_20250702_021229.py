"""
Predictive Breakout Function
Generated: 2025-07-02 02:12:29.563184
"""

def predict_breakout_probability(df, timestamp, lookback_minutes=10):
    """
    Generalized function to predict breakout probability based on discovered patterns

    Args:
        df: DataFrame with technical indicators (indicators as columns, time as index)
        timestamp: Target timestamp to analyze
        lookback_minutes: Minutes to look back for analysis

    Returns:
        dict: Prediction results with probability score and reasoning
    """
    import pandas as pd
    from datetime import timedelta

    # Extract pre-event window
    target_time = pd.to_datetime(timestamp)
    start_time = target_time - timedelta(minutes=lookback_minutes)

    # Filter data
    mask = (df.index >= start_time) & (df.index < target_time)
    pre_event_data = df[mask]

    if len(pre_event_data) == 0:
        return {"probability": 0, "reason": "No data available"}

    probability_score = 0
    reasons = []

    # Check for discovered patterns

    # Check for Squeeze Release Pattern
    squeeze_indicators = [col for col in df.columns if 'SQUEEZE' in col.upper()]
    for indicator in squeeze_indicators:
        if indicator in pre_event_data.columns:
            values = pre_event_data[indicator].dropna()
            if len(values) > 0 and values.iloc[-1] == 1:  # Squeeze OFF
                probability_score += 25
                reasons.append("Squeeze release detected")

    # Normalize probability score
    probability_score = min(probability_score, 100)

    return {
        "probability": probability_score,
        "reasons": reasons,
        "interpretation": "HIGH" if probability_score > 60 else "MODERATE" if probability_score > 30 else "LOW"
    }
