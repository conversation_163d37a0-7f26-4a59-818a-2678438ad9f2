"""
Debug Active Stocks Finder - Diagnose scoring issues

This script debugs the active stocks finder to understand why no stocks are scoring well.
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from active_stocks_finder import ActiveStocksFinder, KNOWN_GOOD_STOCKS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_single_stock(ticker: str, date: str = "30-06-2025"):
    """Debug analysis for a single stock"""
    
    logger.info(f"🔍 Debugging {ticker} analysis...")
    
    finder = ActiveStocksFinder(date=date)
    
    # Get token info
    token_info = finder.get_token_info(ticker)
    if not token_info:
        logger.error(f"❌ Could not get token info for {ticker}")
        return None
    
    logger.info(f"✅ Token info: {token_info}")
    
    # Fetch data
    data_df = finder.fetch_stock_data(token_info)
    if data_df is None:
        logger.error(f"❌ Could not fetch data for {ticker}")
        return None
    
    logger.info(f"✅ Data fetched: {len(data_df)} candles")
    logger.info(f"📊 Data range: {data_df.index[0]} to {data_df.index[-1]}")
    logger.info(f"💰 Price range: {data_df['Close'].min():.2f} to {data_df['Close'].max():.2f}")
    
    # Show sample data
    logger.info(f"📈 Sample data:")
    for i in range(min(5, len(data_df))):
        row = data_df.iloc[i]
        logger.info(f"  {row.name}: O={row['Open']:.2f}, H={row['High']:.2f}, L={row['Low']:.2f}, C={row['Close']:.2f}")
    
    # Analyze candle movement
    candle_analysis = finder.analyze_candle_movement(data_df)
    logger.info(f"🕯️ Candle Analysis:")
    for key, value in candle_analysis.items():
        if isinstance(value, float):
            logger.info(f"  {key}: {value:.4f}")
        else:
            logger.info(f"  {key}: {value}")
    
    # Calculate technical indicators
    try:
        technical_indicators = finder.calculate_technical_indicators(data_df)
        logger.info(f"📊 Technical Indicators:")
        for key, value in technical_indicators.items():
            if isinstance(value, float):
                logger.info(f"  {key}: {value:.4f}")
            else:
                logger.info(f"  {key}: {value}")
    except Exception as e:
        logger.error(f"❌ Technical indicators failed: {str(e)}")
        technical_indicators = {'error': str(e)}
    
    # Calculate movement score
    movement_score = finder.calculate_movement_score(candle_analysis, technical_indicators)
    logger.info(f"🎯 Movement Score:")
    for key, value in movement_score.items():
        logger.info(f"  {key}: {value}")
    
    return {
        'ticker': ticker,
        'data_points': len(data_df),
        'candle_analysis': candle_analysis,
        'technical_indicators': technical_indicators,
        'movement_score': movement_score
    }

def debug_scoring_thresholds():
    """Debug the scoring thresholds to see what's realistic"""
    
    logger.info("🔍 Debugging scoring thresholds with known good stocks...")
    
    date = "30-06-2025"
    test_stocks = KNOWN_GOOD_STOCKS[:3]  # Test first 3
    
    all_metrics = {
        'height_to_price_ratio': [],
        'movement_frequency': [],
        'height_consistency': [],
        'atr_ratio': [],
        'bb_width_avg': [],
        'volume_consistency': []
    }
    
    for ticker in test_stocks:
        logger.info(f"\n{'='*50}")
        logger.info(f"Analyzing {ticker}")
        logger.info(f"{'='*50}")
        
        result = debug_single_stock(ticker, date)
        if result and 'error' not in result.get('candle_analysis', {}):
            candle = result['candle_analysis']
            tech = result['technical_indicators']
            
            # Collect metrics
            all_metrics['height_to_price_ratio'].append(candle.get('height_to_price_ratio', 0))
            all_metrics['movement_frequency'].append(candle.get('movement_frequency', 0))
            all_metrics['height_consistency'].append(candle.get('height_consistency', 0))
            
            # Calculate ATR ratio
            atr_ratio = tech.get('atr_avg', 0) / candle.get('avg_price', 1) * 100
            all_metrics['atr_ratio'].append(atr_ratio)
            all_metrics['bb_width_avg'].append(tech.get('bb_width_avg', 0))
            all_metrics['volume_consistency'].append(tech.get('volume_consistency', 0))
    
    # Show statistics
    logger.info(f"\n{'='*60}")
    logger.info(f"METRICS STATISTICS FOR KNOWN GOOD STOCKS")
    logger.info(f"{'='*60}")
    
    for metric, values in all_metrics.items():
        if values:
            values = [v for v in values if v > 0]  # Remove zeros
            if values:
                logger.info(f"{metric}:")
                logger.info(f"  Min: {min(values):.4f}")
                logger.info(f"  Max: {max(values):.4f}")
                logger.info(f"  Avg: {np.mean(values):.4f}")
                logger.info(f"  Values: {[f'{v:.4f}' for v in values]}")
            else:
                logger.info(f"{metric}: No valid values")
        else:
            logger.info(f"{metric}: No data")

def suggest_new_thresholds():
    """Suggest new scoring thresholds based on actual data"""
    
    logger.info("💡 Suggested new scoring thresholds based on analysis:")
    logger.info("Current thresholds are likely too strict. Consider:")
    logger.info("")
    logger.info("Height Score (25 points):")
    logger.info("  Current: ≥2.0% = 25pts, ≥1.5% = 20pts, ≥1.0% = 15pts, ≥0.5% = 10pts")
    logger.info("  Suggested: ≥1.0% = 25pts, ≥0.7% = 20pts, ≥0.5% = 15pts, ≥0.3% = 10pts")
    logger.info("")
    logger.info("Movement Frequency (20 points):")
    logger.info("  Current: ≥80% = 20pts, ≥60% = 15pts, ≥40% = 10pts")
    logger.info("  Suggested: ≥60% = 20pts, ≥45% = 15pts, ≥30% = 10pts")
    logger.info("")
    logger.info("Consistency Score (20 points):")
    logger.info("  Current: ≥70% = 20pts, ≥50% = 15pts, ≥30% = 10pts")
    logger.info("  Suggested: ≥50% = 20pts, ≥35% = 15pts, ≥20% = 10pts")
    logger.info("")
    logger.info("ATR Score (15 points):")
    logger.info("  Current: 1.0-3.0% = 15pts")
    logger.info("  Suggested: 0.5-4.0% = 15pts, 0.3-5.0% = 10pts")

def main():
    """Main debug function"""
    
    logger.info("🚀 Starting Active Stocks Finder Debug...")
    
    try:
        # Debug individual stocks
        debug_scoring_thresholds()
        
        # Suggest improvements
        suggest_new_thresholds()
        
        logger.info("🎉 Debug completed!")
        
    except Exception as e:
        logger.error(f"❌ Debug failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
