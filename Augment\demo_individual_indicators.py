"""
Demo script for Individual Indicator Multi-Timeframe Analyzer
"""

from individual_indicator_multi_timeframe_analyzer import IndividualIndicatorMultiTimeframeAnalyzer

def run_demo():
    """Run demo with predefined inputs"""
    
    # Create analyzer
    analyzer = IndividualIndicatorMultiTimeframeAnalyzer()
    
    # Predefined inputs for demo
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '01-07-2025',
        'timeframes': ['1', '5', '15', '30'],
        'indicators': ['RSI_14', 'MACD_12_26_9_MACD_12_26_9', 'ADX_14_ADX_14', 'CCI_14', 'STOCH_14_3_STOCHk_14_3_3'],
        'validate_signals': True
    }
    
    print("🚀 DEMO: Individual Indicator Multi-Timeframe Analysis")
    print("=" * 80)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Timeframes: {', '.join(inputs['timeframes'])} minutes")
    print(f"🔍 Indicators: {', '.join(inputs['indicators'])}")
    print("\n🎯 Each indicator will be analyzed separately across timeframes")
    print("📈 Higher timeframe → Lower timeframe hierarchy")
    print("🔍 Overbought/Oversold + Breakout detection for each")
    
    # Find data files
    data_files = analyzer.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])
    
    if not data_files:
        print("❌ No matching data files found")
        return {}
    
    # Assign timeframes to files
    timeframe_files = analyzer.assign_timeframes_to_files(data_files, inputs['timeframes'])
    
    if len(timeframe_files) < 2:
        print("❌ Need at least 2 timeframes for analysis")
        return {}
    
    # Load timeframe data
    timeframe_data = analyzer.load_timeframe_data(timeframe_files)
    
    if len(timeframe_data) < 2:
        print("❌ Failed to load sufficient timeframe data")
        return {}
    
    # Filter indicators to only those we have thresholds for
    available_indicators = []
    for indicator in inputs['indicators']:
        if indicator in analyzer.timeframe_thresholds:
            available_indicators.append(indicator)
    
    if not available_indicators:
        print("❌ No configured indicators found")
        return {}
    
    print(f"\n📊 Analyzing {len(available_indicators)} individual indicators:")
    for indicator in available_indicators:
        print(f"   • {indicator}")
    
    # Perform individual indicator analysis
    individual_analysis = analyzer.analyze_individual_indicators_across_timeframes(
        timeframe_data, available_indicators
    )
    
    # Validate signals
    if inputs['validate_signals']:
        individual_analysis = analyzer.validate_individual_signals_with_price_movement(
            timeframe_data, individual_analysis
        )
    
    # Export to Excel
    excel_filename = analyzer.export_individual_analysis_to_excel(individual_analysis, inputs)
    
    # Print summary
    analyzer.print_individual_analysis_summary(individual_analysis)
    
    print(f"\n💾 Demo results saved to: {excel_filename}")
    
    return individual_analysis

if __name__ == "__main__":
    results = run_demo()
    
    if results:
        print("\n✅ Demo completed successfully!")
        print("📊 Check the Excel file for detailed individual indicator analysis!")
        print("🎯 Each indicator analyzed separately with professional thresholds")
        print("📈 Higher timeframe signals confirmed by lower timeframes")
        print("✅ Signals validated with actual price movement")
    else:
        print("\n❌ Demo failed.")
