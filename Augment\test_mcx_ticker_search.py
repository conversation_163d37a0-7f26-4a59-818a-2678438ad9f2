"""
MCX Ticker Search Helper

This script helps find the correct ticker format for MCX instruments
and tests the searchscrip API for various MCX tickers.
"""

import sys
import os
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from shared_api_manager import get_api

def test_mcx_ticker_search(search_terms):
    """Test MCX ticker search with various terms"""
    print("🔍 MCX Ticker Search Test")
    print("=" * 50)
    
    try:
        api = get_api()
        
        for search_term in search_terms:
            print(f"\n🔍 Searching for: '{search_term}' on MCX")
            print("-" * 30)
            
            try:
                ret = api.searchscrip(exchange='MCX', searchtext=search_term)
                
                if not ret:
                    print("❌ Empty response")
                    continue
                
                if not isinstance(ret, dict):
                    print("❌ Invalid response format")
                    continue
                
                if ret.get('stat') != 'Ok':
                    print(f"❌ API Error: {ret.get('emsg', 'Unknown error')}")
                    continue
                
                values = ret.get('values', [])
                if not values:
                    print("❌ No results found")
                    continue
                
                print(f"✅ Found {len(values)} results:")
                
                for i, item in enumerate(values[:10]):  # Show first 10 results
                    token = item.get('token', 'N/A')
                    tsym = item.get('tsym', 'N/A')
                    instname = item.get('instname', 'N/A')
                    cname = item.get('cname', 'N/A')
                    expiry = item.get('exd', 'N/A')
                    
                    print(f"  {i+1:2d}. Token: {token:<8} | Symbol: {tsym:<20} | Type: {instname:<10} | Name: {cname}")
                    if expiry != 'N/A':
                        print(f"      Expiry: {expiry}")
                
                # Show the best match for this search
                if values:
                    best_match = values[0]
                    print(f"\n🎯 Best Match: {best_match.get('tsym')} (Token: {best_match.get('token')})")
                
            except ValueError as e:
                print(f"❌ JSON parsing error: {str(e)}")
                print("💡 This usually means the API returned empty or invalid data")
            except Exception as e:
                print(f"❌ Search error: {str(e)}")
    
    except Exception as e:
        print(f"❌ API connection error: {str(e)}")

def suggest_mcx_tickers():
    """Suggest common MCX ticker formats"""
    print("\n💡 Common MCX Ticker Formats:")
    print("=" * 40)
    
    suggestions = {
        "Silver": [
            "SILVER", "SILVERM", "SILVERMINI", "SILVERMIC",
            "SILVER24JUL", "SILVER24AUG", "SILVER24SEP"
        ],
        "Gold": [
            "GOLD", "GOLDM", "GOLDMINI", "GOLDMIC",
            "GOLD24JUL", "GOLD24AUG", "GOLD24SEP"
        ],
        "Crude Oil": [
            "CRUDEOIL", "CRUDEOILM", "CRUDEOILMINI",
            "CRUDEOIL24JUL", "CRUDEOIL24AUG"
        ],
        "Natural Gas": [
            "NATURALGAS", "NATURALGASM", "NATURALGASMINI",
            "NATURALGAS24JUL", "NATURALGAS24AUG"
        ],
        "Copper": [
            "COPPER", "COPPERM", "COPPERMINI",
            "COPPER24JUL", "COPPER24AUG"
        ],
        "Zinc": [
            "ZINC", "ZINCMINI",
            "ZINC24JUL", "ZINC24AUG"
        ]
    }
    
    for commodity, tickers in suggestions.items():
        print(f"\n📈 {commodity}:")
        for ticker in tickers:
            print(f"   - {ticker}")

def test_specific_silver_tickers():
    """Test specific silver ticker variations"""
    print("\n🥈 Testing Silver Ticker Variations")
    print("=" * 40)
    
    silver_variations = [
        "SILVER",
        "SILVERM", 
        "SILVERMINI",
        "SILVERMIC",
        "SILVER24JUL",
        "SILVER24AUG",
        "SILVER24SEP",
        "SILVER24OCT",
        "SILVER24NOV",
        "SILVER24DEC",
        "SILVE",  # Partial match
        "SIL",    # Short match
    ]
    
    test_mcx_ticker_search(silver_variations)

def main():
    """Main function"""
    print("🚀 MCX Ticker Search Helper")
    print("=" * 60)
    
    # Test the problematic ticker first
    print("🔍 Testing the problematic ticker: SILVE04JUL24")
    test_mcx_ticker_search(["SILVE04JUL24"])
    
    # Test silver variations
    test_specific_silver_tickers()
    
    # Show suggestions
    suggest_mcx_tickers()
    
    print("\n" + "=" * 60)
    print("✅ MCX Ticker Search Test Completed!")
    
    print("\n💡 Usage Tips:")
    print("1. Use the exact symbol names shown in the search results")
    print("2. For current month contracts, try without date suffix")
    print("3. For mini contracts, add 'MINI' or 'M' suffix")
    print("4. Check expiry dates to ensure contract is active")
    
    print("\n🔧 Integration Command Examples:")
    print("# Try these working MCX tickers:")
    print("python integrated_technical_analyzer.py --mode historical --ticker SILVER --exchange MCX --date 27-06-2025")
    print("python integrated_technical_analyzer.py --mode historical --ticker GOLD --exchange MCX --date 27-06-2025")
    print("python integrated_technical_analyzer.py --mode historical --ticker CRUDEOIL --exchange MCX --date 27-06-2025")

if __name__ == "__main__":
    main()
