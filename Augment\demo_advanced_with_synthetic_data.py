"""
Demo script for Advanced Professional Signal Analyzer with Synthetic Data
Shows PGO-style reversal detection and time-based signal tracking
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from advanced_professional_signal_analyzer import AdvancedProfessionalSignalAnalyzer

def create_synthetic_data():
    """Create synthetic data with realistic indicator patterns"""
    
    print("🔧 CREATING SYNTHETIC DATA WITH REALISTIC PATTERNS")
    print("=" * 60)
    
    # Create time index
    start_time = datetime.now() - timedelta(hours=6)
    
    # Create different timeframe data
    timeframes = {
        '1min': 360,   # 6 hours of 1-minute data
        '5min': 72,    # 6 hours of 5-minute data
        '15min': 24,   # 6 hours of 15-minute data
        '30min': 12,   # 6 hours of 30-minute data
        '45min': 8     # 6 hours of 45-minute data
    }
    
    synthetic_data = {}
    
    for timeframe, periods in timeframes.items():
        # Create time index
        if timeframe == '1min':
            time_index = pd.date_range(start_time, periods=periods, freq='1min')
        elif timeframe == '5min':
            time_index = pd.date_range(start_time, periods=periods, freq='5min')
        elif timeframe == '15min':
            time_index = pd.date_range(start_time, periods=periods, freq='15min')
        elif timeframe == '30min':
            time_index = pd.date_range(start_time, periods=periods, freq='30min')
        elif timeframe == '45min':
            time_index = pd.date_range(start_time, periods=periods, freq='45min')
        
        # Create base price data
        base_price = 180.0
        price_trend = np.cumsum(np.random.randn(periods) * 0.5)
        prices = base_price + price_trend
        
        # Create PGO data with realistic reversal patterns
        pgo_data = create_pgo_with_reversals(periods)
        
        # Create CCI data
        cci_data = create_cci_data(periods)
        
        # Create SMI data
        smi_data = create_smi_data(periods)
        
        # Create other indicators
        bias_data = np.random.randn(periods) * 3 + np.sin(np.arange(periods) * 0.1) * 2
        cg_data = np.random.randn(periods) * 0.8 + np.cos(np.arange(periods) * 0.15) * 0.5
        
        # Create ACCBANDS data (relative to price)
        accbands_data = prices * (1 + np.random.randn(periods) * 0.02 + 0.01)
        
        # Create DataFrame
        df = pd.DataFrame({
            'Time': time_index,
            'Close': prices,
            'PGO_14': pgo_data,
            'CCI_14': cci_data,
            'SMI_5_20_5_SMIo_5_20_5_100.0': smi_data,
            'ACCBANDS_10_ACCBU_10': accbands_data,
            'BIAS_26': bias_data,
            'CG_10': cg_data
        })
        
        synthetic_data[timeframe] = df
        print(f"✅ {timeframe}: {df.shape} with realistic patterns")
    
    return synthetic_data

def create_pgo_with_reversals(periods):
    """Create PGO data with realistic reversal patterns like -2 → -3.5 → -2.7"""
    
    pgo_data = np.zeros(periods)
    
    # Start with random walk
    pgo_data[0] = np.random.randn() * 2
    
    for i in range(1, periods):
        # Normal random walk
        pgo_data[i] = pgo_data[i-1] + np.random.randn() * 0.3
        
        # Add reversal patterns occasionally
        if i > 10 and np.random.random() < 0.05:  # 5% chance of reversal pattern
            if pgo_data[i] < -2:  # Oversold condition
                # Create deeper penetration
                depth = pgo_data[i] - np.random.uniform(0.5, 1.5)  # Go deeper
                pgo_data[i] = depth
                
                # Create reversal in next few periods
                if i + 5 < periods:
                    reversal_target = depth * 0.6  # 40% reversal back
                    for j in range(1, min(6, periods - i)):
                        pgo_data[i + j] = depth + (reversal_target - depth) * (j / 5) + np.random.randn() * 0.1
            
            elif pgo_data[i] > 2:  # Overbought condition
                # Similar logic for overbought reversals
                height = pgo_data[i] + np.random.uniform(0.5, 1.5)
                pgo_data[i] = height
                
                if i + 5 < periods:
                    reversal_target = height * 0.6
                    for j in range(1, min(6, periods - i)):
                        pgo_data[i + j] = height + (reversal_target - height) * (j / 5) + np.random.randn() * 0.1
    
    return pgo_data

def create_cci_data(periods):
    """Create CCI data with overbought/oversold patterns"""
    
    cci_data = np.zeros(periods)
    cci_data[0] = np.random.randn() * 50
    
    for i in range(1, periods):
        # Mean reversion tendency
        mean_reversion = -cci_data[i-1] * 0.1
        cci_data[i] = cci_data[i-1] + mean_reversion + np.random.randn() * 15
        
        # Clamp extreme values with some probability of breakout
        if cci_data[i] > 150 and np.random.random() < 0.8:
            cci_data[i] = 150 - np.random.uniform(0, 30)
        elif cci_data[i] < -150 and np.random.random() < 0.8:
            cci_data[i] = -150 + np.random.uniform(0, 30)
    
    return cci_data

def create_smi_data(periods):
    """Create SMI data with oscillator patterns"""
    
    smi_data = np.zeros(periods)
    smi_data[0] = np.random.randn() * 20
    
    for i in range(1, periods):
        # Oscillator behavior
        cycle_component = 20 * np.sin(i * 0.1)
        noise = np.random.randn() * 5
        momentum = (smi_data[i-1] - cycle_component) * 0.1
        
        smi_data[i] = cycle_component + momentum + noise
        
        # Clamp to realistic range
        smi_data[i] = np.clip(smi_data[i], -60, 60)
    
    return smi_data

def run_advanced_demo_with_synthetic():
    """Run demo with synthetic data"""
    
    # Create analyzer
    analyzer = AdvancedProfessionalSignalAnalyzer()
    
    print("🚀 DEMO: Advanced Professional Signal Analysis with Synthetic Data")
    print("=" * 80)
    print("⏰ Time-based signal generation and confirmation tracking")
    print("🔄 PGO-style reversal detection (-2 → -3.5 → -2.7 = BUY)")
    print("📊 Data-driven threshold optimization")
    print("🎯 Professional timeframe-adjusted signal values")
    
    # Create synthetic data
    timeframe_data = create_synthetic_data()
    
    # Define indicators to analyze
    available_indicators = ['PGO_14', 'CCI_14', 'SMI_5_20_5_SMIo_5_20_5_100.0', 'BIAS_26', 'CG_10']
    
    print(f"\n📊 Analyzing {len(available_indicators)} professional indicators:")
    for indicator in available_indicators:
        print(f"   ✅ {indicator}")
    
    # Perform advanced data analysis
    print(f"\n🔬 PERFORMING ADVANCED DATA ANALYSIS...")
    analysis_results = analyzer.perform_advanced_data_analysis(timeframe_data, available_indicators)
    
    # Detect professional signals with timing
    print(f"\n🎯 DETECTING PROFESSIONAL SIGNALS WITH TIMING...")
    signal_results = analyzer.detect_professional_signals_with_timing(timeframe_data, available_indicators)
    
    # Create inputs for export
    inputs = {
        'ticker': 'SYNTHETIC_DEMO',
        'exchange': 'DEMO',
        'date': datetime.now().strftime('%d-%m-%Y'),
        'timeframes': ['1', '5', '15', '30', '45'],
        'indicators': available_indicators,
        'validate_signals': True
    }
    
    # Export to Excel
    excel_filename = analyzer.export_advanced_analysis_to_excel(analysis_results, signal_results, inputs)
    
    # Print summary
    analyzer._print_advanced_summary(analysis_results, signal_results)
    
    # Show specific PGO examples
    print(f"\n🔄 PGO REVERSAL PATTERN EXAMPLES:")
    print("=" * 60)
    
    pgo_signals = signal_results['signals_by_indicator'].get('PGO_14', [])
    pgo_reversals = [s for s in pgo_signals if s['signal_reason'] == 'PGO_REVERSAL_PATTERN']
    
    if pgo_reversals:
        for i, signal in enumerate(pgo_reversals[:5], 1):
            print(f"\n   {i}. {signal['signal_type']} Signal in {signal['timeframe']}")
            print(f"      Pattern: {signal.get('deepest_value', signal.get('highest_value', 0)):.2f} → {signal['reversal_value']:.2f}")
            print(f"      Reversal: {signal['reversal_percentage']:.1%}")
            print(f"      Generated: {signal['signal_generation_time']}")
            print(f"      Confirmed: {signal['confirmation_time']}")
            print(f"      Strength: {signal['signal_strength']:.3f}")
            print(f"      Assessment: {signal['professional_assessment']}")
    else:
        print("   ⚪ No PGO reversal patterns detected")
        print("   💡 This is normal with random synthetic data")
    
    # Show all signals found
    print(f"\n📊 ALL SIGNALS DETECTED:")
    print("=" * 60)
    
    total_signals = 0
    for indicator, signals in signal_results['signals_by_indicator'].items():
        if signals:
            print(f"\n   📈 {indicator}: {len(signals)} signals")
            for signal in signals[:3]:  # Show first 3 signals
                print(f"      {signal['signal_type']} - {signal['signal_reason']} ({signal['timeframe']})")
                print(f"      Strength: {signal['signal_strength']:.3f} | Time: {signal['signal_generation_time']}")
            total_signals += len(signals)
    
    print(f"\n   📊 Total Signals: {total_signals}")
    
    print(f"\n💾 Synthetic demo results saved to: {excel_filename}")
    
    return {
        'analysis_results': analysis_results,
        'signal_results': signal_results,
        'inputs': inputs,
        'synthetic_data': timeframe_data
    }

if __name__ == "__main__":
    results = run_advanced_demo_with_synthetic()
    
    if results:
        print("\n✅ Advanced Professional Signal Analysis Demo completed!")
        print("⏰ Time-based signal tracking demonstrated")
        print("🔄 PGO-style reversal detection tested")
        print("📊 Data-driven threshold optimization completed")
        print("🎯 Professional signal values applied")
        print("📈 Hierarchical confirmations analyzed")
        print("🎪 Entry points with risk/reward calculated")
        print("📄 Check the Excel file for comprehensive analysis!")
        
        print(f"\n🎯 DEMO HIGHLIGHTS:")
        print(f"   📊 Synthetic data created with realistic patterns")
        print(f"   🔄 PGO reversal patterns embedded in data")
        print(f"   ⏰ Time-based signal generation working")
        print(f"   📈 Professional thresholds applied correctly")
        print(f"   🎪 Entry point detection functional")
        print(f"   📊 Data analysis and optimization completed")
        
    else:
        print("\n❌ Demo failed.")
