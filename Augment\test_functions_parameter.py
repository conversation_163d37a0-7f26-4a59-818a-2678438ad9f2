"""
Test Functions Parameter

Test script to verify that the new --functions parameter works correctly.
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data(interval_minutes=1, start_time="09:15", end_time="15:30"):
    """Create test data with specific interval"""
    
    start_dt = datetime.strptime(f"2025-06-30 {start_time}", "%Y-%m-%d %H:%M")
    end_dt = datetime.strptime(f"2025-06-30 {end_time}", "%Y-%m-%d %H:%M")
    
    times = []
    current = start_dt
    while current <= end_dt:
        times.append(current)
        current += timedelta(minutes=interval_minutes)
    
    # Create realistic price data
    import random
    random.seed(42)
    
    base_price = 100.0
    data = []
    
    for i, time in enumerate(times):
        trend = 0.01 * i
        noise = random.uniform(-0.5, 0.5)
        
        open_price = base_price + trend + noise
        high_price = open_price + random.uniform(0.1, 0.8)
        low_price = open_price - random.uniform(0.1, 0.6)
        close_price = open_price + random.uniform(-0.3, 0.4)
        volume = random.randint(1000, 5000)
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
        
        base_price = close_price
    
    df = pd.DataFrame(data)
    df.set_index('time', inplace=True)
    df.reset_index(inplace=True)
    
    return df

def test_specific_functions():
    """Test specific function calculation"""
    
    logger.info("Testing specific function calculation...")
    
    try:
        from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer
        
        analyzer = TechnicalIndicatorsAnalyzer()
        
        # Create test data
        test_data = create_test_data(interval_minutes=1)
        logger.info(f"Created test data: {len(test_data)} candles")
        
        # Test specific functions
        test_functions = ['rsi', 'macd', 'bbands', 'atr', 'sma']
        
        logger.info(f"Testing functions: {', '.join(test_functions)}")
        
        # Test extension method with specific functions
        result = analyzer._analyze_dataframe(
            data=test_data,
            method='extension',
            categories=None,
            functions=test_functions
        )
        
        if result and 'indicators' in result:
            indicators = result['indicators']
            logger.info(f"✅ Successfully calculated {len(indicators)} indicators")
            
            # Check if we got the expected functions
            found_functions = []
            for indicator_name in indicators.keys():
                for func in test_functions:
                    if func.lower() in indicator_name.lower():
                        found_functions.append(func)
                        break
            
            logger.info(f"Found indicators for functions: {', '.join(set(found_functions))}")
            
            # Show some sample results
            logger.info("Sample indicator values:")
            for i, (name, value) in enumerate(list(indicators.items())[:10]):
                logger.info(f"  {name}: {value}")
            
            return True
        else:
            logger.error(f"❌ No indicators calculated. Result: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing specific functions: {e}")
        return False

def test_cli_functions_parameter():
    """Test CLI functions parameter parsing"""
    
    logger.info("Testing CLI functions parameter parsing...")
    
    try:
        from integrated_technical_analyzer import create_cli_parser
        
        parser = create_cli_parser()
        
        # Test functions parameter
        test_args = [
            '--mode', 'analysis',
            '--analysis-type', 'signals',
            '--ticker', 'ACC',
            '--exchange', 'NSE',
            '--date', '30-06-2025',
            '--method', 'extension',
            '--functions', 'rsi,macd,bbands,atr,sma',
            '--interval', '1'
        ]
        
        args = parser.parse_args(test_args)
        
        logger.info(f"Parsed arguments:")
        logger.info(f"  functions: {args.functions}")
        logger.info(f"  method: {args.method}")
        logger.info(f"  interval: {args.interval}")
        
        # Test functions parsing
        if args.functions:
            functions = [func.strip() for func in args.functions.split(',')]
            logger.info(f"  parsed functions list: {functions}")
            
            expected_functions = ['rsi', 'macd', 'bbands', 'atr', 'sma']
            if functions == expected_functions:
                logger.info("✅ Functions parameter parsed correctly")
                return True
            else:
                logger.error(f"❌ Functions mismatch. Expected: {expected_functions}, Got: {functions}")
                return False
        else:
            logger.error("❌ Functions parameter not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing CLI functions parameter: {e}")
        return False

def test_functions_vs_categories():
    """Test that functions override categories"""
    
    logger.info("Testing functions vs categories priority...")
    
    try:
        from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer
        
        analyzer = TechnicalIndicatorsAnalyzer()
        test_data = create_test_data(interval_minutes=1)
        
        # Test 1: Only categories
        logger.info("Test 1: Only categories (volatility)")
        result1 = analyzer._analyze_dataframe(
            data=test_data,
            method='extension',
            categories=['volatility'],
            functions=None
        )
        
        indicators1 = result1.get('indicators', {}) if result1 else {}
        logger.info(f"  Categories only: {len(indicators1)} indicators")
        
        # Test 2: Only functions
        logger.info("Test 2: Only functions (rsi,macd)")
        result2 = analyzer._analyze_dataframe(
            data=test_data,
            method='extension',
            categories=None,
            functions=['rsi', 'macd']
        )
        
        indicators2 = result2.get('indicators', {}) if result2 else {}
        logger.info(f"  Functions only: {len(indicators2)} indicators")
        
        # Test 3: Both functions and categories (functions should override)
        logger.info("Test 3: Both functions and categories (functions should override)")
        result3 = analyzer._analyze_dataframe(
            data=test_data,
            method='extension',
            categories=['volatility'],  # This should be ignored
            functions=['rsi', 'macd']   # This should be used
        )
        
        indicators3 = result3.get('indicators', {}) if result3 else {}
        logger.info(f"  Both (functions priority): {len(indicators3)} indicators")
        
        # Check if functions override categories
        if len(indicators2) == len(indicators3) and len(indicators2) > 0:
            logger.info("✅ Functions correctly override categories")
            return True
        else:
            logger.warning(f"❌ Functions override test unclear. Functions only: {len(indicators2)}, Both: {len(indicators3)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing functions vs categories: {e}")
        return False

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Functions Parameter Tests...")
    
    # Test 1: CLI parameter parsing
    logger.info("\n" + "="*60)
    logger.info("TEST 1: CLI FUNCTIONS PARAMETER PARSING")
    logger.info("="*60)
    cli_test = test_cli_functions_parameter()
    
    # Test 2: Specific function calculation
    logger.info("\n" + "="*60)
    logger.info("TEST 2: SPECIFIC FUNCTION CALCULATION")
    logger.info("="*60)
    function_test = test_specific_functions()
    
    # Test 3: Functions vs categories priority
    logger.info("\n" + "="*60)
    logger.info("TEST 3: FUNCTIONS VS CATEGORIES PRIORITY")
    logger.info("="*60)
    priority_test = test_functions_vs_categories()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    tests = {
        'CLI Parameter Parsing': cli_test,
        'Function Calculation': function_test,
        'Functions Priority': priority_test
    }
    
    passed = sum(1 for success in tests.values() if success)
    total = len(tests)
    
    logger.info(f"Tests passed: {passed}/{total}")
    
    for test_name, success in tests.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    if passed == total:
        logger.info("🎉 All functions parameter tests passed!")
        logger.info("You can now use: --functions rsi,macd,bbands,atr,sma")
    else:
        logger.info("❌ Some tests failed. Check the implementation.")
    
    logger.info("Tests completed!")

if __name__ == "__main__":
    main()
