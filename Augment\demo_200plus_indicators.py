"""
Demo script for 200+ Individual Indicators Multi-Timeframe Analyzer
Tests all the specific indicators you requested
"""

from individual_indicator_multi_timeframe_analyzer import IndividualIndicatorMultiTimeframeA<PERSON>yzer

def run_comprehensive_demo():
    """Run demo with your specific requested indicators"""
    
    # Create analyzer
    analyzer = IndividualIndicatorMultiTimeframeAnalyzer()
    
    # Your specific requested indicators
    requested_indicators = [
        'CCI_14',
        'SMI_5_20_5_SMIo_5_20_5_100.0',
        'ACCBANDS_10_ACCBU_10',
        'BIAS_26',
        'CG_10',
        'PGO_14',
        'QQE_14_QQE_14_5_4.236_RSIMA',
        'SMI_5_20_5_SMI_5_20_5_100.0',
        # Additional indicators from the analyzer
        'RSI_14',
        'MACD_12_26_9_MACD_12_26_9',
        'ADX_14_ADX_14',
        'STOCH_14_3_STOCHk_14_3_3',
        'WILLR_14',
        'MFI_14',
        'BBANDS_5_2_BBP_5_2.0',
        'BRAR_26_AR_26',
        'BRAR_26_BR_26',
        'HWC_HWPCT',
        'KDJ_9_3_J_9_3',
        'KDJ_9_3_K_9_3',
        'KDJ_9_3_D_9_3',
        'ER_10',
        'MOM_10',
        'QSTICK_10',
        'ROC_10'
    ]
    
    # Predefined inputs for demo
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '01-07-2025',
        'timeframes': ['1', '5', '15', '30'],
        'indicators': requested_indicators,
        'validate_signals': True
    }
    
    print("🚀 DEMO: 200+ Individual Indicators Multi-Timeframe Analysis")
    print("=" * 80)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Timeframes: {', '.join(inputs['timeframes'])} minutes")
    print(f"🔍 Your Specific Indicators: {len(requested_indicators)}")
    
    print("\n🎯 YOUR SPECIFIC REQUESTED INDICATORS:")
    for i, indicator in enumerate(requested_indicators[:8], 1):
        print(f"   {i}. {indicator}")
    print(f"   ... and {len(requested_indicators)-8} more indicators")
    
    print(f"\n📈 Total configured indicators in analyzer: {len(analyzer.timeframe_thresholds)}")
    
    # Find data files
    data_files = analyzer.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])
    
    if not data_files:
        print("❌ No matching data files found")
        return {}
    
    # Assign timeframes to files
    timeframe_files = analyzer.assign_timeframes_to_files(data_files, inputs['timeframes'])
    
    if len(timeframe_files) < 2:
        print("❌ Need at least 2 timeframes for analysis")
        return {}
    
    # Load timeframe data
    timeframe_data = analyzer.load_timeframe_data(timeframe_files)
    
    if len(timeframe_data) < 2:
        print("❌ Failed to load sufficient timeframe data")
        return {}
    
    # Filter indicators to only those we have thresholds for
    available_indicators = []
    for indicator in inputs['indicators']:
        if indicator in analyzer.timeframe_thresholds:
            available_indicators.append(indicator)
        else:
            # Try to find partial matches
            for configured_indicator in analyzer.timeframe_thresholds.keys():
                if indicator in configured_indicator or configured_indicator in indicator:
                    available_indicators.append(configured_indicator)
                    print(f"🔍 Matched '{indicator}' → '{configured_indicator}'")
                    break
    
    if not available_indicators:
        print("❌ No configured indicators found")
        print(f"Available indicators: {list(analyzer.timeframe_thresholds.keys())}")
        return {}
    
    print(f"\n📊 Analyzing {len(available_indicators)} individual indicators:")
    for indicator in available_indicators:
        print(f"   ✅ {indicator}")
    
    # Perform individual indicator analysis
    individual_analysis = analyzer.analyze_individual_indicators_across_timeframes(
        timeframe_data, available_indicators
    )
    
    # Validate signals
    if inputs['validate_signals']:
        individual_analysis = analyzer.validate_individual_signals_with_price_movement(
            timeframe_data, individual_analysis
        )
    
    # Export to Excel
    excel_filename = analyzer.export_individual_analysis_to_excel(individual_analysis, inputs)
    
    # Print summary
    analyzer.print_individual_analysis_summary(individual_analysis)
    
    # Print specific results for your requested indicators
    print(f"\n🎯 SPECIFIC RESULTS FOR YOUR REQUESTED INDICATORS:")
    print("=" * 80)
    
    your_indicators = ['CCI_14', 'SMI_5_20_5_SMIo_5_20_5_100.0', 'ACCBANDS_10_ACCBU_10', 
                      'BIAS_26', 'CG_10', 'PGO_14', 'QQE_14_QQE_14_5_4.236_RSIMA']
    
    for indicator in your_indicators:
        if indicator in individual_analysis:
            analysis = individual_analysis[indicator]
            hierarchy = analysis['hierarchy_confirmation']
            
            print(f"\n📊 {indicator}:")
            
            if hierarchy.get('higher_timeframe_signal'):
                higher_signal = hierarchy['higher_timeframe_signal']
                signal_info = higher_signal['signal']
                
                print(f"   🎯 Signal: {signal_info['signal_type']} - {signal_info['condition']}")
                print(f"   ⏰ Timeframe: {higher_signal['timeframe']}")
                print(f"   💪 Strength: {signal_info['signal_strength']:.3f}")
                print(f"   💥 Breakout: {'Yes' if signal_info.get('breakout_detected', False) else 'No'}")
                print(f"   ✅ Confirmation: {hierarchy['confirmation_strength']:.1%}")
                print(f"   📈 Recommendation: {analysis['trade_recommendation']}")
                
                validation_score = analysis.get('validation_score', 0)
                if validation_score > 0:
                    print(f"   🔍 Validation: {validation_score:.1%}")
            else:
                print(f"   ⚪ No clear signal in higher timeframes")
    
    print(f"\n💾 Comprehensive results saved to: {excel_filename}")
    
    return individual_analysis

if __name__ == "__main__":
    results = run_comprehensive_demo()
    
    if results:
        print("\n✅ 200+ Indicators Demo completed successfully!")
        print("📊 Your specific indicators analyzed with professional thresholds!")
        print("🎯 Each indicator analyzed separately across all timeframes")
        print("📈 Higher timeframe signals confirmed by lower timeframes")
        print("✅ Signals validated with actual price movement")
        print("📄 Check the Excel file for detailed analysis of ALL indicators!")
    else:
        print("\n❌ Demo failed.")
