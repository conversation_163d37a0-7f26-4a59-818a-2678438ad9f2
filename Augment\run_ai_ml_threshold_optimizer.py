"""
CLI Runner for Advanced AI/ML Threshold Optimization System
Execute the comprehensive threshold optimization with proper conda environment
"""

import os
import sys
import subprocess
from datetime import datetime

def activate_conda_and_run():
    """Activate conda environment and run the optimization"""
    
    print("🚀 AI/ML Threshold Optimization System - CLI Runner")
    print("================================================================================")
    print("🔧 Activating conda environment: Shoonya1")
    print("🤖 Starting comprehensive threshold optimization...")
    print("================================================================================")
    
    # PowerShell command to activate conda and run optimization
    powershell_command = """
    conda activate Shoonya1; 
    cd 'C:/Users/<USER>/Downloads/shoonya/ShoonyaApi-py/Augment'; 
    python advanced_ai_ml_threshold_optimizer.py
    """
    
    try:
        # Execute the command
        result = subprocess.run(
            ["powershell", "-Command", powershell_command],
            capture_output=True,
            text=True,
            cwd="C:/Users/<USER>/Downloads/shoonya/ShoonyaApi-py/Augment"
        )
        
        # Print output
        if result.stdout:
            print("📊 OPTIMIZATION OUTPUT:")
            print("=" * 80)
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ WARNINGS/ERRORS:")
            print("=" * 80)
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ AI/ML Threshold Optimization completed successfully!")
        else:
            print(f"\n❌ Optimization failed with return code: {result.returncode}")
            
    except Exception as e:
        print(f"❌ Error running optimization: {str(e)}")

def check_requirements():
    """Check if required files exist"""
    
    print("🔍 Checking requirements...")
    
    required_files = [
        'advanced_ai_ml_threshold_optimizer.py',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_3min_20250714_020457.xlsx',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_5min_20250714_020552.xlsx',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_15min_20250714_020711.xlsx',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_30min_20250714_020756.xlsx',
        'technical_analysis_NATURALGAS26AUG25_MCX_signals_60min_20250714_020823.xlsx'
    ]
    
    missing_files = []
    existing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            existing_files.append(file)
            print(f"✅ Found: {file}")
        else:
            missing_files.append(file)
            print(f"⚠️ Missing: {file}")
    
    print(f"\n📊 Status: {len(existing_files)}/{len(required_files)} files found")
    
    if missing_files:
        print("\n⚠️ Missing files detected:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nNote: Optimization will proceed with available files")
    
    return len(existing_files) > 1  # Need at least 2 files (1min + 1 higher timeframe)

def main():
    """Main execution function"""
    
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check requirements
    if not check_requirements():
        print("❌ Insufficient data files. Need at least 1min + 1 higher timeframe file.")
        return
    
    print("\n" + "="*80)
    
    # Run optimization
    activate_conda_and_run()
    
    print(f"\n🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
