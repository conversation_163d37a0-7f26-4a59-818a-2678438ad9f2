"""
Demo Comprehensive Multi-Timeframe Confirmation System
- Higher timeframe confirmation to avoid false signals
- Professional values optimized for each timeframe
- AI/ML enhanced timeframe combinations
- Separate Excel sheets: ML Enhanced, Reversal, Breakout, ML Optimization, Summary
- Advanced false signal filtering with multi-timeframe validation
"""

import os
from enhanced_multi_interval_professional_analyzer import EnhancedMultiIntervalProfessionalAnalyzer
from advanced_multi_timeframe_confirmation_system import AdvancedMultiTimeframeConfirmationSystem

def demo_professional_timeframe_thresholds():
    """Demo the professional timeframe-specific thresholds"""
    
    print("🚀 PROFESSIONAL TIMEFRAME-SPECIFIC THRESHOLDS DEMO")
    print("=" * 80)
    print("🎯 Different professional values for each timeframe")
    print("📊 Optimized thresholds based on timeframe characteristics")
    
    system = AdvancedMultiTimeframeConfirmationSystem()
    
    print(f"\n📊 PROFESSIONAL TIMEFRAME-SPECIFIC THRESHOLDS:")
    print("=" * 80)
    
    for timeframe in ['1min', '5min', '15min', '30min']:
        if timeframe in system.professional_timeframe_thresholds:
            print(f"\n🕐 {timeframe.upper()} TIMEFRAME:")
            
            for indicator, thresholds in system.professional_timeframe_thresholds[timeframe].items():
                print(f"   🔍 {indicator}:")
                print(f"      📉 Oversold: Detection {thresholds['reversal_detection']} → Confirmation {thresholds['reversal_confirmation']}")
                print(f"      📈 Overbought: Detection {thresholds['overbought_reversal_detection']} → Confirmation {thresholds['overbought_reversal_confirmation']}")
                print(f"      🛑 Breakout Avoidance: {thresholds['breakout_avoidance']} / {thresholds['overbought_breakout_avoidance']}")
                print(f"      💪 Min Strength: {thresholds['min_reversal_strength']}")
                print(f"      💰 Risk: {thresholds['stop_loss_pct']}% SL, {thresholds['target_pct']}% Target")
    
    print(f"\n💡 KEY DIFFERENCES BY TIMEFRAME:")
    print(f"   🕐 1min: Tighter thresholds for quick scalping")
    print(f"   🕐 5min: Balanced thresholds for short-term trading")
    print(f"   🕐 15min: Wider thresholds for swing entries")
    print(f"   🕐 30min: Broadest thresholds for position trading")
    
    print(f"\n🔄 MULTI-TIMEFRAME CONFIRMATION LOGIC:")
    print(f"   1. 📊 Detect signal in primary timeframe (e.g., 5min)")
    print(f"   2. ✅ Confirm with higher timeframes (15min, 30min)")
    print(f"   3. 🛑 Reject if higher timeframes show breakout conditions")
    print(f"   4. 🎯 Only enter when majority of higher timeframes confirm")

def demo_comprehensive_system():
    """Demo the complete comprehensive system"""
    
    print(f"\n🚀 COMPREHENSIVE MULTI-TIMEFRAME SYSTEM DEMO")
    print("=" * 80)
    print("📊 Higher timeframe confirmation for signal accuracy")
    print("🤖 AI/ML enhanced timeframe combinations")
    print("📄 Complete Excel output with all required sheets")
    
    # Use existing interval files if available
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '03-07-2025',
        'start_time': '10:00',
        'end_time': '22:32',
        'intervals': ['1', '5', '15'],  # Use 3 intervals for comprehensive demo
        'run_advanced_analysis': True
    }
    
    print(f"\n📝 DEMO INPUTS:")
    print("=" * 40)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Time: {inputs['start_time']} - {inputs['end_time']}")
    print(f"📈 Intervals: {', '.join(inputs['intervals'])} minutes")
    
    # Create analyzer instance
    analyzer = EnhancedMultiIntervalProfessionalAnalyzer()
    
    # Check for existing files
    print(f"\n🔄 CHECKING FOR EXISTING INTERVAL FILES...")
    import glob
    existing_files = {}
    for interval in inputs['intervals']:
        pattern = f"technical_analysis_{inputs['ticker']}_{inputs['exchange']}_signals_{interval}min_*.xlsx"
        files = glob.glob(pattern)
        if files:
            latest_file = max(files, key=os.path.getctime)
            existing_files[f"{interval}min"] = latest_file
            print(f"✅ Found existing {interval}min file: {os.path.basename(latest_file)}")
        else:
            print(f"❌ No existing {interval}min file found")
    
    if len(existing_files) < len(inputs['intervals']):
        print(f"\n🔄 GENERATING MISSING INTERVAL FILES...")
        interval_files = analyzer.generate_interval_data(inputs)
    else:
        print(f"\n✅ Using existing interval files")
        interval_files = existing_files
    
    if not interval_files:
        print("❌ No interval files available")
        return False
    
    # Run comprehensive analysis
    print(f"\n🎯 RUNNING COMPREHENSIVE MULTI-TIMEFRAME ANALYSIS...")
    success = analyzer.run_advanced_professional_analysis(inputs, interval_files)
    
    if success:
        print(f"\n✅ COMPREHENSIVE SYSTEM DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print("🎯 DEMO SUMMARY:")
        print(f"   📊 Step 1: ✅ {len(interval_files)} interval files processed")
        print(f"   🎯 Step 2: ✅ Multi-timeframe confirmation analysis completed")
        print(f"   📄 Step 3: ✅ Comprehensive Excel output generated")
        
        print(f"\n🏆 KEY COMPREHENSIVE FEATURES DEMONSTRATED:")
        print("   📊 Higher timeframe confirmation to reduce false signals")
        print("   🎯 Professional timeframe-specific threshold values")
        print("   🤖 AI/ML enhanced timeframe combinations")
        print("   📄 Complete Excel output with all required sheets:")
        print("      • ML Enhanced Signals")
        print("      • Reversal Signals") 
        print("      • Breakout Signals")
        print("      • ML Optimization Results")
        print("      • Multi-Timeframe Confirmations")
        print("      • Professional Thresholds")
        print("      • Summary Statistics")
        print("   🛑 Advanced false signal filtering")
        print("   ⚖️  Professional risk/reward calculations")
        
        return True
    else:
        print(f"\n❌ Comprehensive system demo failed")
        return False

def test_excel_output_structure():
    """Test the Excel output structure"""
    
    print(f"\n🧪 TESTING EXCEL OUTPUT STRUCTURE")
    print("=" * 80)
    
    # Check if we have a recent comprehensive analysis file
    import glob
    pattern = "comprehensive_multi_timeframe_analysis_*.xlsx"
    files = glob.glob(pattern)
    
    if files:
        latest_file = max(files, key=os.path.getctime)
        print(f"✅ Found recent analysis file: {os.path.basename(latest_file)}")
        
        try:
            import pandas as pd
            excel_file = pd.ExcelFile(latest_file)
            
            print(f"\n📊 EXCEL SHEET STRUCTURE:")
            required_sheets = [
                'ML_Enhanced_Signals',
                'Reversal_Signals', 
                'Breakout_Signals',
                'ML_Optimization_Results',
                'Multi_Timeframe_Confirmations',
                'Professional_Thresholds',
                'Summary'
            ]
            
            for sheet in required_sheets:
                if sheet in excel_file.sheet_names:
                    df = pd.read_excel(latest_file, sheet_name=sheet)
                    print(f"   ✅ {sheet}: {df.shape} ({len(df)} rows)")
                else:
                    print(f"   ❌ {sheet}: Missing")
            
            print(f"\n📈 ADDITIONAL SHEETS:")
            for sheet in excel_file.sheet_names:
                if sheet not in required_sheets:
                    df = pd.read_excel(latest_file, sheet_name=sheet)
                    print(f"   📄 {sheet}: {df.shape}")
            
            print(f"\n✅ Excel structure validation completed!")
            return True
            
        except Exception as e:
            print(f"❌ Error reading Excel file: {str(e)}")
            return False
    else:
        print(f"❌ No recent analysis files found")
        print(f"💡 Run the comprehensive demo first to generate files")
        return False

def main():
    """Main demo function"""
    
    print("🚀 COMPREHENSIVE MULTI-TIMEFRAME CONFIRMATION SYSTEM")
    print("=" * 80)
    print("📊 Complete professional trading system with multi-timeframe validation")
    print("🎯 Higher timeframe confirmation to avoid false signals")
    print("🤖 AI/ML enhanced analysis with comprehensive Excel output")
    
    # Check if we're in the right directory
    if not os.path.exists('enhanced_multi_interval_professional_analyzer.py'):
        print("❌ enhanced_multi_interval_professional_analyzer.py not found!")
        print("💡 Please run this script from the Augment directory")
        return
    
    if not os.path.exists('advanced_multi_timeframe_confirmation_system.py'):
        print("❌ advanced_multi_timeframe_confirmation_system.py not found!")
        print("💡 Please run this script from the Augment directory")
        return
    
    # Test 1: Professional timeframe thresholds
    print(f"\n🧪 TEST 1: Professional Timeframe-Specific Thresholds")
    demo_professional_timeframe_thresholds()
    
    # Test 2: Complete comprehensive system
    print(f"\n🧪 TEST 2: Complete Comprehensive System")
    test2_success = demo_comprehensive_system()
    
    if not test2_success:
        print(f"\n❌ Test 2 failed!")
        return
    
    # Test 3: Excel output structure validation
    print(f"\n🧪 TEST 3: Excel Output Structure Validation")
    test3_success = test_excel_output_structure()
    
    if test2_success and test3_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("=" * 80)
        print("🏆 Comprehensive Multi-Timeframe Confirmation System is working perfectly!")
        print("📊 Higher timeframe confirmation implemented")
        print("🎯 Professional timeframe-specific thresholds working")
        print("🤖 AI/ML enhanced timeframe combinations active")
        print("📄 Complete Excel output with all required sheets generated")
        print("🛑 Advanced false signal filtering operational")
        
        print(f"\n💡 NEXT STEPS:")
        print("   📊 Review the comprehensive Excel files for detailed analysis")
        print("   🎯 Use the multi-timeframe confirmed signals for trading")
        print("   📈 Apply the professional timeframe-specific thresholds")
        print("   🤖 Leverage the ML enhanced signals for highest probability setups")
        print("   ⚖️  Monitor the multi-timeframe confirmations for signal quality")
        
    else:
        print(f"\n❌ Some tests failed!")

if __name__ == "__main__":
    main()
