"""
Comprehensive Indicator Ranking Analyzer

This script systematically analyzes ALL 200+ technical indicators to determine
which ones are most predictive for each specific scenario:
1. Sharp Breakdown (12:54 PM)
2. Sharp Breakout (16:08 PM) 
3. Falling Knife Start (11:00 AM)

For each scenario, it ranks ALL indicators from highest to lowest predictive power
using multiple mathematical approaches and statistical measures.
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Tuple
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mutual_info_score
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveIndicatorRankingAnalyzer:
    """
    Systematically analyze and rank ALL 200+ indicators for each scenario
    """
    
    def __init__(self):
        """Initialize the comprehensive ranking analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.natural_gas_file = "technical_analysis_NATURALGAS26AUG25_MCX_signals_20250701_163713.xlsx"
        self.crude_oil_file = "technical_analysis_CRUDEOIL21JUL25_MCX_signals_20250702_012815.xlsx"
        
        # Define scenarios with exact times
        self.scenarios = {
            'sharp_breakdown': {
                'time': '12:54',
                'description': 'Sharp price breakdown with high momentum',
                'expected_direction': 'bearish'
            },
            'sharp_breakout': {
                'time': '16:08', 
                'description': 'Sharp price breakout with high momentum',
                'expected_direction': 'bullish'
            },
            'falling_knife_start': {
                'time': '11:00',
                'description': 'Beginning of sustained price decline',
                'expected_direction': 'bearish'
            }
        }
        
        # Lookback periods for analysis
        self.lookback_minutes = 10
        self.prediction_minutes = 5  # How far ahead to validate prediction
        
        print("🔬 Comprehensive Indicator Ranking Analyzer initialized")
        print(f"📊 Scenarios to analyze: {len(self.scenarios)}")
        print(f"⏰ Lookback window: {self.lookback_minutes} minutes")
        print(f"🎯 Prediction window: {self.prediction_minutes} minutes")
    
    def load_and_prepare_data(self, filename: str) -> pd.DataFrame:
        """Load and prepare data with enhanced cleaning"""
        try:
            filepath = os.path.join(self.current_dir, filename)
            if not os.path.exists(filepath):
                print(f"❌ File not found: {filepath}")
                return None
            
            print(f"📂 Loading data from: {filename}")
            
            # Load the Time_Series_Indicators sheet
            df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
            
            # Transpose so timestamps become index and indicators become columns
            df_transposed = df.set_index(df.columns[0]).T
            
            # Clean up the index - convert time strings to proper datetime
            time_index = []
            valid_indices = []
            
            for i, idx in enumerate(df_transposed.index):
                try:
                    if isinstance(idx, str) and ':' in idx:
                        time_obj = pd.to_datetime(f"2025-07-01 {idx}:00", format='%Y-%m-%d %H:%M:%S')
                        time_index.append(time_obj)
                        valid_indices.append(i)
                except:
                    continue
            
            # Filter dataframe to only include valid time entries
            df_filtered = df_transposed.iloc[valid_indices].copy()
            df_filtered.index = time_index
            
            # Convert all columns to numeric
            for col in df_filtered.columns:
                df_filtered[col] = pd.to_numeric(df_filtered[col], errors='coerce')
            
            # Remove columns that are all NaN
            df_filtered = df_filtered.dropna(axis=1, how='all')
            
            print(f"✅ Data loaded: {df_filtered.shape[0]} time points, {df_filtered.shape[1]} indicators")
            
            return df_filtered
            
        except Exception as e:
            print(f"❌ Error loading {filename}: {str(e)}")
            return None
    
    def extract_scenario_data(self, df: pd.DataFrame, scenario_time: str) -> Dict[str, pd.DataFrame]:
        """Extract pre-event and post-event data for a scenario"""
        try:
            event_time = pd.to_datetime(f"2025-07-01 {scenario_time}:00")
            
            # Pre-event window (for prediction)
            pre_start = event_time - timedelta(minutes=self.lookback_minutes)
            pre_mask = (df.index >= pre_start) & (df.index < event_time)
            pre_event_data = df[pre_mask].copy()
            
            # Post-event window (for validation)
            post_end = event_time + timedelta(minutes=self.prediction_minutes)
            post_mask = (df.index >= event_time) & (df.index <= post_end)
            post_event_data = df[post_mask].copy()
            
            # Event data (exact event time)
            event_mask = df.index == event_time
            event_data = df[event_mask].copy()
            
            return {
                'pre_event': pre_event_data,
                'post_event': post_event_data,
                'event_time': event_data,
                'event_timestamp': event_time
            }
            
        except Exception as e:
            print(f"❌ Error extracting scenario data: {str(e)}")
            return {}
    
    def calculate_indicator_predictive_power(self, 
                                           pre_event_data: pd.DataFrame,
                                           post_event_data: pd.DataFrame,
                                           expected_direction: str) -> Dict[str, float]:
        """
        Calculate predictive power for each indicator using multiple methods
        """
        predictive_scores = {}
        
        if len(pre_event_data) == 0 or len(post_event_data) == 0:
            return predictive_scores
        
        # Get price data for validation
        price_columns = ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']
        price_col = None
        for col in price_columns:
            if col in pre_event_data.columns:
                price_col = col
                break
        
        if price_col is None:
            print("⚠️ No price column found for validation")
            return predictive_scores
        
        # Calculate actual price movement for validation
        pre_price = pre_event_data[price_col].iloc[-1] if len(pre_event_data) > 0 else 0
        post_price = post_event_data[price_col].iloc[-1] if len(post_event_data) > 0 else pre_price
        actual_movement = (post_price - pre_price) / pre_price if pre_price != 0 else 0
        
        # Determine if actual movement matches expected direction
        actual_direction_correct = (
            (expected_direction == 'bearish' and actual_movement < 0) or
            (expected_direction == 'bullish' and actual_movement > 0)
        )
        
        print(f"📊 Price movement: {actual_movement:.4f} ({'✅' if actual_direction_correct else '❌'} {expected_direction})")
        
        # Analyze each indicator
        for indicator in pre_event_data.columns:
            try:
                indicator_values = pre_event_data[indicator].dropna()
                
                if len(indicator_values) < 3:
                    predictive_scores[indicator] = 0.0
                    continue
                
                # Calculate multiple predictive metrics
                scores = []
                
                # 1. Trend Analysis Score
                trend_score = self._calculate_trend_prediction_score(
                    indicator_values, expected_direction, actual_direction_correct
                )
                scores.append(trend_score)
                
                # 2. Momentum Analysis Score  
                momentum_score = self._calculate_momentum_prediction_score(
                    indicator_values, expected_direction, actual_direction_correct
                )
                scores.append(momentum_score)
                
                # 3. Volatility Analysis Score
                volatility_score = self._calculate_volatility_prediction_score(
                    indicator_values, expected_direction, actual_direction_correct
                )
                scores.append(volatility_score)
                
                # 4. Pattern Recognition Score
                pattern_score = self._calculate_pattern_prediction_score(
                    indicator_values, expected_direction, actual_direction_correct
                )
                scores.append(pattern_score)
                
                # 5. Statistical Significance Score
                statistical_score = self._calculate_statistical_prediction_score(
                    indicator_values, expected_direction, actual_direction_correct
                )
                scores.append(statistical_score)
                
                # Combine scores with weights
                weights = [0.25, 0.25, 0.2, 0.15, 0.15]  # Trend and momentum most important
                final_score = sum(score * weight for score, weight in zip(scores, weights))
                
                predictive_scores[indicator] = final_score
                
            except Exception as e:
                print(f"⚠️ Error analyzing {indicator}: {str(e)}")
                predictive_scores[indicator] = 0.0
        
        return predictive_scores
    
    def _calculate_trend_prediction_score(self, values: pd.Series, expected_direction: str, actual_correct: bool) -> float:
        """Calculate trend-based prediction score"""
        if len(values) < 3:
            return 0.0
        
        # Calculate trend using linear regression
        x = np.arange(len(values))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)
        
        # Determine predicted direction
        predicted_direction = 'bullish' if slope > 0 else 'bearish'
        direction_match = predicted_direction == expected_direction
        
        # Calculate score based on trend strength and direction accuracy
        trend_strength = abs(r_value)  # R-squared indicates trend strength
        direction_bonus = 0.5 if direction_match else 0.0
        actual_bonus = 0.3 if actual_correct else 0.0
        
        return min(trend_strength + direction_bonus + actual_bonus, 1.0)
    
    def _calculate_momentum_prediction_score(self, values: pd.Series, expected_direction: str, actual_correct: bool) -> float:
        """Calculate momentum-based prediction score"""
        if len(values) < 3:
            return 0.0
        
        # Calculate momentum using rate of change
        recent_change = values.iloc[-1] - values.iloc[-3]
        momentum_direction = 'bullish' if recent_change > 0 else 'bearish'
        
        # Calculate momentum strength
        momentum_strength = abs(recent_change) / values.std() if values.std() > 0 else 0
        momentum_strength = min(momentum_strength, 1.0)
        
        # Direction accuracy
        direction_match = momentum_direction == expected_direction
        direction_bonus = 0.4 if direction_match else 0.0
        actual_bonus = 0.3 if actual_correct else 0.0
        
        return min(momentum_strength + direction_bonus + actual_bonus, 1.0)
    
    def _calculate_volatility_prediction_score(self, values: pd.Series, expected_direction: str, actual_correct: bool) -> float:
        """Calculate volatility-based prediction score"""
        if len(values) < 5:
            return 0.0
        
        # Calculate volatility metrics
        current_vol = values.tail(3).std()
        historical_vol = values.std()
        
        # Volatility expansion/contraction
        vol_ratio = current_vol / historical_vol if historical_vol > 0 else 1.0
        
        # Score based on volatility patterns
        if vol_ratio < 0.7:  # Low volatility (potential breakout setup)
            vol_score = 0.8
        elif vol_ratio > 1.3:  # High volatility (already moving)
            vol_score = 0.4
        else:
            vol_score = 0.6
        
        actual_bonus = 0.2 if actual_correct else 0.0
        
        return min(vol_score + actual_bonus, 1.0)
    
    def _calculate_pattern_prediction_score(self, values: pd.Series, expected_direction: str, actual_correct: bool) -> float:
        """Calculate pattern-based prediction score"""
        if len(values) < 5:
            return 0.0
        
        pattern_score = 0.0
        
        # Check for specific patterns
        recent_values = values.tail(5)
        
        # Divergence pattern
        if self._detect_divergence_pattern(recent_values):
            pattern_score += 0.3
        
        # Breakout pattern
        if self._detect_breakout_pattern(recent_values):
            pattern_score += 0.3
        
        # Reversal pattern
        if self._detect_reversal_pattern(recent_values):
            pattern_score += 0.2
        
        actual_bonus = 0.2 if actual_correct else 0.0
        
        return min(pattern_score + actual_bonus, 1.0)
    
    def _calculate_statistical_prediction_score(self, values: pd.Series, expected_direction: str, actual_correct: bool) -> float:
        """Calculate statistical significance score"""
        if len(values) < 5:
            return 0.0
        
        # Test for statistical significance of recent changes
        recent_values = values.tail(3)
        historical_values = values.head(-3)
        
        if len(historical_values) < 3:
            return 0.0
        
        try:
            # T-test for significant difference
            t_stat, p_value = stats.ttest_ind(recent_values, historical_values)
            
            # Score based on statistical significance
            if p_value < 0.05:
                stat_score = 0.8
            elif p_value < 0.1:
                stat_score = 0.6
            else:
                stat_score = 0.3
            
            actual_bonus = 0.2 if actual_correct else 0.0
            
            return min(stat_score + actual_bonus, 1.0)
            
        except:
            return 0.3
    
    def _detect_divergence_pattern(self, values: pd.Series) -> bool:
        """Detect divergence patterns"""
        if len(values) < 5:
            return False
        
        # Simple divergence detection
        early_trend = values.iloc[1] - values.iloc[0]
        late_trend = values.iloc[-1] - values.iloc[-2]
        
        return early_trend * late_trend < 0  # Opposite signs
    
    def _detect_breakout_pattern(self, values: pd.Series) -> bool:
        """Detect breakout patterns"""
        if len(values) < 5:
            return False
        
        # Check for compression followed by expansion
        early_range = values.head(3).max() - values.head(3).min()
        late_range = values.tail(3).max() - values.tail(3).min()
        
        return late_range > early_range * 1.5
    
    def _detect_reversal_pattern(self, values: pd.Series) -> bool:
        """Detect reversal patterns"""
        if len(values) < 5:
            return False
        
        # Check for V-shaped or inverted V-shaped patterns
        mid_point = len(values) // 2
        first_half_trend = values.iloc[mid_point] - values.iloc[0]
        second_half_trend = values.iloc[-1] - values.iloc[mid_point]
        
        return first_half_trend * second_half_trend < 0

    def analyze_scenario_comprehensive(self, df: pd.DataFrame, scenario_name: str, scenario_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive analysis of a single scenario
        """
        print(f"\n🎯 ANALYZING SCENARIO: {scenario_name.upper()}")
        print("=" * 60)
        print(f"📅 Time: {scenario_info['time']}")
        print(f"📝 Description: {scenario_info['description']}")
        print(f"📈 Expected Direction: {scenario_info['expected_direction']}")

        # Extract scenario data
        scenario_data = self.extract_scenario_data(df, scenario_info['time'])

        if not scenario_data or len(scenario_data.get('pre_event', [])) == 0:
            print(f"❌ No data available for scenario {scenario_name}")
            return {}

        print(f"📊 Pre-event data points: {len(scenario_data['pre_event'])}")
        print(f"📊 Post-event data points: {len(scenario_data['post_event'])}")
        print(f"📊 Total indicators: {len(scenario_data['pre_event'].columns)}")

        # Calculate predictive power for all indicators
        predictive_scores = self.calculate_indicator_predictive_power(
            scenario_data['pre_event'],
            scenario_data['post_event'],
            scenario_info['expected_direction']
        )

        # Sort indicators by predictive power (high to low)
        sorted_indicators = sorted(predictive_scores.items(), key=lambda x: x[1], reverse=True)

        # Categorize indicators by effectiveness
        high_effectiveness = [(ind, score) for ind, score in sorted_indicators if score >= 0.7]
        medium_effectiveness = [(ind, score) for ind, score in sorted_indicators if 0.4 <= score < 0.7]
        low_effectiveness = [(ind, score) for ind, score in sorted_indicators if score < 0.4]

        print(f"\n📈 INDICATOR EFFECTIVENESS SUMMARY:")
        print(f"   🟢 High (≥0.7): {len(high_effectiveness)} indicators")
        print(f"   🟡 Medium (0.4-0.7): {len(medium_effectiveness)} indicators")
        print(f"   🔴 Low (<0.4): {len(low_effectiveness)} indicators")

        # Print top 10 indicators
        print(f"\n🏆 TOP 10 MOST PREDICTIVE INDICATORS:")
        for i, (indicator, score) in enumerate(sorted_indicators[:10]):
            print(f"   {i+1:2d}. {indicator:<40} | Score: {score:.4f}")

        return {
            'scenario_name': scenario_name,
            'scenario_info': scenario_info,
            'all_indicators_ranked': sorted_indicators,
            'high_effectiveness': high_effectiveness,
            'medium_effectiveness': medium_effectiveness,
            'low_effectiveness': low_effectiveness,
            'total_indicators_analyzed': len(predictive_scores),
            'data_quality': {
                'pre_event_points': len(scenario_data['pre_event']),
                'post_event_points': len(scenario_data['post_event']),
                'indicators_count': len(scenario_data['pre_event'].columns)
            }
        }

    def analyze_all_scenarios_comprehensive(self) -> Dict[str, Any]:
        """
        Analyze all scenarios comprehensively for both datasets
        """
        print("\n🚀 STARTING COMPREHENSIVE INDICATOR RANKING ANALYSIS")
        print("=" * 80)
        print("Analyzing ALL 200+ indicators for each scenario...")
        print("Ranking from highest to lowest predictive power...")

        all_results = {}

        # Analyze Natural Gas data
        print(f"\n📊 ANALYZING NATURAL GAS DATA")
        print("=" * 50)
        ng_data = self.load_and_prepare_data(self.natural_gas_file)

        if ng_data is not None:
            for scenario_name, scenario_info in self.scenarios.items():
                result = self.analyze_scenario_comprehensive(ng_data, f"ng_{scenario_name}", scenario_info)
                if result:
                    all_results[f"natural_gas_{scenario_name}"] = result

        # Analyze Crude Oil data
        print(f"\n🛢️ ANALYZING CRUDE OIL DATA")
        print("=" * 50)
        co_data = self.load_and_prepare_data(self.crude_oil_file)

        if co_data is not None:
            for scenario_name, scenario_info in self.scenarios.items():
                result = self.analyze_scenario_comprehensive(co_data, f"co_{scenario_name}", scenario_info)
                if result:
                    all_results[f"crude_oil_{scenario_name}"] = result

        return all_results

    def generate_comprehensive_ranking_report(self, all_results: Dict[str, Any]) -> str:
        """
        Generate comprehensive ranking report for all scenarios
        """
        print(f"\n📊 GENERATING COMPREHENSIVE RANKING REPORT")
        print("=" * 60)

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        report = f"""# COMPREHENSIVE INDICATOR RANKING ANALYSIS REPORT
Generated: {timestamp}

## EXECUTIVE SUMMARY
This report provides a comprehensive ranking of ALL 200+ technical indicators
for each trading scenario, sorted from highest to lowest predictive power.

## METHODOLOGY
- **Trend Analysis**: Linear regression slope and R-squared
- **Momentum Analysis**: Rate of change and momentum strength
- **Volatility Analysis**: Volatility expansion/contraction patterns
- **Pattern Recognition**: Divergence, breakout, and reversal patterns
- **Statistical Significance**: T-tests for significant changes

## DETAILED SCENARIO ANALYSIS

"""

        # Generate detailed analysis for each scenario
        for scenario_key, result in all_results.items():
            if not result:
                continue

            scenario_name = result['scenario_name']
            scenario_info = result['scenario_info']
            all_indicators = result['all_indicators_ranked']
            high_eff = result['high_effectiveness']
            medium_eff = result['medium_effectiveness']
            low_eff = result['low_effectiveness']

            report += f"""
### {scenario_key.upper().replace('_', ' ')}
**Time**: {scenario_info['time']} | **Direction**: {scenario_info['expected_direction']} | **Description**: {scenario_info['description']}

**Effectiveness Summary**:
- 🟢 High Effectiveness (≥0.7): {len(high_eff)} indicators
- 🟡 Medium Effectiveness (0.4-0.7): {len(medium_eff)} indicators
- 🔴 Low Effectiveness (<0.4): {len(low_eff)} indicators

#### TOP 20 MOST PREDICTIVE INDICATORS:
"""

            # Add top 20 indicators
            for i, (indicator, score) in enumerate(all_indicators[:20]):
                effectiveness_emoji = "🟢" if score >= 0.7 else "🟡" if score >= 0.4 else "🔴"
                report += f"{i+1:2d}. {effectiveness_emoji} **{indicator}** | Score: {score:.4f}\n"

            # Add high effectiveness indicators if any
            if high_eff:
                report += f"\n#### HIGH EFFECTIVENESS INDICATORS (≥0.7):\n"
                for i, (indicator, score) in enumerate(high_eff):
                    report += f"{i+1}. {indicator} | Score: {score:.4f}\n"

            report += "\n" + "-" * 80 + "\n"

        # Add cross-scenario analysis
        report += self._generate_cross_scenario_analysis(all_results)

        # Add implementation recommendations
        report += """
## IMPLEMENTATION RECOMMENDATIONS

### Priority Indicators (Across All Scenarios):
1. **Focus on High Effectiveness Indicators** (Score ≥ 0.7)
2. **Use Multiple Confirmation** from different indicator categories
3. **Implement Scenario-Specific Rules** based on top-ranked indicators
4. **Monitor Cross-Asset Consistency** between Natural Gas and Crude Oil

### Trading System Integration:
1. **Primary Signals**: Use top 5 indicators for each scenario
2. **Confirmation Signals**: Use top 6-15 indicators for confirmation
3. **Filter Signals**: Use medium effectiveness indicators as filters
4. **Risk Management**: Incorporate volatility-based indicators

### Next Steps:
1. Backtest top-ranked indicators on additional datasets
2. Develop automated alert systems for high-scoring combinations
3. Implement machine learning models using top indicators as features
4. Create real-time monitoring dashboards for scenario detection
"""

        return report

    def _generate_cross_scenario_analysis(self, all_results: Dict[str, Any]) -> str:
        """Generate cross-scenario analysis section"""

        # Find indicators that perform well across multiple scenarios
        indicator_performance = {}

        for scenario_key, result in all_results.items():
            if not result or 'all_indicators_ranked' not in result:
                continue

            for indicator, score in result['all_indicators_ranked']:
                if indicator not in indicator_performance:
                    indicator_performance[indicator] = []
                indicator_performance[indicator].append((scenario_key, score))

        # Find consistently high-performing indicators
        consistent_performers = []
        for indicator, performances in indicator_performance.items():
            if len(performances) >= 3:  # Appears in at least 3 scenarios
                avg_score = np.mean([score for _, score in performances])
                min_score = min([score for _, score in performances])
                if avg_score >= 0.5 and min_score >= 0.3:  # Consistently decent performance
                    consistent_performers.append((indicator, avg_score, min_score, len(performances)))

        # Sort by average score
        consistent_performers.sort(key=lambda x: x[1], reverse=True)

        cross_analysis = f"""
## CROSS-SCENARIO ANALYSIS

### CONSISTENTLY HIGH-PERFORMING INDICATORS:
These indicators show strong predictive power across multiple scenarios:

"""

        for i, (indicator, avg_score, min_score, count) in enumerate(consistent_performers[:15]):
            cross_analysis += f"{i+1:2d}. **{indicator}**\n"
            cross_analysis += f"    - Average Score: {avg_score:.4f}\n"
            cross_analysis += f"    - Minimum Score: {min_score:.4f}\n"
            cross_analysis += f"    - Scenarios: {count}\n\n"

        return cross_analysis

    def save_comprehensive_results(self, all_results: Dict[str, Any], report: str):
        """Save comprehensive results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save detailed JSON results
        json_filename = f"comprehensive_indicator_rankings_{timestamp}.json"
        with open(os.path.join(self.current_dir, json_filename), 'w') as f:
            json.dump(all_results, f, indent=2, default=str)

        # Save markdown report
        report_filename = f"comprehensive_indicator_rankings_report_{timestamp}.md"
        with open(os.path.join(self.current_dir, report_filename), 'w') as f:
            f.write(report)

        # Save CSV rankings for each scenario
        for scenario_key, result in all_results.items():
            if result and 'all_indicators_ranked' in result:
                csv_filename = f"indicator_rankings_{scenario_key}_{timestamp}.csv"

                # Create DataFrame
                rankings_df = pd.DataFrame(
                    result['all_indicators_ranked'],
                    columns=['Indicator', 'Predictive_Score']
                )
                rankings_df['Rank'] = range(1, len(rankings_df) + 1)
                rankings_df['Effectiveness_Category'] = rankings_df['Predictive_Score'].apply(
                    lambda x: 'High' if x >= 0.7 else 'Medium' if x >= 0.4 else 'Low'
                )

                # Reorder columns
                rankings_df = rankings_df[['Rank', 'Indicator', 'Predictive_Score', 'Effectiveness_Category']]

                # Save to CSV
                rankings_df.to_csv(os.path.join(self.current_dir, csv_filename), index=False)

        print(f"💾 Results saved:")
        print(f"   📄 Detailed JSON: {json_filename}")
        print(f"   📊 Report: {report_filename}")
        print(f"   📈 CSV Rankings: {len(all_results)} files")

    def run_complete_comprehensive_analysis(self) -> Dict[str, Any]:
        """
        Run complete comprehensive analysis of all indicators for all scenarios
        """
        print("\n🎯 STARTING COMPLETE COMPREHENSIVE INDICATOR ANALYSIS")
        print("=" * 80)
        print("🔬 Analyzing ALL 200+ indicators for predictive power")
        print("📊 Ranking from highest to lowest effectiveness")
        print("🎯 Covering all scenarios: Breakdown, Breakout, Falling Knife")

        # Run comprehensive analysis
        all_results = self.analyze_all_scenarios_comprehensive()

        if not all_results:
            print("❌ No results generated")
            return {}

        # Generate comprehensive report
        report = self.generate_comprehensive_ranking_report(all_results)

        # Save all results
        self.save_comprehensive_results(all_results, report)

        # Print summary
        print(f"\n🏆 ANALYSIS COMPLETE!")
        print(f"📊 Scenarios analyzed: {len(all_results)}")

        total_indicators = 0
        for result in all_results.values():
            if result and 'total_indicators_analyzed' in result:
                total_indicators = max(total_indicators, result['total_indicators_analyzed'])

        print(f"🔬 Total indicators analyzed: {total_indicators}")
        print(f"📈 All indicators ranked from highest to lowest predictive power")
        print(f"💾 Detailed results saved to files")

        return all_results


def main():
    """
    Main execution function
    """
    print("🔬 Comprehensive Indicator Ranking Analyzer")
    print("=" * 60)
    print("Systematically analyzing ALL 200+ indicators for each scenario")
    print("Ranking by predictive effectiveness from highest to lowest")

    # Initialize analyzer
    analyzer = ComprehensiveIndicatorRankingAnalyzer()

    # Run complete analysis
    results = analyzer.run_complete_comprehensive_analysis()

    if results:
        print("\n✅ Comprehensive analysis completed successfully!")
        print("📊 All indicators ranked for each scenario")
        print("📈 Check generated files for detailed rankings")
    else:
        print("\n❌ Analysis failed. Please check data files.")


if __name__ == "__main__":
    main()
