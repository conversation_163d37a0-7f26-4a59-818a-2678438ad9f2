"""
Demo script to run advanced analyzer with ALL indicators
"""

from advanced_interactive_multi_timeframe_analyzer import AdvancedInteractiveMultiTimeframe<PERSON>naly<PERSON>

def run_demo():
    """Run demo with predefined inputs"""
    
    # Create analyzer
    analyzer = AdvancedInteractiveMultiTimeframeAnalyzer()
    
    # Predefined inputs for demo
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '01-07-2025',
        'timeframes': ['1', '5', '15', '30'],
        'indicators': 'ALL',
        'use_custom_thresholds': False,
        'validate_signals': True
    }
    
    print("🚀 DEMO: Advanced Multi-Timeframe Analysis with ALL Indicators")
    print("=" * 80)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Timeframes: {', '.join(inputs['timeframes'])} minutes")
    print(f"🔍 Indicators: {inputs['indicators']}")
    
    # Find data files
    data_files = analyzer.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])
    
    if not data_files:
        print("❌ No matching data files found")
        return {}
    
    # Assign timeframes to files
    timeframe_files = analyzer.assign_timeframes_to_files(data_files, inputs['timeframes'])
    
    if len(timeframe_files) < 2:
        print("❌ Need at least 2 timeframes for analysis")
        return {}
    
    # Load timeframe data
    timeframe_data = analyzer.load_timeframe_data(timeframe_files)
    
    if len(timeframe_data) < 2:
        print("❌ Failed to load sufficient timeframe data")
        return {}
    
    # Filter indicators (ALL)
    filtered_indicators = analyzer.filter_indicators(timeframe_data, inputs['indicators'])
    
    if not filtered_indicators:
        print("❌ No indicators found matching criteria")
        return {}
    
    # Use default thresholds
    thresholds = analyzer.default_thresholds
    
    # Perform hierarchical analysis
    analysis_results = analyzer.hierarchical_timeframe_analysis(
        timeframe_data, filtered_indicators, thresholds
    )
    
    # Add threshold configuration to results
    analysis_results['thresholds_used'] = thresholds
    
    # Validate signals
    if inputs['validate_signals']:
        analysis_results = analyzer.validate_signals_with_price_movement(
            timeframe_data, analysis_results
        )
    
    # Perform advanced correlation analysis
    analysis_results = analyzer.advanced_correlation_analysis(
        timeframe_data, analysis_results
    )
    
    # Export to Excel
    excel_filename = analyzer.export_to_excel(analysis_results, inputs)
    
    # Print summary
    analyzer.print_analysis_summary(analysis_results)
    
    print(f"\n💾 Demo results saved to: {excel_filename}")
    
    return analysis_results

if __name__ == "__main__":
    results = run_demo()
    
    if results:
        print("\n✅ Demo completed successfully!")
        print("📊 Check the Excel file for comprehensive results with ALL indicators analyzed!")
    else:
        print("\n❌ Demo failed.")
