"""
Demo Enhanced Multi-Interval Professional Analyzer
- Provides predefined inputs to test the functionality
- Generates separate files for each interval
- Runs advanced professional analysis
"""

import subprocess
import os
import time
import glob
import shutil
from datetime import datetime
from enhanced_multi_interval_professional_analyzer import EnhancedMultiIntervalProfessionalAnalyzer

def demo_with_predefined_inputs():
    """Demo with predefined inputs to test functionality"""
    
    print("🚀 DEMO: Enhanced Multi-Interval Professional Analyzer")
    print("=" * 80)
    print("📊 Using predefined inputs for demonstration")
    
    # Predefined inputs
    inputs = {
        'ticker': 'NATURALGAS26AUG25',
        'exchange': 'MCX',
        'date': '03-07-2025',
        'start_time': '10:00',
        'end_time': '22:32',
        'intervals': ['1', '5', '15', '30'],  # Test with 4 intervals
        'run_advanced_analysis': True
    }
    
    print(f"\n📝 PREDEFINED INPUTS:")
    print("=" * 40)
    print(f"📊 Ticker: {inputs['ticker']}")
    print(f"🏢 Exchange: {inputs['exchange']}")
    print(f"📅 Date: {inputs['date']}")
    print(f"⏰ Time: {inputs['start_time']} - {inputs['end_time']}")
    print(f"📈 Intervals: {', '.join(inputs['intervals'])} minutes")
    print(f"🎯 Advanced Analysis: {inputs['run_advanced_analysis']}")
    
    # Create analyzer instance
    analyzer = EnhancedMultiIntervalProfessionalAnalyzer()
    
    # Step 1: Generate interval data
    print(f"\n🔄 STEP 1: GENERATING SEPARATE FILES FOR EACH INTERVAL")
    print("=" * 80)
    
    interval_files = analyzer.generate_interval_data(inputs)
    
    if not interval_files:
        print("❌ No interval files generated")
        return False
    
    # Step 2: Run advanced analysis
    if inputs['run_advanced_analysis']:
        print(f"\n🎯 STEP 2: ADVANCED PROFESSIONAL SIGNAL ANALYSIS")
        print("=" * 80)
        
        success = analyzer.run_advanced_professional_analysis(inputs, interval_files)
        
        if success:
            print(f"\n✅ DEMO COMPLETED SUCCESSFULLY!")
            print("=" * 80)
            print("🎯 DEMO SUMMARY:")
            print(f"   📊 Step 1: ✅ {len(interval_files)} separate interval files generated")
            print(f"   🎯 Step 2: ✅ Advanced professional signal analysis completed")
            
            print(f"\n📄 GENERATED FILES:")
            for interval, filename in interval_files.items():
                print(f"   📄 {interval}: {filename}")
            
            print(f"\n🏆 KEY ACHIEVEMENTS:")
            print("   📈 Separate files for each interval (no sampling)")
            print("   ⏰ Proper interval-specific data for each timeframe")
            print("   🔄 PGO-style reversal detection with real data")
            print("   📊 Data-driven threshold optimization per interval")
            print("   🎯 Professional signal analysis with timing")
            
            return True
        else:
            print(f"\n⚠️  Interval files generated but advanced analysis failed")
            return False
    else:
        print(f"\n✅ INTERVAL DATA GENERATION COMPLETED!")
        print("=" * 60)
        print(f"📊 Generated {len(interval_files)} separate interval files")
        return True

def test_individual_interval_generation():
    """Test generating individual interval files"""
    
    print(f"\n🧪 TESTING INDIVIDUAL INTERVAL GENERATION")
    print("=" * 80)
    
    # Test parameters
    ticker = 'NATURALGAS26AUG25'
    exchange = 'MCX'
    date = '03-07-2025'
    start_time = '10:00'
    end_time = '22:32'
    test_interval = '5'  # Test with 5-minute interval
    
    print(f"📊 Testing {test_interval}-minute interval generation")
    print(f"📊 Ticker: {ticker}")
    print(f"🏢 Exchange: {exchange}")
    print(f"📅 Date: {date}")
    print(f"⏰ Time: {start_time} - {end_time}")
    
    # Build command
    cmd = [
        'python', 'integrated_technical_analyzer.py',
        '--mode', 'analysis',
        '--analysis-type', 'signals',
        '--ticker', ticker,
        '--exchange', exchange,
        '--date', date,
        '--method', 'strategy_all',
        '--interval', test_interval,
        '--start-time', start_time,
        '--end-time', end_time
    ]
    
    print(f"\n🔧 Command: {' '.join(cmd)}")
    
    try:
        print(f"⏳ Executing command...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {test_interval}-minute data generation completed!")
            
            # Find the generated file
            pattern = f"technical_analysis_{ticker}_{exchange}_signals_*.xlsx"
            files = glob.glob(pattern)
            
            if files:
                # Get the most recent file
                latest_file = max(files, key=os.path.getctime)
                
                # Create new filename with interval
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_filename = f"technical_analysis_{ticker}_{exchange}_signals_{test_interval}min_{timestamp}.xlsx"
                
                # Rename the file
                shutil.move(latest_file, new_filename)
                
                print(f"📄 File renamed to: {new_filename}")
                
                # Show file info
                file_size = os.path.getsize(new_filename) / (1024 * 1024)  # MB
                print(f"📊 File size: {file_size:.2f} MB")
                
                return True
            else:
                print(f"❌ No output file found")
                return False
        else:
            print(f"❌ Command failed!")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Command timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main demo function"""
    
    print("🚀 ENHANCED MULTI-INTERVAL PROFESSIONAL ANALYZER DEMO")
    print("=" * 80)
    print("🧪 Testing separate file generation for each interval")
    print("📊 Testing advanced professional signal analysis")
    print("⏰ Testing proper interval-specific data handling")
    
    # Check if we're in the right directory
    if not os.path.exists('integrated_technical_analyzer.py'):
        print("❌ integrated_technical_analyzer.py not found!")
        print("💡 Please run this script from the Augment directory")
        return
    
    if not os.path.exists('enhanced_multi_interval_professional_analyzer.py'):
        print("❌ enhanced_multi_interval_professional_analyzer.py not found!")
        print("💡 Please run this script from the Augment directory")
        return
    
    # Test 1: Individual interval generation
    print(f"\n🧪 TEST 1: Individual Interval Generation")
    test1_success = test_individual_interval_generation()
    
    if test1_success:
        print(f"✅ Test 1 passed!")
    else:
        print(f"❌ Test 1 failed!")
        return
    
    # Wait a moment
    time.sleep(2)
    
    # Test 2: Full demo with multiple intervals
    print(f"\n🧪 TEST 2: Full Multi-Interval Demo")
    test2_success = demo_with_predefined_inputs()
    
    if test2_success:
        print(f"✅ Test 2 passed!")
        print(f"\n🎉 ALL TESTS PASSED!")
        print("=" * 80)
        print("🏆 Enhanced Multi-Interval Professional Analyzer is working perfectly!")
        print("📊 Separate files generated for each interval")
        print("🎯 Advanced professional signal analysis completed")
        print("⏰ Proper interval-specific data handling verified")
    else:
        print(f"❌ Test 2 failed!")

if __name__ == "__main__":
    main()
