"""
Debug Progressive Calculation

This script tests what indicators are actually being calculated by the progressive calculator.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data with enough history for indicators"""
    start_time = datetime(2025, 6, 24, 10, 0)
    times = [start_time + timedelta(minutes=i) for i in range(120)]  # 2 hours of data
    
    np.random.seed(42)
    base_price = 1200
    
    data = []
    for i, time in enumerate(times):
        close = base_price + np.random.normal(0, 5) + i * 0.1  # Slight uptrend
        open_price = close + np.random.normal(0, 2)
        high = max(open_price, close) + abs(np.random.normal(0, 3))
        low = min(open_price, close) - abs(np.random.normal(0, 3))
        volume = int(2000 + np.random.normal(0, 500))
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    return pd.DataFrame(data)

def test_progressive_calculation():
    """Test what indicators are actually being calculated"""
    print("🧪 TESTING PROGRESSIVE CALCULATION")
    print("=" * 60)
    
    # Create test data
    market_data = create_test_data()
    print(f"📊 Created test data: {len(market_data)} candles")
    print(f"🕐 Time range: {market_data['time'].iloc[0]} to {market_data['time'].iloc[-1]}")
    
    # Initialize calculator
    calculator = CompleteProgressiveIndicatorsCalculator()
    
    # Test with a few time periods (use later periods that have enough history)
    test_times = ['10:30', '11:00', '11:30']  # 30, 60, 90 minutes into the data
    
    print(f"\n🔄 Testing progressive calculation for times: {test_times}")
    
    try:
        # Test the progressive calculation
        results = calculator.calculate_all_indicators_progressive(
            market_data, test_times, 1, None, 'strategy_all'
        )
        
        if results:
            print(f"✅ Progressive calculation completed!")
            print(f"📊 Results for {len(results)} time periods")
            
            # Analyze each time period
            for time_period, time_data in results.items():
                print(f"\n🕐 TIME PERIOD: {time_period}")
                print("-" * 40)
                
                indicators = time_data.get('indicators', {})
                price_data = time_data.get('price_data', {})
                
                print(f"📈 Price data: {price_data}")
                print(f"📊 Total indicators: {len(indicators)}")
                
                if indicators:
                    # Group indicators by type
                    indicator_types = {}
                    for indicator_name, value in indicators.items():
                        # Extract the base indicator name
                        base_name = indicator_name.split('_')[0].lower()
                        if base_name not in indicator_types:
                            indicator_types[base_name] = []
                        indicator_types[base_name].append((indicator_name, value))
                    
                    print(f"📂 Indicator types found: {len(indicator_types)}")
                    
                    # Check for major indicators
                    major_indicators = ['rsi', 'sma', 'ema', 'macd', 'atr', 'bbands', 'stoch']
                    
                    for major in major_indicators:
                        if major in indicator_types:
                            indicators_of_type = indicator_types[major]
                            print(f"   ✅ {major.upper()}: {len(indicators_of_type)} variants")
                            for name, value in indicators_of_type[:3]:  # Show first 3
                                print(f"      {name}: {value:.4f}")
                        else:
                            print(f"   ❌ {major.upper()}: Not found")
                    
                    # Show all indicator types
                    print(f"\n📋 All indicator types:")
                    for indicator_type, indicators_list in sorted(indicator_types.items()):
                        print(f"   {indicator_type}: {len(indicators_list)} indicators")
                
                else:
                    print(f"❌ No indicators calculated for {time_period}")
                
                print()
        
        else:
            print(f"❌ Progressive calculation failed - no results")
            
    except Exception as e:
        print(f"❌ Error in progressive calculation: {str(e)}")
        import traceback
        traceback.print_exc()

def test_single_indicator():
    """Test a single indicator calculation to see if pandas-ta is working"""
    print("\n🔬 TESTING SINGLE INDICATOR CALCULATION")
    print("=" * 60)
    
    # Create simple test data
    market_data = create_test_data()
    
    try:
        import pandas_ta as ta
        
        # Test RSI calculation directly
        close = market_data['Close']
        print(f"📊 Testing RSI calculation with {len(close)} data points")
        
        rsi_result = ta.rsi(close, length=14)
        print(f"📈 RSI result type: {type(rsi_result)}")
        print(f"📈 RSI result length: {len(rsi_result) if rsi_result is not None else 'None'}")
        
        if rsi_result is not None and not rsi_result.empty:
            # Show last few values
            last_values = rsi_result.tail(5)
            print(f"📈 Last 5 RSI values:")
            for i, (idx, val) in enumerate(last_values.items()):
                time_str = market_data.iloc[idx]['time'].strftime('%H:%M')
                print(f"   {time_str}: {val:.4f}")
        else:
            print(f"❌ RSI calculation failed")
        
        # Test SMA calculation
        print(f"\n📊 Testing SMA calculation")
        sma_result = ta.sma(close, length=10)
        print(f"📈 SMA result type: {type(sma_result)}")
        print(f"📈 SMA result length: {len(sma_result) if sma_result is not None else 'None'}")
        
        if sma_result is not None and not sma_result.empty:
            last_values = sma_result.tail(5)
            print(f"📈 Last 5 SMA values:")
            for i, (idx, val) in enumerate(last_values.items()):
                time_str = market_data.iloc[idx]['time'].strftime('%H:%M')
                print(f"   {time_str}: {val:.4f}")
        else:
            print(f"❌ SMA calculation failed")
            
    except Exception as e:
        print(f"❌ Error in single indicator test: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    try:
        test_single_indicator()
        test_progressive_calculation()
        
        print(f"\n🎯 SUMMARY:")
        print("This test helps identify why major indicators are missing from the Excel output.")
        print("Expected: RSI, SMA, EMA, MACD, ATR should be calculated")
        print("Current: Only basic indicators are appearing")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
