#!/usr/bin/env python3
"""
Test script for the new Automated All Indicators functionality

This script demonstrates the new strategy_all_automated method that uses:
- df.ta.cores = 6 for optimal performance
- df.ta.strategy("All", timed=True) for comprehensive analysis
- 280+ technical indicators with categorization
- Enhanced Excel export with summary sheets

Usage:
    python test_automated_all_indicators.py
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_automated_all_indicators():
    """Test the new automated all indicators functionality"""
    try:
        logger.info("🚀 Testing Automated All Indicators Functionality")
        
        # Import the integrated analyzer
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        # Initialize analyzer
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Test parameters
        ticker = "BATAINDIA"
        exchange = "BSE"
        date = "24-06-2025"  # Use a recent trading day
        
        logger.info(f"📊 Testing with: {ticker} on {exchange} for {date}")
        
        # Test the automated all indicators method
        logger.info("🔧 Testing strategy_all_automated method...")
        
        result = analyzer.analyze_with_market_data(
            ticker=ticker,
            exchange=exchange,
            date=date,
            mode='full',
            method='strategy_all_automated',
            categories=None,  # Not needed for automated method
            start_time="09:15",
            end_time="15:30"
        )
        
        if 'error' in result:
            logger.error(f"❌ Test failed: {result['error']}")
            return False
        
        # Display results summary
        logger.info("✅ Automated All Indicators Analysis Completed!")
        
        metadata = result.get('metadata', {})
        logger.info(f"📊 Data points analyzed: {metadata.get('data_points', 'Unknown')}")
        
        if 'indicators' in result:
            total_indicators = len(result['indicators'])
            logger.info(f"📈 Total indicators calculated: {total_indicators}")
            
            # Show categorized breakdown if available
            if 'categorized_indicators' in result:
                logger.info("📂 Indicators by category:")
                for category, indicators in result['categorized_indicators'].items():
                    logger.info(f"   {category}: {len(indicators)} indicators")
            
            # Show performance info if available
            if 'performance_settings' in result:
                perf = result['performance_settings']
                logger.info(f"⚙️ Performance settings:")
                logger.info(f"   Cores: {perf.get('cores', 'Unknown')}")
                logger.info(f"   Timed: {perf.get('timed', 'Unknown')}")
                calc_time = perf.get('calculation_time_seconds')
                if calc_time:
                    logger.info(f"   Calculation time: {calc_time:.2f} seconds")
        
        # Test Excel export
        logger.info("📊 Testing Excel export...")
        excel_file = analyzer.export_to_excel(result)
        
        if excel_file:
            logger.info(f"✅ Excel export successful: {excel_file}")
            
            # Check if file exists
            if os.path.exists(excel_file):
                file_size = os.path.getsize(excel_file) / 1024  # KB
                logger.info(f"📁 File size: {file_size:.1f} KB")
            else:
                logger.warning("⚠️ Excel file not found after export")
        else:
            logger.warning("⚠️ Excel export failed")
        
        logger.info("🎉 Test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        return False

def test_comparison_with_regular_method():
    """Compare automated method with regular strategy_all method"""
    try:
        logger.info("🔄 Comparing automated vs regular strategy_all methods...")
        
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        analyzer = IntegratedTechnicalAnalyzer()
        
        ticker = "BATAINDIA"
        exchange = "BSE"
        date = "24-06-2025"
        
        # Test regular strategy_all
        logger.info("📊 Testing regular strategy_all...")
        result_regular = analyzer.analyze_with_market_data(
            ticker=ticker,
            exchange=exchange,
            date=date,
            mode='full',
            method='strategy_all'
        )
        
        # Test automated strategy_all
        logger.info("🚀 Testing strategy_all_automated...")
        result_automated = analyzer.analyze_with_market_data(
            ticker=ticker,
            exchange=exchange,
            date=date,
            mode='full',
            method='strategy_all_automated'
        )
        
        # Compare results
        if 'error' not in result_regular and 'error' not in result_automated:
            regular_count = len(result_regular.get('indicators', {}))
            automated_count = len(result_automated.get('indicators', {}))
            
            logger.info(f"📊 Comparison Results:")
            logger.info(f"   Regular method: {regular_count} indicators")
            logger.info(f"   Automated method: {automated_count} indicators")
            logger.info(f"   Difference: {automated_count - regular_count} indicators")
            
            # Check for categorization in automated method
            if 'categorized_indicators' in result_automated:
                logger.info(f"✅ Automated method includes categorization")
            else:
                logger.info(f"⚠️ Automated method missing categorization")
                
        else:
            logger.warning("⚠️ One or both methods failed, cannot compare")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Comparison test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    logger.info("🧪 Starting Automated All Indicators Tests")
    
    # Test 1: Basic functionality
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Basic Automated All Indicators Functionality")
    logger.info("="*50)
    
    success1 = test_automated_all_indicators()
    
    # Test 2: Comparison with regular method
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Comparison with Regular Method")
    logger.info("="*50)
    
    success2 = test_comparison_with_regular_method()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    logger.info(f"Test 1 (Basic functionality): {'✅ PASSED' if success1 else '❌ FAILED'}")
    logger.info(f"Test 2 (Comparison): {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        logger.info("🎉 All tests passed! Automated All Indicators is ready to use.")
        logger.info("\n💡 Usage examples:")
        logger.info("   python integrated_technical_analyzer.py --mode analysis --analysis-type full --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method strategy_all_automated")
        logger.info("   python integrated_technical_analyzer.py --mode historical --ticker RELIANCE --exchange NSE --date 24-06-2025 --method strategy_all_automated")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
