"""
Advanced Interactive Multi-Timeframe Analyzer

Features:
- Interactive CLI for all inputs (ticker, exchange, date, timeframes, indicators)
- Direction-based hierarchical analysis (higher timeframe first)
- Custom threshold levels for each indicator
- Signal validation with actual price movement
- Advanced correlation analysis
- Excel output with comprehensive results
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
import argparse
from sklearn.metrics import accuracy_score, precision_score, recall_score
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class AdvancedInteractiveMultiTimeframeAnalyzer:
    """
    Advanced interactive multi-timeframe analyzer with CLI inputs
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Professional threshold levels for each indicator type
        self.default_thresholds = {
            # Oscillators (0-100 scale)
            'RSI_14': {'buy': 30, 'sell': 70, 'threshold_pct': 10},
            'MFI_14': {'buy': 20, 'sell': 80, 'threshold_pct': 10},
            'STOCH_14_3_STOCHk_14_3_3': {'buy': 20, 'sell': 80, 'threshold_pct': 10},
            'STOCHRSI_14_3_3_STOCHRSIk_14_14_3_3': {'buy': 20, 'sell': 80, 'threshold_pct': 10},
            
            # Momentum Oscillators (-100 to +100 or similar)
            'CCI_14': {'buy': -100, 'sell': 100, 'threshold_pct': 15},
            'WILLR_14': {'buy': -80, 'sell': -20, 'threshold_pct': 10},
            'CMO_14': {'buy': -50, 'sell': 50, 'threshold_pct': 15},
            
            # Trend Strength
            'ADX_14_ADX_14': {'buy': 25, 'sell': 25, 'threshold_pct': 20},
            'ADX_14_DMP_14': {'buy': 25, 'sell': 25, 'threshold_pct': 15},
            'ADX_14_DMN_14': {'buy': 25, 'sell': 25, 'threshold_pct': 15},
            
            # MACD Family
            'MACD_12_26_9_MACD_12_26_9': {'buy': 0, 'sell': 0, 'threshold_pct': 25},
            'MACD_12_26_9_MACDh_12_26_9': {'buy': 0, 'sell': 0, 'threshold_pct': 30},
            'PPO_12_26_9_PPOh_12_26_9': {'buy': 0, 'sell': 0, 'threshold_pct': 25},
            
            # Bollinger Bands
            'BBANDS_5_2_BBP_5_2.0': {'buy': 0.2, 'sell': 0.8, 'threshold_pct': 15},
            'BBANDS_5_2_BBB_5_2.0': {'buy': 0.1, 'sell': 0.1, 'threshold_pct': 20},
            
            # Squeeze Indicators (Binary)
            'SQUEEZE_SQZ_OFF': {'buy': 1, 'sell': 1, 'threshold_pct': 0},
            'SQUEEZE_PRO_SQZPRO_OFF': {'buy': 1, 'sell': 1, 'threshold_pct': 0},
            
            # Aroon
            'AROON_14_AROONU_14': {'buy': 70, 'sell': 30, 'threshold_pct': 15},
            'AROON_14_AROOND_14': {'buy': 30, 'sell': 70, 'threshold_pct': 15},
            'AROON_14_AROONOSC_14': {'buy': 50, 'sell': -50, 'threshold_pct': 20},
            
            # Vortex
            'VORTEX_14_VTXP_14': {'buy': 1.0, 'sell': 1.0, 'threshold_pct': 10},
            
            # Statistical
            'ZSCORE_30_1': {'buy': -2, 'sell': 2, 'threshold_pct': 15},
            'SKEW_30': {'buy': -1, 'sell': 1, 'threshold_pct': 25},
            'KURTOSIS_30': {'buy': 3, 'sell': 3, 'threshold_pct': 20},
        }
        
        print("🚀 Advanced Interactive Multi-Timeframe Analyzer initialized")
    
    def get_user_inputs(self) -> Dict[str, Any]:
        """Get all inputs from user via CLI"""
        print("\n📝 INTERACTIVE INPUT COLLECTION")
        print("=" * 60)
        
        inputs = {}
        
        # Ticker input
        inputs['ticker'] = input("📊 Enter ticker symbol (e.g., NATURALGAS26AUG25, CRUDEOIL21JUL25): ").strip().upper()
        
        # Exchange input
        print("\n🏢 Available exchanges: NSE, BSE, MCX, NFO")
        inputs['exchange'] = input("🏢 Enter exchange: ").strip().upper()
        
        # Date input
        inputs['date'] = input("📅 Enter date (DD-MM-YYYY format, e.g., 01-07-2025): ").strip()
        
        # Timeframes input
        print("\n⏰ Available timeframes: 1, 3, 5, 10, 15, 30, 60, 120, 240 minutes")
        timeframes_input = input("⏰ Enter timeframes (comma-separated, e.g., 1,5,15,30): ").strip()
        inputs['timeframes'] = [tf.strip() for tf in timeframes_input.split(',')]
        
        # Indicators input
        print("\n🔍 Indicator selection:")
        print("   • Enter 'ALL' for all indicators")
        print("   • Enter specific indicators (comma-separated)")
        print("   • Enter keywords to search (e.g., ADX,RSI,MACD)")
        indicators_input = input("🔍 Enter indicators: ").strip().upper()
        
        if indicators_input == 'ALL':
            inputs['indicators'] = 'ALL'
        else:
            inputs['indicators'] = [ind.strip() for ind in indicators_input.split(',')]
        
        # Custom thresholds
        print("\n🎯 Threshold configuration:")
        use_custom = input("🎯 Use custom thresholds? (y/n, default=n): ").strip().lower()
        inputs['use_custom_thresholds'] = use_custom == 'y'
        
        if inputs['use_custom_thresholds']:
            inputs['custom_thresholds'] = self.get_custom_thresholds()
        
        # Signal validation
        print("\n✅ Signal validation:")
        validate_signals = input("✅ Validate signals with price movement? (y/n, default=y): ").strip().lower()
        inputs['validate_signals'] = validate_signals != 'n'
        
        return inputs
    
    def get_custom_thresholds(self) -> Dict[str, Dict]:
        """Get custom threshold levels from user"""
        print("\n🎯 CUSTOM THRESHOLD CONFIGURATION")
        print("=" * 50)
        print("For each indicator, enter buy/sell levels and threshold percentage")
        print("Example: RSI_14 - Buy: 30, Sell: 70, Threshold: 10%")
        
        custom_thresholds = {}
        
        while True:
            indicator = input("\nIndicator name (or 'done' to finish): ").strip()
            if indicator.lower() == 'done':
                break
            
            try:
                buy_level = float(input(f"Buy level for {indicator}: "))
                sell_level = float(input(f"Sell level for {indicator}: "))
                threshold_pct = float(input(f"Threshold percentage for {indicator}: "))
                
                custom_thresholds[indicator] = {
                    'buy': buy_level,
                    'sell': sell_level,
                    'threshold_pct': threshold_pct
                }
                print(f"✅ Added thresholds for {indicator}")
                
            except ValueError:
                print("❌ Invalid input. Please enter numeric values.")
        
        return custom_thresholds
    
    def find_data_files(self, ticker: str, exchange: str, date: str) -> List[str]:
        """Find data files matching the criteria"""
        print(f"\n🔍 SEARCHING FOR DATA FILES")
        print("=" * 50)
        
        # Convert date format for file matching
        try:
            date_obj = datetime.strptime(date, '%d-%m-%Y')
            date_str = date_obj.strftime('%Y%m%d')
        except:
            print(f"❌ Invalid date format: {date}")
            return []
        
        matching_files = []
        
        for file in os.listdir(self.current_dir):
            if (ticker in file and 
                exchange in file and 
                file.endswith('.xlsx') and 
                not file.startswith('~$')):
                
                # Check if file has required sheet
                try:
                    filepath = os.path.join(self.current_dir, file)
                    excel_file = pd.ExcelFile(filepath)
                    
                    if 'Time_Series_Indicators' in excel_file.sheet_names:
                        df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                        if df.shape[1] > 50:  # Should have many indicators
                            matching_files.append(file)
                            print(f"✅ Found: {file} ({df.shape[1]} indicators)")
                except Exception as e:
                    print(f"⚠️ Error checking {file}: {str(e)}")
        
        return matching_files
    
    def assign_timeframes_to_files(self, files: List[str], requested_timeframes: List[str]) -> Dict[str, str]:
        """Assign timeframes to available files"""
        print(f"\n📊 ASSIGNING TIMEFRAMES TO FILES")
        print("=" * 50)
        
        timeframe_files = {}
        
        # If we have enough files, assign them to requested timeframes
        for i, timeframe in enumerate(requested_timeframes):
            if i < len(files):
                timeframe_files[f"{timeframe}min"] = files[i]
                print(f"📈 {timeframe}min: {files[i]}")
            else:
                print(f"⚠️ No file available for {timeframe}min timeframe")
        
        return timeframe_files
    
    def load_timeframe_data(self, timeframe_files: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load data from timeframe files"""
        print(f"\n📂 LOADING TIMEFRAME DATA")
        print("=" * 50)
        
        timeframe_data = {}
        
        for timeframe, filename in timeframe_files.items():
            try:
                filepath = os.path.join(self.current_dir, filename)
                df = pd.read_excel(filepath, sheet_name='Time_Series_Indicators')
                
                # Transpose if needed (indicators as columns, time as rows)
                if len(df.columns) > len(df):
                    df_transposed = df.set_index(df.columns[0]).T
                else:
                    first_col = df.iloc[:, 0]
                    if any(':' in str(val) for val in first_col.head(10)):
                        df_transposed = df.set_index(df.columns[0]).T
                    else:
                        df_transposed = df
                
                # Clean data
                for col in df_transposed.columns:
                    df_transposed[col] = pd.to_numeric(df_transposed[col], errors='coerce')
                
                timeframe_data[timeframe] = df_transposed
                print(f"✅ {timeframe}: {df_transposed.shape}")
                
            except Exception as e:
                print(f"❌ Error loading {timeframe}: {str(e)}")
        
        return timeframe_data
    
    def filter_indicators(self, timeframe_data: Dict[str, pd.DataFrame], 
                         indicator_filter: Any) -> List[str]:
        """Filter indicators based on user input"""
        print(f"\n🔍 FILTERING INDICATORS")
        print("=" * 50)
        
        # Get all available indicators
        all_indicators = set()
        for df in timeframe_data.values():
            all_indicators.update(df.columns)
        
        if indicator_filter == 'ALL':
            filtered_indicators = list(all_indicators)
            print(f"📊 Using ALL {len(filtered_indicators)} indicators")
        else:
            # Search for indicators matching keywords
            filtered_indicators = []
            for keyword in indicator_filter:
                matching = [ind for ind in all_indicators if keyword in ind]
                filtered_indicators.extend(matching)
                print(f"🔍 '{keyword}' matched {len(matching)} indicators")
            
            # Remove duplicates
            filtered_indicators = list(set(filtered_indicators))
            print(f"📊 Total filtered indicators: {len(filtered_indicators)}")
        
        return filtered_indicators

    def detect_signal_with_threshold(self, indicator: str, value: float,
                                   thresholds: Dict[str, Dict]) -> Tuple[str, float]:
        """
        Detect buy/sell signal with threshold consideration
        Returns (signal_type, signal_strength)
        """
        if indicator not in thresholds:
            return 'NEUTRAL', 0.0

        threshold_config = thresholds[indicator]
        buy_level = threshold_config['buy']
        sell_level = threshold_config['sell']
        threshold_pct = threshold_config['threshold_pct'] / 100

        # Calculate threshold ranges
        buy_threshold = buy_level * (1 + threshold_pct) if buy_level > 0 else buy_level * (1 - threshold_pct)
        sell_threshold = sell_level * (1 + threshold_pct) if sell_level > 0 else sell_level * (1 - threshold_pct)

        # Determine signal
        if 'RSI' in indicator or 'MFI' in indicator or 'STOCH' in indicator:
            # For oscillators: buy when low, sell when high
            if value <= buy_threshold:
                strength = min((buy_threshold - value) / buy_threshold, 1.0) if buy_threshold != 0 else 1.0
                return 'BUY', strength
            elif value >= sell_threshold:
                strength = min((value - sell_threshold) / sell_threshold, 1.0) if sell_threshold != 0 else 1.0
                return 'SELL', strength

        elif 'CCI' in indicator:
            # CCI: buy below -100, sell above +100
            if value <= buy_level - (abs(buy_level) * threshold_pct):
                strength = min(abs(value - buy_level) / abs(buy_level), 1.0)
                return 'BUY', strength
            elif value >= sell_level + (abs(sell_level) * threshold_pct):
                strength = min(abs(value - sell_level) / abs(sell_level), 1.0)
                return 'SELL', strength

        elif 'WILLR' in indicator:
            # Williams %R: buy when oversold, sell when overbought
            if value <= buy_level - (abs(buy_level) * threshold_pct):
                strength = min(abs(value - buy_level) / abs(buy_level), 1.0)
                return 'BUY', strength
            elif value >= sell_level + (abs(sell_level) * threshold_pct):
                strength = min(abs(value - sell_level) / abs(sell_level), 1.0)
                return 'SELL', strength

        elif 'MACD' in indicator or 'PPO' in indicator:
            # MACD/PPO: buy when positive, sell when negative
            if value > 0:
                return 'BUY', min(abs(value) / 1.0, 1.0)
            elif value < 0:
                return 'SELL', min(abs(value) / 1.0, 1.0)

        elif 'ADX' in indicator:
            # ADX: trend strength indicator
            if value >= buy_level * (1 - threshold_pct):
                strength = min(value / 50, 1.0)  # Normalize to 50
                return 'STRONG_TREND', strength

        elif 'SQUEEZE' in indicator and 'OFF' in indicator:
            # Squeeze release
            if value == 1:
                return 'BREAKOUT', 1.0

        elif 'BBANDS' in indicator and 'BBP' in indicator:
            # Bollinger Band Position
            if value <= buy_level + threshold_pct:
                return 'BUY', min((buy_level - value + threshold_pct) / threshold_pct, 1.0)
            elif value >= sell_level - threshold_pct:
                return 'SELL', min((value - sell_level + threshold_pct) / threshold_pct, 1.0)

        elif 'AROON' in indicator:
            # Aroon indicators
            if 'AROONU' in indicator and value >= sell_level * (1 - threshold_pct):
                return 'BUY', min(value / 100, 1.0)
            elif 'AROOND' in indicator and value >= sell_level * (1 - threshold_pct):
                return 'SELL', min(value / 100, 1.0)

        return 'NEUTRAL', 0.0

    def hierarchical_timeframe_analysis(self, timeframe_data: Dict[str, pd.DataFrame],
                                      indicators: List[str],
                                      thresholds: Dict[str, Dict]) -> Dict[str, Any]:
        """
        Perform hierarchical analysis: higher timeframe direction first
        """
        print(f"\n🎯 HIERARCHICAL TIMEFRAME ANALYSIS")
        print("=" * 50)

        # Sort timeframes by duration (highest to lowest)
        timeframe_order = sorted(timeframe_data.keys(),
                               key=lambda x: int(x.replace('min', '')),
                               reverse=True)

        print(f"📊 Timeframe hierarchy: {' → '.join(timeframe_order)}")

        analysis_results = {
            'timeframe_order': timeframe_order,
            'direction_analysis': {},
            'confluence_analysis': {},
            'signal_summary': {}
        }

        # Step 1: Determine overall direction from highest timeframe
        highest_tf = timeframe_order[0]
        print(f"\n🔍 Analyzing direction from {highest_tf} (highest timeframe)")

        direction_signals = {}
        for indicator in indicators:
            if indicator in timeframe_data[highest_tf].columns:
                values = timeframe_data[highest_tf][indicator].dropna()
                if len(values) > 0:
                    latest_value = values.iloc[-1]
                    signal_type, strength = self.detect_signal_with_threshold(
                        indicator, latest_value, thresholds
                    )

                    if signal_type in ['BUY', 'SELL']:
                        direction_signals[indicator] = {
                            'signal': signal_type,
                            'strength': strength,
                            'value': latest_value
                        }

        # Determine overall market direction
        buy_signals = sum(1 for sig in direction_signals.values() if sig['signal'] == 'BUY')
        sell_signals = sum(1 for sig in direction_signals.values() if sig['signal'] == 'SELL')

        if buy_signals > sell_signals:
            overall_direction = 'BUY'
        elif sell_signals > buy_signals:
            overall_direction = 'SELL'
        else:
            overall_direction = 'NEUTRAL'

        analysis_results['direction_analysis'] = {
            'highest_timeframe': highest_tf,
            'overall_direction': overall_direction,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'direction_signals': direction_signals
        }

        print(f"📈 Overall direction from {highest_tf}: {overall_direction}")
        print(f"   Buy signals: {buy_signals}, Sell signals: {sell_signals}")

        # Step 2: Analyze confluence in the direction of higher timeframe
        if overall_direction != 'NEUTRAL':
            print(f"\n🔍 Analyzing {overall_direction} confluence across all timeframes")

            confluence_results = {}

            for indicator in indicators:
                confluence_info = {
                    'timeframes_with_signal': [],
                    'signal_strength': {},
                    'confluence_score': 0.0,
                    'direction_match': False
                }

                total_timeframes = 0
                matching_signals = 0

                for timeframe in timeframe_order:
                    if indicator in timeframe_data[timeframe].columns:
                        total_timeframes += 1
                        values = timeframe_data[timeframe][indicator].dropna()

                        if len(values) > 0:
                            latest_value = values.iloc[-1]
                            signal_type, strength = self.detect_signal_with_threshold(
                                indicator, latest_value, thresholds
                            )

                            # Check if signal matches overall direction
                            if signal_type == overall_direction:
                                matching_signals += 1
                                confluence_info['timeframes_with_signal'].append(timeframe)
                                confluence_info['signal_strength'][timeframe] = strength
                                confluence_info['direction_match'] = True

                # Calculate confluence score
                if total_timeframes > 0:
                    confluence_info['confluence_score'] = matching_signals / total_timeframes
                    confluence_results[indicator] = confluence_info

            analysis_results['confluence_analysis'] = confluence_results

        return analysis_results

    def validate_signals_with_price_movement(self, timeframe_data: Dict[str, pd.DataFrame],
                                           analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate signals by checking actual price movement in 1-minute timeframe
        """
        print(f"\n✅ VALIDATING SIGNALS WITH PRICE MOVEMENT")
        print("=" * 50)

        # Find 1-minute data for validation
        one_min_data = None
        for tf, data in timeframe_data.items():
            if '1min' in tf:
                one_min_data = data
                break

        if one_min_data is None:
            print("⚠️ No 1-minute data available for validation")
            return analysis_results

        # Get price columns
        price_columns = ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']
        price_col = None
        for col in price_columns:
            if col in one_min_data.columns:
                price_col = col
                break

        if price_col is None:
            print("⚠️ No price column found for validation")
            return analysis_results

        print(f"📊 Using {price_col} for price movement validation")

        # Get price data
        price_data = one_min_data[price_col].dropna()
        if len(price_data) < 10:
            print("⚠️ Insufficient price data for validation")
            return analysis_results

        # Define validation windows
        validation_windows = {
            'very_quick': (3, 10),    # 3-10 minutes
            'quick': (11, 20),        # 11-20 minutes
            'moderate': (21, 40),     # 21-40 minutes
            'slow': (41, 60)          # 41+ minutes
        }

        validation_results = {}
        overall_direction = analysis_results['direction_analysis']['overall_direction']

        if overall_direction in ['BUY', 'SELL']:
            print(f"🎯 Validating {overall_direction} signals...")

            # Get current price (latest available)
            current_price = price_data.iloc[-1]

            # Check price movement in different windows
            for window_name, (start_min, end_min) in validation_windows.items():
                if len(price_data) > end_min:
                    # Get price at the start of validation window
                    start_price = price_data.iloc[-(start_min + 1)]
                    end_price = price_data.iloc[-(end_min + 1)] if len(price_data) > end_min else price_data.iloc[0]

                    # Calculate price movement
                    price_change = (current_price - start_price) / start_price * 100

                    # Determine if movement matches signal direction
                    movement_correct = False
                    if overall_direction == 'BUY' and price_change > 0.1:  # At least 0.1% up
                        movement_correct = True
                    elif overall_direction == 'SELL' and price_change < -0.1:  # At least 0.1% down
                        movement_correct = True

                    validation_results[window_name] = {
                        'price_change_pct': price_change,
                        'movement_correct': movement_correct,
                        'start_price': start_price,
                        'current_price': current_price
                    }

                    status = "✅" if movement_correct else "❌"
                    print(f"   {window_name}: {price_change:.2f}% {status}")

        analysis_results['signal_validation'] = validation_results
        return analysis_results

    def advanced_correlation_analysis(self, timeframe_data: Dict[str, pd.DataFrame],
                                    analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform advanced correlation analysis using ML models
        """
        print(f"\n🧠 ADVANCED CORRELATION ANALYSIS")
        print("=" * 50)

        # Prepare data for ML analysis
        ml_results = {}

        try:
            # Get 1-minute price data for target variable
            one_min_data = None
            for tf, data in timeframe_data.items():
                if '1min' in tf:
                    one_min_data = data
                    break

            if one_min_data is None:
                print("⚠️ No 1-minute data for ML analysis")
                return analysis_results

            # Get price column
            price_columns = ['Close', 'CURRENT_PRICE', 'HLC3', 'OHLC4']
            price_col = None
            for col in price_columns:
                if col in one_min_data.columns:
                    price_col = col
                    break

            if price_col is None:
                print("⚠️ No price column for ML analysis")
                return analysis_results

            # Calculate future returns (target variable)
            price_data = one_min_data[price_col].dropna()
            future_returns = price_data.pct_change(periods=5).shift(-5)  # 5-minute future return

            # Prepare feature matrix from confluence analysis
            confluence_data = analysis_results.get('confluence_analysis', {})

            if not confluence_data:
                print("⚠️ No confluence data for ML analysis")
                return analysis_results

            # Create feature matrix
            features = []
            feature_names = []

            for indicator, info in confluence_data.items():
                if info['confluence_score'] > 0:
                    features.append(info['confluence_score'])
                    feature_names.append(f"{indicator}_confluence")

            if len(features) < 5:
                print("⚠️ Insufficient features for ML analysis")
                return analysis_results

            # Create feature matrix (repeat for each time point)
            n_samples = min(len(future_returns.dropna()), 50)  # Use last 50 samples
            X = np.array([features] * n_samples)
            y = future_returns.dropna().tail(n_samples).values

            if len(y) < 10:
                print("⚠️ Insufficient samples for ML analysis")
                return analysis_results

            # Split data
            split_idx = int(len(y) * 0.7)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]

            # Train models
            models = {
                'linear_regression': LinearRegression(),
                'random_forest': RandomForestRegressor(n_estimators=50, random_state=42)
            }

            model_results = {}

            for model_name, model in models.items():
                try:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)

                    # Calculate metrics
                    mse = np.mean((y_test - y_pred) ** 2)
                    r2 = model.score(X_test, y_test)

                    # Direction accuracy
                    y_test_direction = np.sign(y_test)
                    y_pred_direction = np.sign(y_pred)
                    direction_accuracy = accuracy_score(y_test_direction, y_pred_direction)

                    model_results[model_name] = {
                        'mse': mse,
                        'r2_score': r2,
                        'direction_accuracy': direction_accuracy,
                        'feature_importance': getattr(model, 'feature_importances_', None)
                    }

                    print(f"📊 {model_name}: R² = {r2:.3f}, Direction Accuracy = {direction_accuracy:.3f}")

                except Exception as e:
                    print(f"❌ Error training {model_name}: {str(e)}")

            ml_results = {
                'models': model_results,
                'feature_names': feature_names,
                'n_samples': n_samples
            }

        except Exception as e:
            print(f"❌ Error in ML analysis: {str(e)}")

        analysis_results['ml_analysis'] = ml_results
        return analysis_results

    def export_to_excel(self, analysis_results: Dict[str, Any], inputs: Dict[str, Any]) -> str:
        """
        Export comprehensive results to Excel format
        """
        print(f"\n📊 EXPORTING RESULTS TO EXCEL")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"advanced_multi_timeframe_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: Summary
                summary_data = {
                    'Parameter': ['Ticker', 'Exchange', 'Date', 'Timeframes', 'Total Indicators',
                                'Overall Direction', 'Confluence Score'],
                    'Value': [
                        inputs['ticker'],
                        inputs['exchange'],
                        inputs['date'],
                        ', '.join(inputs['timeframes']),
                        len(analysis_results.get('confluence_analysis', {})),
                        analysis_results['direction_analysis']['overall_direction'],
                        f"{np.mean([info['confluence_score'] for info in analysis_results.get('confluence_analysis', {}).values()]):.3f}"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Sheet 2: Direction Analysis
                direction_data = []
                direction_signals = analysis_results['direction_analysis']['direction_signals']
                for indicator, info in direction_signals.items():
                    direction_data.append({
                        'Indicator': indicator,
                        'Signal': info['signal'],
                        'Strength': info['strength'],
                        'Value': info['value']
                    })

                if direction_data:
                    direction_df = pd.DataFrame(direction_data)
                    direction_df = direction_df.sort_values('Strength', ascending=False)
                    direction_df.to_excel(writer, sheet_name='Direction_Analysis', index=False)

                # Sheet 3: Confluence Analysis (sorted by score)
                confluence_data = []
                confluence_analysis = analysis_results.get('confluence_analysis', {})
                for indicator, info in confluence_analysis.items():
                    confluence_data.append({
                        'Indicator': indicator,
                        'Confluence_Score': info['confluence_score'],
                        'Timeframes_With_Signal': ', '.join(info['timeframes_with_signal']),
                        'Direction_Match': info['direction_match'],
                        'Signal_Strength': ', '.join([f"{tf}:{strength:.3f}"
                                                    for tf, strength in info['signal_strength'].items()])
                    })

                if confluence_data:
                    confluence_df = pd.DataFrame(confluence_data)
                    confluence_df = confluence_df.sort_values('Confluence_Score', ascending=False)
                    confluence_df.to_excel(writer, sheet_name='Confluence_Analysis', index=False)

                # Sheet 4: Signal Validation
                validation_data = analysis_results.get('signal_validation', {})
                if validation_data:
                    validation_list = []
                    for window, info in validation_data.items():
                        validation_list.append({
                            'Time_Window': window,
                            'Price_Change_Pct': info['price_change_pct'],
                            'Movement_Correct': info['movement_correct'],
                            'Start_Price': info['start_price'],
                            'Current_Price': info['current_price']
                        })

                    validation_df = pd.DataFrame(validation_list)
                    validation_df.to_excel(writer, sheet_name='Signal_Validation', index=False)

                # Sheet 5: ML Analysis
                ml_data = analysis_results.get('ml_analysis', {})
                if ml_data and 'models' in ml_data:
                    ml_list = []
                    for model_name, metrics in ml_data['models'].items():
                        ml_list.append({
                            'Model': model_name,
                            'R2_Score': metrics['r2_score'],
                            'MSE': metrics['mse'],
                            'Direction_Accuracy': metrics['direction_accuracy']
                        })

                    ml_df = pd.DataFrame(ml_list)
                    ml_df.to_excel(writer, sheet_name='ML_Analysis', index=False)

                # Sheet 6: Perfect Confluence Indicators
                perfect_confluence = [indicator for indicator, info in confluence_analysis.items()
                                    if info['confluence_score'] == 1.0]

                if perfect_confluence:
                    perfect_df = pd.DataFrame({
                        'Perfect_Confluence_Indicators': perfect_confluence
                    })
                    perfect_df.to_excel(writer, sheet_name='Perfect_Confluence', index=False)

                # Sheet 7: Threshold Configuration
                threshold_data = []
                thresholds = analysis_results.get('thresholds_used', {})
                for indicator, config in thresholds.items():
                    threshold_data.append({
                        'Indicator': indicator,
                        'Buy_Level': config['buy'],
                        'Sell_Level': config['sell'],
                        'Threshold_Pct': config['threshold_pct']
                    })

                if threshold_data:
                    threshold_df = pd.DataFrame(threshold_data)
                    threshold_df.to_excel(writer, sheet_name='Threshold_Config', index=False)

            print(f"✅ Excel file saved: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error exporting to Excel: {str(e)}")
            return ""

    def run_complete_analysis(self) -> Dict[str, Any]:
        """
        Run complete interactive multi-timeframe analysis
        """
        print("\n🚀 ADVANCED INTERACTIVE MULTI-TIMEFRAME ANALYZER")
        print("=" * 80)

        # Get user inputs
        inputs = self.get_user_inputs()

        # Find data files
        data_files = self.find_data_files(inputs['ticker'], inputs['exchange'], inputs['date'])

        if not data_files:
            print("❌ No matching data files found")
            return {}

        # Assign timeframes to files
        timeframe_files = self.assign_timeframes_to_files(data_files, inputs['timeframes'])

        if len(timeframe_files) < 2:
            print("❌ Need at least 2 timeframes for analysis")
            return {}

        # Load timeframe data
        timeframe_data = self.load_timeframe_data(timeframe_files)

        if len(timeframe_data) < 2:
            print("❌ Failed to load sufficient timeframe data")
            return {}

        # Filter indicators
        filtered_indicators = self.filter_indicators(timeframe_data, inputs['indicators'])

        if not filtered_indicators:
            print("❌ No indicators found matching criteria")
            return {}

        # Prepare thresholds
        if inputs['use_custom_thresholds']:
            thresholds = inputs['custom_thresholds']
        else:
            thresholds = self.default_thresholds

        # Perform hierarchical analysis
        analysis_results = self.hierarchical_timeframe_analysis(
            timeframe_data, filtered_indicators, thresholds
        )

        # Add threshold configuration to results
        analysis_results['thresholds_used'] = thresholds

        # Validate signals if requested
        if inputs['validate_signals']:
            analysis_results = self.validate_signals_with_price_movement(
                timeframe_data, analysis_results
            )

        # Perform advanced correlation analysis
        analysis_results = self.advanced_correlation_analysis(
            timeframe_data, analysis_results
        )

        # Export to Excel
        excel_filename = self.export_to_excel(analysis_results, inputs)

        # Print summary
        self.print_analysis_summary(analysis_results)

        # Save JSON backup
        json_filename = f"advanced_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump(analysis_results, f, indent=2, default=str)

        print(f"\n💾 Results saved:")
        print(f"   📊 Excel: {excel_filename}")
        print(f"   📄 JSON: {json_filename}")

        return analysis_results

    def print_analysis_summary(self, analysis_results: Dict[str, Any]):
        """Print comprehensive analysis summary"""
        print(f"\n📊 ANALYSIS SUMMARY")
        print("=" * 60)

        # Direction analysis
        direction_analysis = analysis_results['direction_analysis']
        print(f"🎯 Overall Direction: {direction_analysis['overall_direction']}")
        print(f"📈 Buy Signals: {direction_analysis['buy_signals']}")
        print(f"📉 Sell Signals: {direction_analysis['sell_signals']}")

        # Confluence analysis
        confluence_analysis = analysis_results.get('confluence_analysis', {})
        if confluence_analysis:
            perfect_confluence = [ind for ind, info in confluence_analysis.items()
                                if info['confluence_score'] == 1.0]
            high_confluence = [ind for ind, info in confluence_analysis.items()
                             if 0.7 <= info['confluence_score'] < 1.0]

            print(f"\n⭐ Perfect Confluence (1.0): {len(perfect_confluence)} indicators")
            print(f"🟢 High Confluence (≥0.7): {len(high_confluence)} indicators")

            if perfect_confluence:
                print("   Perfect confluence indicators:")
                for ind in perfect_confluence[:5]:  # Show top 5
                    print(f"   • {ind}")

        # Signal validation
        validation_results = analysis_results.get('signal_validation', {})
        if validation_results:
            print(f"\n✅ SIGNAL VALIDATION RESULTS:")
            for window, info in validation_results.items():
                status = "✅" if info['movement_correct'] else "❌"
                print(f"   {window}: {info['price_change_pct']:.2f}% {status}")

        # ML analysis
        ml_results = analysis_results.get('ml_analysis', {})
        if ml_results and 'models' in ml_results:
            print(f"\n🧠 ML ANALYSIS RESULTS:")
            for model_name, metrics in ml_results['models'].items():
                print(f"   {model_name}: Direction Accuracy = {metrics['direction_accuracy']:.3f}")


def main():
    """
    Main execution function
    """
    analyzer = AdvancedInteractiveMultiTimeframeAnalyzer()
    results = analyzer.run_complete_analysis()

    if results:
        print("\n✅ Advanced multi-timeframe analysis completed successfully!")
    else:
        print("\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
