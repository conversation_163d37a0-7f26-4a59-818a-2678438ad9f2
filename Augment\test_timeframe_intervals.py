"""
Test Script for Different Timeframe Intervals in Technical Analysis

This script demonstrates how different candle intervals (1, 3, 5, 10, 15, 30, 60, 120, 240 minutes)
affect technical indicator calculations and shows meaningful variations.

Usage:
    python test_timeframe_intervals.py
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_sample_ohlcv_data(num_candles=100, base_price=1200):
    """
    Generate sample OHLCV data for testing different timeframes
    """
    logger.info(f"📊 Generating {num_candles} sample candles with base price {base_price}")
    
    # Generate realistic price movements
    np.random.seed(42)  # For reproducible results
    
    # Create time series
    start_time = datetime(2025, 6, 24, 9, 15)
    times = [start_time + timedelta(minutes=i) for i in range(num_candles)]
    
    # Generate price data with realistic movements
    close_prices = []
    high_prices = []
    low_prices = []
    open_prices = []
    volumes = []
    
    current_price = base_price
    
    for i in range(num_candles):
        # Random walk with some trend
        price_change = np.random.normal(0, 5)  # Random change with std dev of 5
        current_price += price_change
        
        # Ensure price doesn't go too low
        current_price = max(current_price, base_price * 0.8)
        
        # Generate OHLC for this candle
        open_price = current_price
        
        # High and low with some randomness
        high_range = np.random.uniform(2, 8)
        low_range = np.random.uniform(2, 8)
        
        high_price = open_price + high_range
        low_price = open_price - low_range
        
        # Close price somewhere between high and low
        close_price = np.random.uniform(low_price + 1, high_price - 1)
        current_price = close_price
        
        # Volume with some randomness
        volume = np.random.randint(1000, 10000)
        
        open_prices.append(round(open_price, 2))
        high_prices.append(round(high_price, 2))
        low_prices.append(round(low_price, 2))
        close_prices.append(round(close_price, 2))
        volumes.append(volume)
    
    # Create DataFrame
    df = pd.DataFrame({
        'time': times,
        'Open': open_prices,
        'High': high_prices,
        'Low': low_prices,
        'Close': close_prices,
        'Volume': volumes
    })
    
    logger.info(f"✅ Generated sample data: {len(df)} candles from {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
    return df

def resample_to_timeframe(df, interval_minutes):
    """
    Resample 1-minute data to different timeframes
    """
    logger.info(f"🔄 Resampling data to {interval_minutes}-minute timeframe")
    
    # Set time as index
    df_resampled = df.set_index('time')
    
    # Resample to the specified interval
    interval_str = f'{interval_minutes}T'  # T for minutes in pandas
    
    resampled = df_resampled.resample(interval_str).agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    logger.info(f"✅ Resampled to {len(resampled)} {interval_minutes}-minute candles")
    return resampled.reset_index()

def calculate_key_indicators(df, timeframe_name):
    """
    Calculate key technical indicators for comparison
    """
    logger.info(f"📈 Calculating indicators for {timeframe_name}")
    
    # Ensure we have enough data
    if len(df) < 50:
        logger.warning(f"⚠️ Insufficient data for {timeframe_name}: {len(df)} candles")
        return None
    
    try:
        # Calculate key indicators
        indicators = {}
        
        # Moving Averages
        indicators['SMA_20'] = ta.sma(df['Close'], length=20).iloc[-1] if len(df) >= 20 else None
        indicators['EMA_20'] = ta.ema(df['Close'], length=20).iloc[-1] if len(df) >= 20 else None
        
        # Momentum Indicators
        indicators['RSI_14'] = ta.rsi(df['Close'], length=14).iloc[-1] if len(df) >= 14 else None
        
        # MACD
        macd_result = ta.macd(df['Close'])
        if macd_result is not None and len(macd_result) > 0:
            indicators['MACD'] = macd_result['MACD_12_26_9'].iloc[-1] if 'MACD_12_26_9' in macd_result.columns else None
            indicators['MACD_Signal'] = macd_result['MACDs_12_26_9'].iloc[-1] if 'MACDs_12_26_9' in macd_result.columns else None
        
        # Bollinger Bands
        bb_result = ta.bbands(df['Close'], length=20)
        if bb_result is not None and len(bb_result) > 0:
            indicators['BB_Upper'] = bb_result['BBU_20_2.0'].iloc[-1] if 'BBU_20_2.0' in bb_result.columns else None
            indicators['BB_Lower'] = bb_result['BBL_20_2.0'].iloc[-1] if 'BBL_20_2.0' in bb_result.columns else None
        
        # Volatility
        indicators['ATR_14'] = ta.atr(df['High'], df['Low'], df['Close'], length=14).iloc[-1] if len(df) >= 14 else None
        
        # Volume indicators
        indicators['OBV'] = ta.obv(df['Close'], df['Volume']).iloc[-1] if len(df) > 1 else None
        
        # Price information
        indicators['Current_Price'] = df['Close'].iloc[-1]
        indicators['Price_Change'] = df['Close'].iloc[-1] - df['Close'].iloc[0] if len(df) > 1 else 0
        indicators['Price_Change_Pct'] = ((df['Close'].iloc[-1] - df['Close'].iloc[0]) / df['Close'].iloc[0] * 100) if len(df) > 1 else 0
        
        # Remove None values and round numbers
        cleaned_indicators = {}
        for key, value in indicators.items():
            if value is not None and not pd.isna(value):
                if isinstance(value, (int, float)):
                    cleaned_indicators[key] = round(float(value), 4)
                else:
                    cleaned_indicators[key] = value
        
        logger.info(f"✅ Calculated {len(cleaned_indicators)} indicators for {timeframe_name}")
        return cleaned_indicators
        
    except Exception as e:
        logger.error(f"❌ Error calculating indicators for {timeframe_name}: {str(e)}")
        return None

def test_different_timeframes():
    """
    Test technical indicators across different timeframes
    """
    print("🚀 Testing Technical Indicators Across Different Timeframes")
    print("=" * 80)
    
    # Generate base 1-minute data
    base_data = generate_sample_ohlcv_data(num_candles=375)  # ~6 hours of 1-minute data
    
    # Test different intervals
    intervals = [1, 3, 5, 10, 15, 30, 60]  # Minutes
    
    results = {}
    
    for interval in intervals:
        print(f"\n📊 Testing {interval}-minute timeframe...")
        print("-" * 50)
        
        if interval == 1:
            # Use original data for 1-minute
            timeframe_data = base_data
        else:
            # Resample to the target timeframe
            timeframe_data = resample_to_timeframe(base_data, interval)
        
        # Calculate indicators
        indicators = calculate_key_indicators(timeframe_data, f"{interval}-minute")
        
        if indicators:
            results[f"{interval}min"] = indicators
            
            # Display key metrics
            print(f"📈 Data Points: {len(timeframe_data)}")
            print(f"💰 Current Price: {indicators.get('Current_Price', 'N/A')}")
            print(f"📊 RSI(14): {indicators.get('RSI_14', 'N/A')}")
            print(f"📈 SMA(20): {indicators.get('SMA_20', 'N/A')}")
            print(f"📉 EMA(20): {indicators.get('EMA_20', 'N/A')}")
            print(f"🎯 MACD: {indicators.get('MACD', 'N/A')}")
            print(f"📊 ATR(14): {indicators.get('ATR_14', 'N/A')}")
            print(f"📈 Price Change: {indicators.get('Price_Change_Pct', 'N/A')}%")
        else:
            print(f"❌ Failed to calculate indicators for {interval}-minute timeframe")
    
    # Compare results
    print(f"\n📋 COMPARISON SUMMARY")
    print("=" * 80)
    
    comparison_metrics = ['RSI_14', 'SMA_20', 'EMA_20', 'MACD', 'ATR_14']
    
    for metric in comparison_metrics:
        print(f"\n📊 {metric}:")
        for timeframe, indicators in results.items():
            value = indicators.get(metric, 'N/A')
            print(f"   {timeframe:>8}: {value}")
    
    # Show differences
    print(f"\n🔍 KEY OBSERVATIONS:")
    print("-" * 50)
    
    if '1min' in results and '15min' in results:
        rsi_1min = results['1min'].get('RSI_14')
        rsi_15min = results['15min'].get('RSI_14')
        
        if rsi_1min and rsi_15min:
            rsi_diff = abs(rsi_1min - rsi_15min)
            print(f"📊 RSI Difference (1min vs 15min): {rsi_diff:.2f}")
            
        sma_1min = results['1min'].get('SMA_20')
        sma_15min = results['15min'].get('SMA_20')
        
        if sma_1min and sma_15min:
            sma_diff = abs(sma_1min - sma_15min)
            print(f"📈 SMA(20) Difference (1min vs 15min): {sma_diff:.2f}")
    
    print(f"\n✅ Analysis complete! Different timeframes show different indicator values.")
    print(f"💡 This demonstrates why interval selection is crucial for technical analysis.")
    
    return results

if __name__ == "__main__":
    try:
        results = test_different_timeframes()
        
        print(f"\n📖 EXPLANATION:")
        print("=" * 80)
        print("🔍 Why values differ across timeframes:")
        print("   • 1-minute: Most sensitive, captures every small movement")
        print("   • 5-minute: Smooths out noise, more reliable signals")
        print("   • 15-minute: Even smoother, good for trend identification")
        print("   • 30-minute+: Very smooth, best for major trend analysis")
        print()
        print("📊 For real market analysis:")
        print("   • Use shorter timeframes (1-5min) for scalping")
        print("   • Use medium timeframes (15-30min) for day trading")
        print("   • Use longer timeframes (60min+) for swing trading")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")
