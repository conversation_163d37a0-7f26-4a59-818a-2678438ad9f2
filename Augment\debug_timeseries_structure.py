"""
Debug Time-Series Structure

This script tests the time-series structure to understand why the Excel export is not working.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from integrated_technical_analyzer import IntegratedTechnicalAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create simple test data with enough history for indicators"""
    start_time = datetime(2025, 6, 24, 11, 0)
    times = [start_time + timedelta(minutes=i * 5) for i in range(60)]  # More data for indicators
    
    np.random.seed(42)
    base_price = 1200
    
    data = []
    for i, time in enumerate(times):
        close = base_price + np.random.normal(0, 5)
        open_price = close + np.random.normal(0, 2)
        high = max(open_price, close) + abs(np.random.normal(0, 3))
        low = min(open_price, close) - abs(np.random.normal(0, 3))
        volume = int(2000 + np.random.normal(0, 500))
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    return pd.DataFrame(data)

def test_timeseries_structure():
    """Test the time-series structure generation"""
    print("🧪 TESTING TIME-SERIES STRUCTURE GENERATION")
    print("=" * 60)
    
    # Create test data
    market_data = create_test_data()
    print(f"📊 Created test data: {len(market_data)} candles")
    print(f"🕐 Time range: {market_data['time'].iloc[0]} to {market_data['time'].iloc[-1]}")

    # Use only the last 20 time periods for analysis (but keep all data for indicator calculation)
    analysis_start_index = len(market_data) - 20
    analysis_times = market_data['time'].iloc[analysis_start_index:].dt.strftime('%H:%M').tolist()
    print(f"📊 Analysis will focus on last 20 time periods: {analysis_times[:5]}...{analysis_times[-5:]}")
    
    # Initialize analyzer
    analyzer = IntegratedTechnicalAnalyzer()
    
    # Test the _generate_universal_time_series_analysis method directly
    print(f"\n🔄 Testing _generate_universal_time_series_analysis method...")
    
    try:
        time_series_result = analyzer._generate_universal_time_series_analysis(
            market_data, 'strategy_all', None, 'period', include_history=False
        )
        
        if time_series_result:
            print(f"✅ Time-series analysis generated successfully!")
            print(f"📊 Keys in result: {list(time_series_result.keys())}")
            
            # Check time_series_data
            time_series_data = time_series_result.get('time_series_data', {})
            time_periods = time_series_result.get('time_periods', [])
            
            print(f"⏰ Time periods: {len(time_periods)}")
            print(f"🕐 Sample time periods: {time_periods[:5]}{'...' if len(time_periods) > 5 else ''}")
            
            if time_series_data:
                print(f"📈 Time-series data keys: {len(time_series_data)}")
                
                # Check first time period
                if time_periods:
                    first_time = time_periods[0]
                    first_data = time_series_data.get(first_time, {})

                    print(f"\n🔍 First time period ({first_time}) data:")
                    print(f"   Keys: {list(first_data.keys())}")

                    indicators = first_data.get('indicators', {})
                    print(f"   Indicators: {len(indicators)}")

                    if indicators:
                        sample_indicators = list(indicators.keys())[:5]
                        print(f"   Sample indicators: {sample_indicators}")

                        # Show values for first few indicators
                        for indicator in sample_indicators:
                            value = indicators[indicator]
                            print(f"      {indicator}: {value}")

                    price_data = first_data.get('price_data', {})
                    print(f"   Price data: {price_data}")

                    # Check a later time period (middle)
                    if len(time_periods) > 30:
                        middle_time = time_periods[30]
                        middle_data = time_series_data.get(middle_time, {})

                        print(f"\n🔍 Middle time period ({middle_time}) data:")
                        print(f"   Keys: {list(middle_data.keys())}")

                        middle_indicators = middle_data.get('indicators', {})
                        print(f"   Indicators: {len(middle_indicators)}")

                        if middle_indicators:
                            sample_indicators = list(middle_indicators.keys())[:5]
                            print(f"   Sample indicators: {sample_indicators}")

                            # Show values for first few indicators
                            for indicator in sample_indicators:
                                value = middle_indicators[indicator]
                                print(f"      {indicator}: {value}")

                        middle_price_data = middle_data.get('price_data', {})
                        print(f"   Price data: {middle_price_data}")
            
            # Test the Excel creation method
            print(f"\n📄 Testing Excel creation...")
            
            # Create a mock results structure
            mock_results = {
                'method': 'strategy_all',
                'indicators': {},
                'total_indicators': 0,
                'time_series_analysis': time_series_result
            }
            
            # Test the _create_universal_timeseries_sheet method
            import io
            from openpyxl import Workbook
            
            # Create a mock writer
            buffer = io.BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                timeseries_created = analyzer._create_universal_timeseries_sheet(mock_results, writer)
                
                if timeseries_created:
                    print(f"✅ Universal time-series sheet created successfully!")
                else:
                    print(f"❌ Universal time-series sheet creation failed")
                    
                    # Debug why it failed
                    print(f"\n🔍 Debugging Excel creation failure:")
                    print(f"   time_series_analysis in mock_results: {'time_series_analysis' in mock_results}")
                    
                    if 'time_series_analysis' in mock_results:
                        ts_analysis = mock_results['time_series_analysis']
                        print(f"   time_series_analysis keys: {list(ts_analysis.keys())}")
                        print(f"   time_series_data in analysis: {'time_series_data' in ts_analysis}")
                        print(f"   time_periods in analysis: {'time_periods' in ts_analysis}")
                        
                        if 'time_series_data' in ts_analysis:
                            ts_data = ts_analysis['time_series_data']
                            print(f"   time_series_data length: {len(ts_data)}")
                        
                        if 'time_periods' in ts_analysis:
                            ts_periods = ts_analysis['time_periods']
                            print(f"   time_periods length: {len(ts_periods)}")
        else:
            print(f"❌ Time-series analysis generation failed")
            
    except Exception as e:
        print(f"❌ Error in time-series structure test: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    try:
        test_timeseries_structure()
        
        print(f"\n🎯 SUMMARY:")
        print("This test helps identify why the Excel time-series format is not working.")
        print("Expected: Multiple time period columns in Excel")
        print("Current: Single 'Value' column")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
