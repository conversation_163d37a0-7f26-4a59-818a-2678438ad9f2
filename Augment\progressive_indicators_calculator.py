"""
Progressive Technical Indicators Calculator

This module fixes the issue where all indicators show the same values across time periods.
It implements progressive calculation where indicators are calculated for each specific time point.

Key Fix: Instead of calculating indicators once on full dataset and reusing values,
we calculate indicators progressively for each time point using only data available up to that point.
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
import logging
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ProgressiveIndicatorsCalculator:
    """
    Progressive calculator that computes indicators for each time point individually
    """
    
    def __init__(self):
        """Initialize the progressive calculator"""
        self.min_periods = {
            # Minimum periods required for each indicator type
            'sma': 10,
            'ema': 10, 
            'rsi': 14,
            'macd': 26,
            'bbands': 20,
            'atr': 14,
            'cci': 20,
            'stoch': 14,
            'adx': 14,
            'obv': 2,
            'mfi': 14,
            'willr': 14,
            'roc': 10,
            'mom': 10,
            'vwap': 1,
            'true_range': 1
        }
    
    def adjust_parameters_for_timeframe(self, interval_minutes: int) -> Dict:
        """
        Adjust indicator parameters based on timeframe for optimal sensitivity
        """
        logger.info(f"🔧 Adjusting parameters for {interval_minutes}-minute timeframe")
        
        if interval_minutes == 1:
            # 1-minute: Use shorter periods for higher sensitivity
            params = {
                'rsi_length': 9,
                'sma_lengths': [5, 10, 20],
                'ema_lengths': [5, 10, 20], 
                'macd_fast': 8, 'macd_slow': 17, 'macd_signal': 9,
                'bb_length': 10, 'bb_std': 2,
                'atr_length': 9,
                'cci_length': 14,
                'stoch_k': 9, 'stoch_d': 3,
                'adx_length': 9,
                'mfi_length': 9,
                'willr_length': 9,
                'roc_length': 5,
                'mom_length': 5
            }
        elif interval_minutes <= 5:
            # 3-5 minute: Balanced parameters
            params = {
                'rsi_length': 14,
                'sma_lengths': [10, 20, 50],
                'ema_lengths': [10, 20, 50],
                'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9,
                'bb_length': 20, 'bb_std': 2,
                'atr_length': 14,
                'cci_length': 20,
                'stoch_k': 14, 'stoch_d': 3,
                'adx_length': 14,
                'mfi_length': 14,
                'willr_length': 14,
                'roc_length': 10,
                'mom_length': 10
            }
        else:
            # 15+ minute: Standard parameters for smoother signals
            params = {
                'rsi_length': 14,
                'sma_lengths': [20, 50, 100],
                'ema_lengths': [20, 50, 100],
                'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9,
                'bb_length': 20, 'bb_std': 2,
                'atr_length': 14,
                'cci_length': 20,
                'stoch_k': 14, 'stoch_d': 3,
                'adx_length': 14,
                'mfi_length': 14,
                'willr_length': 14,
                'roc_length': 12,
                'mom_length': 12
            }
        
        logger.info(f"✅ Parameters adjusted: RSI={params['rsi_length']}, SMA={params['sma_lengths']}")
        return params
    
    def calculate_progressive_indicators(self, df: pd.DataFrame, 
                                       time_periods: List[str],
                                       interval_minutes: int = 1,
                                       categories: List[str] = None) -> Dict:
        """
        Calculate indicators progressively for each time period
        
        This is the KEY FIX: Instead of calculating once and reusing,
        we calculate indicators for each time point using only data up to that point.
        """
        logger.info(f"🔄 Starting progressive calculation for {len(time_periods)} time periods")
        
        # Adjust parameters based on timeframe
        params = self.adjust_parameters_for_timeframe(interval_minutes)
        
        # Prepare data
        if 'time' not in df.columns:
            df = df.reset_index()
        
        # Convert time to string format for matching
        if not pd.api.types.is_datetime64_any_dtype(df['time']):
            df['time'] = pd.to_datetime(df['time'])
        df['time_str'] = df['time'].dt.strftime('%H:%M')
        
        # Results storage
        progressive_results = {}
        
        for i, target_time in enumerate(time_periods):
            logger.debug(f"📊 Processing time {i+1}/{len(time_periods)}: {target_time}")
            
            # Find the index for this time
            matching_indices = df[df['time_str'] == target_time].index
            if len(matching_indices) == 0:
                logger.warning(f"⚠️ No data found for time {target_time}")
                continue
            
            target_index = matching_indices[0]
            
            # Get data up to this point (CRITICAL: only use data available up to this time)
            data_subset = df.iloc[:target_index + 1].copy()
            
            if len(data_subset) < 10:  # Need minimum data for calculations
                logger.warning(f"⚠️ Insufficient data for {target_time}: {len(data_subset)} candles")
                continue
            
            # Calculate indicators for this specific time point
            indicators = self._calculate_indicators_for_timepoint(
                data_subset, params, categories, target_time
            )
            
            # Store results
            progressive_results[target_time] = {
                'indicators': indicators,
                'data_points_used': len(data_subset),
                'price_data': {
                    'Open': data_subset['Open'].iloc[-1],
                    'High': data_subset['High'].iloc[-1], 
                    'Low': data_subset['Low'].iloc[-1],
                    'Close': data_subset['Close'].iloc[-1],
                    'Volume': data_subset['Volume'].iloc[-1] if 'Volume' in data_subset.columns else 0
                }
            }
        
        logger.info(f"✅ Progressive calculation completed for {len(progressive_results)} time periods")
        return progressive_results
    
    def _calculate_indicators_for_timepoint(self, data_subset: pd.DataFrame, 
                                          params: Dict, categories: List[str],
                                          target_time: str) -> Dict:
        """
        Calculate indicators for a specific timepoint using only available data
        """
        indicators = {}
        
        try:
            # Extract OHLCV data
            close = data_subset['Close']
            high = data_subset['High']
            low = data_subset['Low']
            open_prices = data_subset['Open']
            volume = data_subset['Volume'] if 'Volume' in data_subset.columns else pd.Series([1000] * len(data_subset))
            
            # Calculate indicators based on categories
            if not categories or 'momentum' in categories:
                # RSI
                if len(close) >= params['rsi_length']:
                    rsi_result = ta.rsi(close, length=params['rsi_length'])
                    if rsi_result is not None and len(rsi_result) > 0:
                        indicators[f"RSI_{params['rsi_length']}"] = float(rsi_result.iloc[-1]) if not pd.isna(rsi_result.iloc[-1]) else None
                
                # MACD
                if len(close) >= params['macd_slow']:
                    macd_result = ta.macd(close, fast=params['macd_fast'], slow=params['macd_slow'], signal=params['macd_signal'])
                    if macd_result is not None and len(macd_result) > 0:
                        for col in macd_result.columns:
                            if not pd.isna(macd_result[col].iloc[-1]):
                                indicators[col] = float(macd_result[col].iloc[-1])
                
                # ROC (Rate of Change)
                if len(close) >= params['roc_length']:
                    roc_result = ta.roc(close, length=params['roc_length'])
                    if roc_result is not None and len(roc_result) > 0:
                        indicators[f"ROC_{params['roc_length']}"] = float(roc_result.iloc[-1]) if not pd.isna(roc_result.iloc[-1]) else None
                
                # Momentum
                if len(close) >= params['mom_length']:
                    mom_result = ta.mom(close, length=params['mom_length'])
                    if mom_result is not None and len(mom_result) > 0:
                        indicators[f"MOM_{params['mom_length']}"] = float(mom_result.iloc[-1]) if not pd.isna(mom_result.iloc[-1]) else None
            
            if not categories or 'overlap' in categories:
                # Moving Averages
                for length in params['sma_lengths']:
                    if len(close) >= length:
                        sma_result = ta.sma(close, length=length)
                        if sma_result is not None and len(sma_result) > 0:
                            indicators[f"SMA_{length}"] = float(sma_result.iloc[-1]) if not pd.isna(sma_result.iloc[-1]) else None
                
                for length in params['ema_lengths']:
                    if len(close) >= length:
                        ema_result = ta.ema(close, length=length)
                        if ema_result is not None and len(ema_result) > 0:
                            indicators[f"EMA_{length}"] = float(ema_result.iloc[-1]) if not pd.isna(ema_result.iloc[-1]) else None
                
                # Bollinger Bands
                if len(close) >= params['bb_length']:
                    bb_result = ta.bbands(close, length=params['bb_length'], std=params['bb_std'])
                    if bb_result is not None and len(bb_result) > 0:
                        for col in bb_result.columns:
                            if not pd.isna(bb_result[col].iloc[-1]):
                                indicators[col] = float(bb_result[col].iloc[-1])
            
            if not categories or 'volatility' in categories:
                # ATR
                if len(close) >= params['atr_length']:
                    atr_result = ta.atr(high, low, close, length=params['atr_length'])
                    if atr_result is not None and len(atr_result) > 0:
                        indicators[f"ATR_{params['atr_length']}"] = float(atr_result.iloc[-1]) if not pd.isna(atr_result.iloc[-1]) else None
                
                # True Range
                tr_result = ta.true_range(high, low, close)
                if tr_result is not None and len(tr_result) > 0:
                    indicators["TRUE_RANGE"] = float(tr_result.iloc[-1]) if not pd.isna(tr_result.iloc[-1]) else None
            
            if not categories or 'volume' in categories:
                # OBV
                if len(close) >= 2:
                    obv_result = ta.obv(close, volume)
                    if obv_result is not None and len(obv_result) > 0:
                        indicators["OBV"] = float(obv_result.iloc[-1]) if not pd.isna(obv_result.iloc[-1]) else None
                
                # VWAP
                vwap_result = ta.vwap(high, low, close, volume)
                if vwap_result is not None and len(vwap_result) > 0:
                    indicators["VWAP"] = float(vwap_result.iloc[-1]) if not pd.isna(vwap_result.iloc[-1]) else None
            
            # Add price-based indicators
            indicators["CURRENT_PRICE"] = float(close.iloc[-1])
            indicators["PRICE_CHANGE"] = float(close.iloc[-1] - close.iloc[0]) if len(close) > 1 else 0
            indicators["PRICE_CHANGE_PCT"] = float((close.iloc[-1] - close.iloc[0]) / close.iloc[0] * 100) if len(close) > 1 else 0
            
            # Remove None values
            indicators = {k: v for k, v in indicators.items() if v is not None}
            
        except Exception as e:
            logger.error(f"❌ Error calculating indicators for {target_time}: {str(e)}")
            indicators = {"ERROR": str(e)}
        
        return indicators

def test_progressive_calculation():
    """Test the progressive calculation to verify it produces different values"""
    print("🧪 Testing Progressive Indicators Calculator")
    print("=" * 60)
    
    # Create sample data with clear trends
    dates = pd.date_range('2025-06-24 09:15', periods=100, freq='1min')
    
    # Create trending price data
    base_price = 1200
    trend = np.linspace(0, 50, 100)  # Upward trend
    noise = np.random.normal(0, 2, 100)
    
    close_prices = base_price + trend + noise
    high_prices = close_prices + np.random.uniform(1, 5, 100)
    low_prices = close_prices - np.random.uniform(1, 5, 100)
    open_prices = np.roll(close_prices, 1)
    open_prices[0] = base_price
    
    df = pd.DataFrame({
        'time': dates,
        'Open': open_prices,
        'High': high_prices,
        'Low': low_prices,
        'Close': close_prices,
        'Volume': np.random.randint(1000, 5000, 100)
    })
    
    # Test progressive calculation
    calculator = ProgressiveIndicatorsCalculator()
    
    # Test with specific time periods
    test_times = ['09:25', '09:35', '09:45', '09:55', '10:05']
    
    results = calculator.calculate_progressive_indicators(
        df, test_times, interval_minutes=1, categories=['momentum', 'overlap']
    )
    
    # Display results
    print(f"\n📊 Progressive Calculation Results:")
    print("-" * 60)
    
    for time_str, data in results.items():
        indicators = data['indicators']
        print(f"\n🕐 Time: {time_str} (using {data['data_points_used']} candles)")
        print(f"   💰 Price: {indicators.get('CURRENT_PRICE', 'N/A'):.2f}")
        print(f"   📊 RSI: {indicators.get('RSI_9', 'N/A')}")
        print(f"   📈 SMA_10: {indicators.get('SMA_10', 'N/A')}")
        print(f"   📉 EMA_10: {indicators.get('EMA_10', 'N/A')}")
        print(f"   🎯 MACD: {indicators.get('MACD_8_17_9', 'N/A')}")
    
    # Verify values are different
    rsi_values = [results[t]['indicators'].get('RSI_9') for t in test_times if 'RSI_9' in results[t]['indicators']]
    sma_values = [results[t]['indicators'].get('SMA_10') for t in test_times if 'SMA_10' in results[t]['indicators']]
    
    print(f"\n🔍 Verification:")
    print(f"   RSI values: {[f'{v:.2f}' if v else 'N/A' for v in rsi_values]}")
    print(f"   SMA values: {[f'{v:.2f}' if v else 'N/A' for v in sma_values]}")
    
    # Check if values are different
    unique_rsi = len(set([v for v in rsi_values if v is not None]))
    unique_sma = len(set([v for v in sma_values if v is not None]))
    
    if unique_rsi > 1 and unique_sma > 1:
        print(f"✅ SUCCESS: Values are different across time periods!")
        print(f"   RSI has {unique_rsi} unique values")
        print(f"   SMA has {unique_sma} unique values")
    else:
        print(f"❌ ISSUE: Values are still the same across time periods")
    
    return results

if __name__ == "__main__":
    test_progressive_calculation()
