"""
Detailed Technical Indicators Help Generator

This script generates comprehensive help documentation for all pandas-ta indicators
using the help() function to get exact parameter details and default values.

Usage:
    python detailed_indicators_help_generator.py
"""

import pandas_ta as ta
import pandas as pd
import sys
import os
from datetime import datetime
import io
from contextlib import redirect_stdout

def get_function_help(func_name):
    """Get detailed help for a specific function"""
    try:
        # Get the function
        if hasattr(ta, func_name):
            func = getattr(ta, func_name)
            
            # Capture help output
            f = io.StringIO()
            with redirect_stdout(f):
                help(func)
            help_text = f.getvalue()
            
            return help_text
        else:
            return f"Function {func_name} not found in pandas-ta"
    except Exception as e:
        return f"Error getting help for {func_name}: {str(e)}"

def generate_detailed_help_documentation():
    """Generate detailed help documentation for all pandas-ta indicators"""
    print("🔍 Generating Detailed Pandas-TA Indicators Help Documentation...")
    
    # All indicators from your list
    all_indicators = [
        'aberration', 'above', 'above_value', 'accbands', 'ad', 'adosc', 'adx', 'alma', 'amat', 'ao', 'aobv', 'apo', 'aroon', 'atr', 'bbands', 'below', 'below_value', 'bias', 'bop', 'brar', 'cci', 'cdl_pattern', 'cdl_z', 'cfo', 'cg', 'chop', 'cksp', 'cmf', 'cmo', 'coppock', 'cross', 'cross_value', 'cti', 'decay', 'decreasing', 'dema', 'dm', 'donchian', 'dpo', 'ebsw', 'efi', 'ema', 'entropy', 'eom', 'er', 'eri', 'fisher', 'fwma', 'ha', 'hilo', 'hl2', 'hlc3', 'hma', 'hwc', 'hwma', 'ichimoku', 'increasing', 'inertia', 'jma', 'kama', 'kc', 'kdj', 'kst', 'kurtosis', 'kvo', 'linreg', 'log_return', 'long_run', 'macd', 'mad', 'massi', 'mcgd', 'median', 'mfi', 'midpoint', 'midprice', 'mom', 'natr', 'nvi', 'obv', 'ohlc4', 'pdist', 'percent_return', 'pgo', 'ppo', 'psar', 'psl', 'pvi', 'pvo', 'pvol', 'pvr', 'pvt', 'pwma', 'qqe', 'qstick', 'quantile', 'rma', 'roc', 'rsi', 'rsx', 'rvgi', 'rvi', 'short_run', 'sinwma', 'skew', 'slope', 'sma', 'smi', 'squeeze', 'squeeze_pro', 'ssf', 'stc', 'stdev', 'stoch', 'stochrsi', 'supertrend', 'swma', 't3', 'td_seq', 'tema', 'thermo', 'tos_stdevall', 'trima', 'trix', 'true_range', 'tsi', 'tsignals', 'ttm_trend', 'ui', 'uo', 'variance', 'vhf', 'vidya', 'vortex', 'vp', 'vwap', 'vwma', 'wcp', 'willr', 'wma', 'xsignals', 'zlma', 'zscore'
    ]
    
    # Candle patterns
    candle_patterns = [
        '2crows', '3blackcrows', '3inside', '3linestrike', '3outside', '3starsinsouth', '3whitesoldiers', 'abandonedbaby', 'advanceblock', 'belthold', 'breakaway', 'closingmarubozu', 'concealbabyswall', 'counterattack', 'darkcloudcover', 'doji', 'dojistar', 'dragonflydoji', 'engulfing', 'eveningdojistar', 'eveningstar', 'gapsidesidewhite', 'gravestonedoji', 'hammer', 'hangingman', 'harami', 'haramicross', 'highwave', 'hikkake', 'hikkakemod', 'homingpigeon', 'identical3crows', 'inneck', 'inside', 'invertedhammer', 'kicking', 'kickingbylength', 'ladderbottom', 'longleggeddoji', 'longline', 'marubozu', 'matchinglow', 'mathold', 'morningdojistar', 'morningstar', 'onneck', 'piercing', 'rickshawman', 'risefall3methods', 'separatinglines', 'shootingstar', 'shortline', 'spinningtop', 'stalledpattern', 'sticksandwich', 'takuri', 'tasukigap', 'thrusting', 'tristar', 'unique3river', 'upsidegap2crows', 'xsidegap3methods'
    ]
    
    # Create output file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"detailed_pandas_ta_help_{timestamp}.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("DETAILED PANDAS-TA INDICATORS HELP DOCUMENTATION\n")
        f.write("=" * 100 + "\n")
        f.write(f"Generated on: {datetime.now().isoformat()}\n")
        f.write(f"Pandas-TA Version: {ta.version}\n")
        f.write("=" * 100 + "\n\n")
        
        # Process regular indicators
        f.write("SECTION 1: TECHNICAL INDICATORS DETAILED HELP\n")
        f.write("=" * 60 + "\n\n")
        
        for i, indicator in enumerate(all_indicators, 1):
            print(f"Processing {i}/{len(all_indicators)}: {indicator}")
            
            f.write(f"{i}. {indicator.upper()}\n")
            f.write("=" * 50 + "\n")
            
            help_text = get_function_help(indicator)
            f.write(help_text)
            f.write("\n" + "=" * 50 + "\n\n")
        
        # Process candle patterns
        f.write("\n\nSECTION 2: CANDLE PATTERNS HELP\n")
        f.write("=" * 60 + "\n\n")
        
        # For candle patterns, we need to check cdl_pattern function
        f.write("CANDLE PATTERNS (accessed via cdl_pattern function)\n")
        f.write("-" * 50 + "\n")
        
        help_text = get_function_help('cdl_pattern')
        f.write(help_text)
        f.write("\n" + "-" * 50 + "\n\n")
        
        f.write("Available Candle Patterns:\n")
        for i, pattern in enumerate(candle_patterns, 1):
            f.write(f"{i:3d}. {pattern}\n")
        
        # Add summary of key insights
        f.write("\n\nSECTION 3: KEY INSIGHTS FOR TIMEFRAME ANALYSIS\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("""
CRITICAL FINDINGS FOR FIXING SAME VALUES ISSUE:

1. PARAMETER SENSITIVITY:
   - Most indicators have 'length' or 'period' parameters
   - Default values are often too large for minute-level analysis
   - Need to adjust parameters based on timeframe

2. COMMON DEFAULT VALUES:
   - RSI: length=14 (good for all timeframes)
   - MACD: fast=12, slow=26, signal=9 (may need adjustment)
   - SMA/EMA: length=10,20,50 (adjust based on timeframe)
   - Bollinger Bands: length=20, std=2 (may need shorter for 1min)
   - ATR: length=14 (good for all timeframes)

3. TIMEFRAME ADJUSTMENTS NEEDED:
   - 1-minute: Use shorter periods (5-10 instead of 20)
   - 3-minute: Use standard periods but monitor sensitivity
   - 5-minute: Standard periods work well
   - 15-minute+: Can use longer periods for smoother signals

4. DATA REQUIREMENTS:
   - Ensure sufficient historical data for calculation
   - Some indicators need 2x their period for stable values
   - Check for NaN values at the beginning of series

5. CALCULATION METHOD:
   - Use progressive calculation (calculate for each time point)
   - Don't use the same final value for all time periods
   - Ensure indicators are calculated on the correct subset of data

RECOMMENDED FIXES:
1. Implement progressive indicator calculation
2. Adjust parameters based on timeframe
3. Ensure proper data windowing for each time point
4. Add validation for sufficient data points
5. Handle NaN values appropriately
""")
    
    print(f"✅ Detailed help documentation generated: {output_file}")
    return output_file

if __name__ == "__main__":
    try:
        output_file = generate_detailed_help_documentation()
        print(f"\n📖 Detailed help documentation saved to: {output_file}")
        print("\n💡 This file contains:")
        print("   - Complete help() output for each indicator")
        print("   - Function signatures and parameter details")
        print("   - Default values for all parameters")
        print("   - Usage examples and requirements")
        print("   - Key insights for fixing the same values issue")
        
    except Exception as e:
        print(f"❌ Error generating detailed help documentation: {str(e)}")
