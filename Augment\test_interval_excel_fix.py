"""
Test Interval Excel Fix

Test script to verify that the Excel output shows the correct interval in time series.
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data_with_interval(interval_minutes=1, start_time="09:15", end_time="15:30"):
    """Create test data with specific interval"""
    
    start_dt = datetime.strptime(f"2025-06-30 {start_time}", "%Y-%m-%d %H:%M")
    end_dt = datetime.strptime(f"2025-06-30 {end_time}", "%Y-%m-%d %H:%M")
    
    times = []
    current = start_dt
    while current <= end_dt:
        times.append(current)
        current += timedelta(minutes=interval_minutes)
    
    # Create realistic price data
    import random
    random.seed(42)
    
    base_price = 100.0
    data = []
    
    for i, time in enumerate(times):
        trend = 0.01 * i
        noise = random.uniform(-0.5, 0.5)
        
        open_price = base_price + trend + noise
        high_price = open_price + random.uniform(0.1, 0.8)
        low_price = open_price - random.uniform(0.1, 0.6)
        close_price = open_price + random.uniform(-0.3, 0.4)
        volume = random.randint(1000, 5000)
        
        data.append({
            'time': time,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
        
        base_price = close_price
    
    df = pd.DataFrame(data)
    df.set_index('time', inplace=True)
    df.reset_index(inplace=True)
    
    return df

def test_interval_in_excel_output(interval_minutes=1):
    """Test that Excel output shows correct interval"""
    
    logger.info(f"Testing {interval_minutes}-minute interval in Excel output...")
    
    try:
        from integrated_technical_analyzer import IntegratedTechnicalAnalyzer
        
        analyzer = IntegratedTechnicalAnalyzer()
        
        # Create test data with specific interval
        test_data = create_test_data_with_interval(interval_minutes)
        logger.info(f"Created test data: {len(test_data)} candles with {interval_minutes}-minute interval")
        
        # Show first few timestamps to verify interval
        logger.info("First 5 timestamps:")
        for i in range(min(5, len(test_data))):
            logger.info(f"  {i+1}. {test_data.iloc[i]['time']}")
        
        # Calculate time differences to verify interval
        if len(test_data) > 1:
            time_diffs = []
            for i in range(1, min(6, len(test_data))):
                diff = (test_data.iloc[i]['time'] - test_data.iloc[i-1]['time']).total_seconds() / 60
                time_diffs.append(diff)
            logger.info(f"Time differences (minutes): {time_diffs}")
            avg_diff = sum(time_diffs) / len(time_diffs)
            logger.info(f"Average interval: {avg_diff:.1f} minutes")
        
        # Test the analyzer with this data
        analyzer._current_interval = str(interval_minutes)  # Set the interval explicitly
        
        # Test time-series analysis
        result = analyzer._generate_universal_time_series_analysis(
            market_data=test_data,
            method="extension",
            categories=["volatility"],
            mode="signals",
            candle_times=None,
            include_history=False
        )
        
        if result and 'time_periods' in result:
            time_periods = result['time_periods']
            logger.info(f"Generated {len(time_periods)} time periods")
            logger.info(f"First 5 time periods: {time_periods[:5]}")
            
            # Check if time periods match the expected interval
            if len(time_periods) > 1:
                # Convert time periods back to datetime for comparison
                period_times = []
                for tp in time_periods[:5]:
                    try:
                        dt = datetime.strptime(f"2025-06-30 {tp}", "%Y-%m-%d %H:%M")
                        period_times.append(dt)
                    except:
                        continue
                
                if len(period_times) > 1:
                    period_diffs = []
                    for i in range(1, len(period_times)):
                        diff = (period_times[i] - period_times[i-1]).total_seconds() / 60
                        period_diffs.append(diff)
                    
                    logger.info(f"Time period differences (minutes): {period_diffs}")
                    avg_period_diff = sum(period_diffs) / len(period_diffs)
                    logger.info(f"Average time period interval: {avg_period_diff:.1f} minutes")
                    
                    # Check if it matches expected interval
                    if abs(avg_period_diff - interval_minutes) < 0.1:
                        logger.info(f"✅ Time periods match expected {interval_minutes}-minute interval")
                        return True
                    else:
                        logger.warning(f"❌ Time periods don't match expected interval. Expected: {interval_minutes}, Got: {avg_period_diff:.1f}")
                        return False
        
        logger.warning("❌ Could not verify time periods")
        return False
        
    except Exception as e:
        logger.error(f"❌ Error testing interval: {e}")
        return False

def test_multiple_intervals():
    """Test multiple intervals to ensure fix works for all"""
    
    test_intervals = [1, 5, 15, 30]
    results = {}
    
    for interval in test_intervals:
        logger.info(f"\n{'='*50}")
        logger.info(f"TESTING {interval}-MINUTE INTERVAL")
        logger.info(f"{'='*50}")
        
        success = test_interval_in_excel_output(interval)
        results[interval] = success
        
        if success:
            logger.info(f"✅ {interval}-minute interval test PASSED")
        else:
            logger.error(f"❌ {interval}-minute interval test FAILED")
    
    return results

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Interval Excel Fix Test...")
    
    # Test multiple intervals
    results = test_multiple_intervals()
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    
    for interval, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {interval}-minute interval: {status}")
    
    if passed == total:
        logger.info(f"🎉 All interval tests passed! Excel output should now show correct intervals.")
    else:
        logger.info(f"❌ Some tests failed. The interval fix may need additional work.")
    
    logger.info("Test completed!")

if __name__ == "__main__":
    main()
