"""
Test script for Active Stocks Finder

This script tests the active stocks finder with known good stocks to validate the logic.
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from active_stocks_finder import ActiveStocksFinder, KNOWN_GOOD_STOCKS, A_TO_G_STOCKS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_known_good_stocks():
    """Test the finder with known good stocks"""
    
    # Use a recent date for testing
    test_date = "30-06-2025"  # You can change this to any date
    
    logger.info("Testing Active Stocks Finder with known good stocks...")
    logger.info(f"Test date: {test_date}")
    logger.info(f"Known good stocks: {KNOWN_GOOD_STOCKS}")
    
    # Initialize finder
    finder = ActiveStocksFinder(date=test_date)
    
    # Test with first 3 known good stocks for quick validation
    test_stocks = KNOWN_GOOD_STOCKS[:3]
    logger.info(f"Testing with: {test_stocks}")
    
    # Analyze stocks
    results = finder.analyze_multiple_stocks(test_stocks)
    
    # Display results
    logger.info("\n" + "="*60)
    logger.info("TEST RESULTS")
    logger.info("="*60)
    
    for ticker in test_stocks:
        result = results.get(ticker, {})
        
        if 'error' in result:
            logger.info(f"{ticker}: ERROR - {result['error']}")
            continue
        
        movement_score = result.get('movement_score', {})
        score = movement_score.get('movement_score', 0)
        grade = movement_score.get('grade', 'N/A')
        
        candle_analysis = result.get('candle_analysis', {})
        height_ratio = candle_analysis.get('height_to_price_ratio', 0)
        movement_freq = candle_analysis.get('movement_frequency', 0)
        
        logger.info(f"{ticker}:")
        logger.info(f"  Score: {score:.1f} ({grade})")
        logger.info(f"  Height Ratio: {height_ratio:.2f}%")
        logger.info(f"  Movement Frequency: {movement_freq:.2f}")
        logger.info(f"  Candles: {candle_analysis.get('total_candles', 0)}")
        
        # Show score breakdown
        score_breakdown = movement_score.get('score_breakdown', {})
        logger.info(f"  Score Breakdown: {score_breakdown}")
        logger.info("")
    
    # Export test results
    excel_file = finder.export_to_excel(results, f"test_results_{test_date.replace('-', '_')}.xlsx")
    if excel_file:
        logger.info(f"Test results exported to: {excel_file}")
    
    return results

def test_a_to_g_stocks():
    """Test with A-G stocks to validate logic"""
    
    test_date = "30-06-2025"
    
    logger.info("Testing with A-G stocks for validation...")
    logger.info(f"A-G stocks count: {len(A_TO_G_STOCKS)}")
    
    # Initialize finder
    finder = ActiveStocksFinder(date=test_date)
    
    # Analyze A-G stocks
    results = finder.analyze_multiple_stocks(A_TO_G_STOCKS)
    
    # Filter good stocks
    good_stocks = finder.filter_good_stocks(results, min_score=60.0)
    
    # Check validation
    known_good_found = [s['ticker'] for s in good_stocks if s['ticker'] in KNOWN_GOOD_STOCKS]
    
    logger.info("\n" + "="*60)
    logger.info("A-G STOCKS VALIDATION")
    logger.info("="*60)
    logger.info(f"Total A-G stocks analyzed: {len(A_TO_G_STOCKS)}")
    logger.info(f"Good stocks found (score >= 60): {len(good_stocks)}")
    logger.info(f"Known good stocks in results: {len(known_good_found)}/{len(KNOWN_GOOD_STOCKS)}")
    logger.info(f"Known good stocks found: {known_good_found}")
    
    if good_stocks:
        logger.info(f"\nTop 10 A-G stocks by score:")
        for i, stock in enumerate(good_stocks[:10], 1):
            ticker = stock['ticker']
            score = stock['movement_score']['movement_score']
            grade = stock['movement_score']['grade']
            is_known = ticker in KNOWN_GOOD_STOCKS
            logger.info(f"{i:2d}. {ticker:12s} - Score: {score:5.1f} ({grade}) {'✓ Known Good' if is_known else ''}")
    
    # Export validation results
    excel_file = finder.export_to_excel(results, f"a_to_g_validation_{test_date.replace('-', '_')}.xlsx")
    if excel_file:
        logger.info(f"Validation results exported to: {excel_file}")
    
    return results, good_stocks

def main():
    """Main test function"""
    logger.info("Starting Active Stocks Finder Tests...")
    
    try:
        # Test 1: Known good stocks
        logger.info("\n" + "="*80)
        logger.info("TEST 1: KNOWN GOOD STOCKS")
        logger.info("="*80)
        test_results = test_known_good_stocks()
        
        # Test 2: A-G stocks validation
        logger.info("\n" + "="*80)
        logger.info("TEST 2: A-G STOCKS VALIDATION")
        logger.info("="*80)
        validation_results, good_stocks = test_a_to_g_stocks()
        
        # Summary
        logger.info("\n" + "="*80)
        logger.info("TEST SUMMARY")
        logger.info("="*80)
        
        # Calculate success rate
        known_good_in_results = [s['ticker'] for s in good_stocks if s['ticker'] in KNOWN_GOOD_STOCKS]
        success_rate = len(known_good_in_results) / len(KNOWN_GOOD_STOCKS) * 100
        
        logger.info(f"Validation Success Rate: {success_rate:.1f}%")
        logger.info(f"Known good stocks correctly identified: {len(known_good_in_results)}/{len(KNOWN_GOOD_STOCKS)}")
        
        if success_rate >= 70:
            logger.info("✅ VALIDATION PASSED - Logic correctly identifies good stocks")
        else:
            logger.info("❌ VALIDATION FAILED - Logic needs adjustment")
        
        logger.info("Tests completed!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        raise

if __name__ == "__main__":
    main()
