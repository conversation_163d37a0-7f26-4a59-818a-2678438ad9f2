"""
Enhanced Multi-Interval Professional Analyzer
- Generates separate files for each interval using integrated_technical_analyzer
- Asks for user inputs (ticker, exchange, date, time, intervals)
- Runs separate commands for each interval
- Renames files immediately after each command
- Uses proper interval-specific data for advanced professional analysis
"""

import subprocess
import os
import time
import glob
import shutil
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any

class EnhancedMultiIntervalProfessionalAnalyzer:
    def __init__(self):
        print("🚀 Enhanced Multi-Interval Professional Analyzer initialized")
        print("📊 Separate file generation for each interval")
        print("⏰ Individual command execution for each timeframe")
        print("🎯 Professional signal analysis with proper interval data")
        
        # Available intervals
        self.available_intervals = ["1", "3", "5", "10", "15", "30", "60", "120", "240"]
        
    def get_user_inputs(self) -> Dict[str, Any]:
        """Get comprehensive user inputs"""
        
        print(f"\n📝 ENTER ANALYSIS PARAMETERS")
        print("=" * 60)
        
        # Asset details
        ticker = input("📊 Enter ticker (e.g., NATURALGAS26AUG25): ").strip().upper()
        
        print(f"\n🏢 Available exchanges: NSE, BSE, MCX, NFO")
        exchange = input("🏢 Enter exchange: ").strip().upper()
        
        date = input("📅 Enter date (DD-MM-YYYY): ").strip()
        
        # Time range
        print(f"\n⏰ TIME RANGE SELECTION")
        print("=" * 30)
        start_time = input("🕐 Enter start time (HH:MM, e.g., 10:00): ").strip()
        end_time = input("🕕 Enter end time (HH:MM, e.g., 22:32): ").strip()
        
        # Intervals selection
        print(f"\n📈 INTERVAL SELECTION")
        print("=" * 30)
        print(f"Available intervals: {', '.join(self.available_intervals)} minutes")
        intervals_input = input("Enter intervals (comma-separated, e.g., 1,5,15,30): ").strip()
        intervals = [interval.strip() for interval in intervals_input.split(',')]
        
        # Validate intervals
        valid_intervals = []
        for interval in intervals:
            if interval in self.available_intervals:
                valid_intervals.append(interval)
            else:
                print(f"⚠️  Invalid interval '{interval}' - skipping")
        
        if not valid_intervals:
            print("❌ No valid intervals provided")
            return {}
        
        # Analysis options
        print(f"\n🔧 ANALYSIS OPTIONS")
        print("=" * 30)
        run_advanced_analysis = input("🎯 Run advanced professional signal analysis? (y/n): ").strip().lower() == 'y'
        
        return {
            'ticker': ticker,
            'exchange': exchange,
            'date': date,
            'start_time': start_time,
            'end_time': end_time,
            'intervals': valid_intervals,
            'run_advanced_analysis': run_advanced_analysis
        }
    
    def generate_interval_data(self, inputs: Dict[str, Any]) -> Dict[str, str]:
        """Generate separate data files for each interval"""
        
        print(f"\n🔄 GENERATING SEPARATE FILES FOR EACH INTERVAL")
        print("=" * 80)
        print(f"📊 Ticker: {inputs['ticker']}")
        print(f"🏢 Exchange: {inputs['exchange']}")
        print(f"📅 Date: {inputs['date']}")
        print(f"⏰ Time: {inputs['start_time']} - {inputs['end_time']}")
        print(f"📈 Intervals: {', '.join(inputs['intervals'])} minutes")
        
        interval_files = {}
        
        for i, interval in enumerate(inputs['intervals'], 1):
            print(f"\n🔄 STEP {i}/{len(inputs['intervals'])}: Generating {interval}-minute data")
            print("=" * 60)
            
            # Build command for this interval
            cmd = [
                'python', 'integrated_technical_analyzer.py',
                '--mode', 'analysis',
                '--analysis-type', 'signals',
                '--ticker', inputs['ticker'],
                '--exchange', inputs['exchange'],
                '--date', inputs['date'],
                '--method', 'strategy_all',
                '--interval', interval,
                '--start-time', inputs['start_time'],
                '--end-time', inputs['end_time']
            ]
            
            print(f"📊 Interval: {interval} minutes")
            print(f"🔧 Command: {' '.join(cmd)}")
            
            try:
                # Run the command
                print(f"⏳ Executing command for {interval}-minute interval...")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0:
                    print(f"✅ {interval}-minute data generation completed!")
                    
                    # Find the generated file
                    pattern = f"technical_analysis_{inputs['ticker']}_{inputs['exchange']}_signals_*.xlsx"
                    files = glob.glob(pattern)
                    
                    if files:
                        # Get the most recent file
                        latest_file = max(files, key=os.path.getctime)
                        
                        # Create new filename with interval
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        new_filename = f"technical_analysis_{inputs['ticker']}_{inputs['exchange']}_signals_{interval}min_{timestamp}.xlsx"
                        
                        # Rename the file
                        shutil.move(latest_file, new_filename)
                        interval_files[f"{interval}min"] = new_filename
                        
                        print(f"📄 File renamed to: {new_filename}")
                        
                        # Show file info
                        file_size = os.path.getsize(new_filename) / (1024 * 1024)  # MB
                        print(f"📊 File size: {file_size:.2f} MB")
                        
                    else:
                        print(f"❌ No output file found for {interval}-minute interval")
                        
                else:
                    print(f"❌ {interval}-minute data generation failed!")
                    print(f"Error: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"❌ {interval}-minute data generation timed out after 10 minutes")
            except Exception as e:
                print(f"❌ Error generating {interval}-minute data: {str(e)}")
            
            # Wait between commands to avoid conflicts
            if i < len(inputs['intervals']):
                print(f"⏳ Waiting 2 seconds before next interval...")
                time.sleep(2)
        
        print(f"\n📊 INTERVAL DATA GENERATION SUMMARY")
        print("=" * 60)
        print(f"✅ Successfully generated: {len(interval_files)} files")
        for interval, filename in interval_files.items():
            print(f"   📄 {interval}: {filename}")
        
        if len(interval_files) != len(inputs['intervals']):
            failed_intervals = set(f"{i}min" for i in inputs['intervals']) - set(interval_files.keys())
            print(f"❌ Failed intervals: {', '.join(failed_intervals)}")
        
        return interval_files
    
    def run_advanced_professional_analysis(self, inputs: Dict[str, Any],
                                         interval_files: Dict[str, str]) -> bool:
        """Run advanced ML-enhanced professional signal analysis with proper interval files"""

        print(f"\n🎯 ADVANCED ML-ENHANCED PROFESSIONAL SIGNAL ANALYSIS")
        print("=" * 80)
        print("⏰ Real time format (not Period_1, Period_2)")
        print("🎯 Proper stop loss and targets for PGO")
        print("🤖 ML-based threshold optimization for near 1.0 accuracy")
        print("🔄 Separate thresholds for reversals vs breakouts")
        print("📊 Advanced overbought/oversold detection")

        try:
            from professional_reversal_breakout_detector import ProfessionalReversalBreakoutDetector

            detector = ProfessionalReversalBreakoutDetector()

            # Load data from separate interval files
            timeframe_data = self._load_interval_specific_data(interval_files)

            if len(timeframe_data) < 1:
                print("❌ Need at least 1 interval for analysis")
                return False

            # Define indicators to analyze
            indicators = ['PGO_14', 'CCI_14', 'SMI_5_20_5_SMIo_5_20_5_100.0', 'BIAS_26', 'CG_10']

            # Filter available indicators
            available_indicators = self._filter_available_indicators(indicators, timeframe_data)

            if not available_indicators:
                print("❌ No configured indicators found")
                return False

            print(f"\n📊 Analyzing {len(available_indicators)} professional indicators:")
            for indicator in available_indicators:
                print(f"   ✅ {indicator}")

            # Perform 1min signal detection with higher timeframe confirmation
            print(f"\n🎯 PERFORMING 1-MINUTE SIGNAL DETECTION WITH HIGHER TIMEFRAME CONFIRMATION...")
            signal_results = detector.detect_1min_signals_with_higher_timeframe_confirmation(timeframe_data, available_indicators)

            # Export to Excel with proper separate sheets
            excel_filename = self._export_professional_signals_to_excel(signal_results, inputs)

            # Print professional summary
            self._print_professional_signals_summary(signal_results)

            print(f"\n💾 Professional analysis results saved to: {excel_filename}")

            return True

        except Exception as e:
            print(f"❌ Error running ML-enhanced professional analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_interval_specific_data(self, interval_files: Dict[str, str]) -> Dict[str, Any]:
        """Load data from interval-specific files"""
        
        print(f"\n📂 LOADING INTERVAL-SPECIFIC DATA")
        print("=" * 60)
        
        timeframe_data = {}
        
        for interval, filename in interval_files.items():
            try:
                import pandas as pd
                
                # Load the Excel file
                excel_file = pd.ExcelFile(filename)
                
                # Look for time-series data sheet
                time_series_sheet = None
                for sheet_name in excel_file.sheet_names:
                    if 'time' in sheet_name.lower() or 'series' in sheet_name.lower():
                        time_series_sheet = sheet_name
                        break
                
                if not time_series_sheet:
                    time_series_sheet = excel_file.sheet_names[0]
                
                # Load the data
                df = pd.read_excel(filename, sheet_name=time_series_sheet)
                
                # Transpose data from indicator rows to indicator columns
                if 'Indicator' in df.columns:
                    # Get time columns
                    time_columns = [col for col in df.columns if col not in ['Indicator', 'Category']]
                    
                    # Create indicator data dictionary
                    indicator_data = {}
                    for idx, row in df.iterrows():
                        indicator_name = row['Indicator']
                        if indicator_name and indicator_name != '--- TECHNICAL INDICATORS ---':
                            values = []
                            for time_col in time_columns:
                                val = row[time_col]
                                if pd.isna(val):
                                    values.append(float('nan'))
                                else:
                                    try:
                                        values.append(float(val))
                                    except:
                                        values.append(float('nan'))
                            indicator_data[indicator_name] = values
                    
                    # Create DataFrame with proper structure
                    transposed_df = pd.DataFrame(indicator_data, index=time_columns)
                    timeframe_data[interval] = transposed_df
                    
                    print(f"✅ {interval}: {transposed_df.shape} (from {filename})")
                else:
                    timeframe_data[interval] = df
                    print(f"✅ {interval}: {df.shape} (from {filename})")
                
            except Exception as e:
                print(f"❌ Error loading {interval} data from {filename}: {str(e)}")
        
        return timeframe_data

    def _filter_available_indicators(self, indicators: List[str], timeframe_data: Dict[str, Any]) -> List[str]:
        """Filter indicators that are available in the data"""
        available_indicators = []

        # Check first timeframe for available indicators
        first_timeframe = list(timeframe_data.values())[0]

        for indicator in indicators:
            if indicator in first_timeframe.columns:
                available_indicators.append(indicator)
            else:
                print(f"   ⚠️  {indicator} not found in data")

        return available_indicators

    def _export_professional_signals_to_excel(self, signal_results: Dict[str, Any],
                                             inputs: Dict[str, Any]) -> str:
        """Export professional signals to Excel with separate sheets for each signal type"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"professional_signals_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: All Confirmed Signals
                all_signals = signal_results.get('signals', [])
                if all_signals:
                    all_df = pd.DataFrame(all_signals)
                    # Sort by available columns
                    sort_columns = []
                    if 'confirmation_strength' in all_df.columns:
                        sort_columns.append('confirmation_strength')
                    if 'reversal_strength' in all_df.columns:
                        sort_columns.append('reversal_strength')

                    if sort_columns:
                        all_df = all_df.sort_values(sort_columns, ascending=False)
                    all_df.to_excel(writer, sheet_name='All_Confirmed_Signals', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'confirmation_strength'])
                    empty_df.to_excel(writer, sheet_name='All_Confirmed_Signals', index=False)

                # Sheet 2: Reversal Signals Only
                reversal_signals = signal_results.get('reversal_signals', [])
                if reversal_signals:
                    reversal_df = pd.DataFrame(reversal_signals)
                    reversal_df = reversal_df.sort_values(['confirmation_strength', 'reversal_strength'], ascending=False)
                    reversal_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'reversal_strength'])
                    empty_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)

                # Sheet 3: Breakout Signals Only
                breakout_signals = signal_results.get('breakout_signals', [])
                if breakout_signals:
                    breakout_df = pd.DataFrame(breakout_signals)
                    breakout_df = breakout_df.sort_values(['confirmation_strength'], ascending=False)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'confirmation_strength'])
                    empty_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

                # Sheet 4: Higher Timeframe Confirmations
                confirmation_data = []
                for signal in all_signals:
                    confirmation_data.append({
                        'Indicator': signal.get('indicator', ''),
                        'Signal_Type': signal.get('signal_type', ''),
                        'Primary_Timeframe': signal.get('primary_timeframe', ''),
                        'Entry_Time': signal.get('entry_time', ''),
                        'Peak_Value': signal.get('peak_value', 0),
                        'Entry_Value': signal.get('entry_value', 0),
                        'Reversal_Strength': signal.get('reversal_strength', 0),
                        'Confirmation_Strength': signal.get('confirmation_strength', 0),
                        'Higher_Timeframes_Confirmed': ', '.join(signal.get('confirmed_by_higher_timeframes', [])),
                        'Entry_Price': signal.get('entry_price', 0),
                        'Stop_Loss': signal.get('stop_loss', 0),
                        'Target': signal.get('target', 0),
                        'Risk_Reward_Ratio': signal.get('risk_reward_ratio', 0)
                    })

                if confirmation_data:
                    conf_df = pd.DataFrame(confirmation_data)
                    conf_df.to_excel(writer, sheet_name='Higher_Timeframe_Confirmations', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['Indicator', 'Signal_Type', 'Primary_Timeframe', 'Confirmation_Strength'])
                    empty_df.to_excel(writer, sheet_name='Higher_Timeframe_Confirmations', index=False)

                # Sheet 5: Professional Thresholds by Timeframe
                threshold_data = []
                from professional_reversal_breakout_detector import ProfessionalReversalBreakoutDetector
                detector = ProfessionalReversalBreakoutDetector()

                for timeframe, indicators_dict in detector.professional_timeframe_thresholds.items():
                    for indicator, thresholds in indicators_dict.items():
                        threshold_data.append({
                            'Timeframe': timeframe,
                            'Indicator': indicator,
                            'Reversal_Detection': thresholds['reversal_detection'],
                            'Reversal_Confirmation': thresholds['reversal_confirmation'],
                            'Breakout_Avoidance': thresholds['breakout_avoidance'],
                            'Overbought_Detection': thresholds['overbought_reversal_detection'],
                            'Overbought_Confirmation': thresholds['overbought_reversal_confirmation'],
                            'Overbought_Breakout_Avoidance': thresholds['overbought_breakout_avoidance'],
                            'Min_Reversal_Strength': thresholds['min_reversal_strength'],
                            'Stop_Loss_Pct': thresholds['stop_loss_pct'],
                            'Target_Pct': thresholds['target_pct']
                        })

                threshold_df = pd.DataFrame(threshold_data)
                threshold_df.to_excel(writer, sheet_name='Professional_Thresholds', index=False)

                # Sheet 6: Pattern Analysis Summary
                pattern_analysis = signal_results.get('pattern_analysis', {})
                analysis_data = []
                for key, analysis in pattern_analysis.items():
                    analysis_data.append({
                        'Indicator_Timeframe': key,
                        'Total_Patterns': analysis.get('total_patterns', 0),
                        'Confirmed_Patterns': analysis.get('confirmed_patterns', 0),
                        'Confirmation_Rate': f"{analysis.get('confirmation_rate', 0):.1%}",
                        'Higher_Timeframes_Used': ', '.join(analysis.get('higher_timeframes_used', []))
                    })

                if analysis_data:
                    analysis_df = pd.DataFrame(analysis_data)
                    analysis_df.to_excel(writer, sheet_name='Pattern_Analysis', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['Indicator_Timeframe', 'Total_Patterns', 'Confirmed_Patterns'])
                    empty_df.to_excel(writer, sheet_name='Pattern_Analysis', index=False)

                # Sheet 7: Summary Statistics
                summary_data = {
                    'Metric': [
                        'Total Confirmed Signals',
                        'Reversal Signals',
                        'Breakout Signals',
                        'Average Confirmation Strength',
                        'Average Reversal Strength',
                        'Signals with Higher TF Confirmation'
                    ],
                    'Value': [
                        signal_results.get('total_signals', 0),
                        signal_results.get('total_reversal_signals', 0),
                        signal_results.get('total_breakout_signals', 0),
                        f"{np.mean([s.get('confirmation_strength', 0) for s in all_signals]) if all_signals else 0:.1%}",
                        f"{np.mean([s.get('reversal_strength', 0) for s in all_signals]) if all_signals else 0:.2f}",
                        len([s for s in all_signals if s.get('confirmed_by_higher_timeframes')])
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            print(f"✅ Professional signals Excel file created: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""

    def _export_ml_enhanced_analysis_to_excel(self, signal_results: Dict[str, Any],
                                            inputs: Dict[str, Any]) -> str:
        """Export ML-enhanced analysis results to Excel"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ml_enhanced_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: All Signals with Real Time
                signals_df = pd.DataFrame(signal_results['signals'])
                if not signals_df.empty:
                    signals_df = signals_df.sort_values(['generation_time', 'timeframe'])
                signals_df.to_excel(writer, sheet_name='ML_Enhanced_Signals', index=False)

                # Sheet 2: Reversal Signals Only
                reversal_signals = [s for s in signal_results['signals'] if s['strategy'] == 'REVERSAL']
                if reversal_signals:
                    reversal_df = pd.DataFrame(reversal_signals)
                    reversal_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)

                # Sheet 3: Breakout Signals Only
                breakout_signals = [s for s in signal_results['signals'] if s['strategy'] == 'BREAKOUT']
                if breakout_signals:
                    breakout_df = pd.DataFrame(breakout_signals)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

                # Sheet 4: ML Optimization Results
                ml_results = []
                for key, result in signal_results['ml_optimization_results'].items():
                    if isinstance(result, dict):
                        for strategy, strategy_result in result.items():
                            if isinstance(strategy_result, dict) and 'accuracy' in strategy_result:
                                ml_results.append({
                                    'Indicator_Timeframe': key,
                                    'Strategy': strategy,
                                    'ML_Accuracy': strategy_result.get('accuracy', 0),
                                    'ML_Precision': strategy_result.get('precision', 0),
                                    'ML_Recall': strategy_result.get('recall', 0),
                                    'Feature_Importance': strategy_result.get('feature_importance', 0),
                                    'Optimized_Oversold': strategy_result.get('optimized_thresholds', {}).get('oversold', 0),
                                    'Optimized_Overbought': strategy_result.get('optimized_thresholds', {}).get('overbought', 0),
                                    'Optimized_Oversold_Exit': strategy_result.get('optimized_thresholds', {}).get('oversold_exit', 0),
                                    'Optimized_Overbought_Exit': strategy_result.get('optimized_thresholds', {}).get('overbought_exit', 0)
                                })

                if ml_results:
                    ml_df = pd.DataFrame(ml_results)
                    ml_df.to_excel(writer, sheet_name='ML_Optimization_Results', index=False)

                # Sheet 5: Summary Statistics
                summary_data = {
                    'Metric': ['Total Signals', 'Reversal Signals', 'Breakout Signals', 'Buy Signals', 'Sell Signals'],
                    'Count': [
                        signal_results['total_signals'],
                        len(reversal_signals),
                        len(breakout_signals),
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'BUY']),
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'SELL'])
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            print(f"✅ Excel file created: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            return ""

    def _print_ml_enhanced_summary(self, signal_results: Dict[str, Any]):
        """Print enhanced summary of ML results"""

        print(f"\n📊 ML-ENHANCED ANALYSIS SUMMARY")
        print("=" * 80)

        signals = signal_results['signals']

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   📊 Total Signals: {len(signals)}")
        print(f"   🔄 Reversal Signals: {len([s for s in signals if s['strategy'] == 'REVERSAL'])}")
        print(f"   📈 Breakout Signals: {len([s for s in signals if s['strategy'] == 'BREAKOUT'])}")
        print(f"   🟢 Buy Signals: {len([s for s in signals if s['signal_type'] == 'BUY'])}")
        print(f"   🔴 Sell Signals: {len([s for s in signals if s['signal_type'] == 'SELL'])}")

        # Show top signals with real time
        print(f"\n🏆 TOP SIGNALS WITH REAL TIME FORMAT:")

        # Sort by strength and show top 10
        sorted_signals = sorted(signals, key=lambda x: x.get('strength', 0), reverse=True)[:10]

        for i, signal in enumerate(sorted_signals, 1):
            print(f"\n   {i:2d}. {signal['indicator']} - {signal['signal_type']} ({signal['strategy']})")
            print(f"       ⏰ Generation: {signal['generation_time']}")
            print(f"       ✅ Confirmation: {signal['confirmation_time']}")
            print(f"       🎯 Entry: {signal['entry_time']}")
            print(f"       💰 Price: {signal['current_price']}")
            print(f"       🛑 Stop Loss: {signal['stop_loss']} ({signal['stop_loss_pct']}%)")
            print(f"       🎯 Target: {signal['target']} ({signal['target_pct']}%)")
            print(f"       📊 Strength: {signal['strength']:.3f}")
            print(f"       📝 Reason: {signal['reason']}")

        # Show ML optimization results
        print(f"\n🤖 ML OPTIMIZATION RESULTS:")
        for key, result in signal_results['ml_optimization_results'].items():
            if isinstance(result, dict):
                print(f"\n   📊 {key}:")
                for strategy, strategy_result in result.items():
                    if isinstance(strategy_result, dict) and 'accuracy' in strategy_result:
                        accuracy = strategy_result.get('accuracy', 0)
                        precision = strategy_result.get('precision', 0)
                        thresholds = strategy_result.get('optimized_thresholds', {})

                        print(f"      🔄 {strategy.upper()}:")
                        print(f"         🎯 ML Accuracy: {accuracy:.3f}")
                        print(f"         📊 ML Precision: {precision:.3f}")
                        print(f"         📉 Optimized Oversold: {thresholds.get('oversold', 0):.2f}")
                        print(f"         📈 Optimized Overbought: {thresholds.get('overbought', 0):.2f}")

    def _export_comprehensive_analysis_to_excel(self, signal_results: Dict[str, Any],
                                              inputs: Dict[str, Any]) -> str:
        """Export comprehensive multi-timeframe analysis results to Excel with all required sheets"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_multi_timeframe_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: ML Enhanced Signals
                ml_enhanced_signals = signal_results.get('ml_enhanced_signals', [])
                if ml_enhanced_signals:
                    ml_df = pd.DataFrame(ml_enhanced_signals)
                    ml_df = ml_df.sort_values(['ml_enhancement_score', 'confirmation_strength'], ascending=False)
                    ml_df.to_excel(writer, sheet_name='ML_Enhanced_Signals', index=False)
                else:
                    # Create empty sheet with headers
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'ml_enhancement_score'])
                    empty_df.to_excel(writer, sheet_name='ML_Enhanced_Signals', index=False)

                # Sheet 2: Reversal Signals
                reversal_signals = signal_results.get('reversal_signals', [])
                if reversal_signals:
                    reversal_df = pd.DataFrame(reversal_signals)
                    reversal_df = reversal_df.sort_values(['confirmation_strength', 'reversal_strength'], ascending=False)
                    reversal_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'reversal_strength'])
                    empty_df.to_excel(writer, sheet_name='Reversal_Signals', index=False)

                # Sheet 3: Breakout Signals
                breakout_signals = signal_results.get('breakout_signals', [])
                if breakout_signals:
                    breakout_df = pd.DataFrame(breakout_signals)
                    breakout_df = breakout_df.sort_values(['confirmation_strength'], ascending=False)
                    breakout_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['indicator', 'signal_type', 'strategy', 'entry_time', 'confirmation_strength'])
                    empty_df.to_excel(writer, sheet_name='Breakout_Signals', index=False)

                # Sheet 4: ML Optimization Results
                ml_optimization_data = []
                optimized_combinations = signal_results.get('optimized_combinations', {})

                for indicator, combinations in optimized_combinations.items():
                    for i, combo in enumerate(combinations):
                        ml_optimization_data.append({
                            'Indicator': indicator,
                            'Combination_ID': f"{indicator}_Combo_{i+1}",
                            'Timeframes': ', '.join(combo.get('timeframes', [])),
                            'Confidence_Score': combo.get('confidence_score', 0),
                            'Sample_Count': combo.get('sample_count', 0),
                            'Mean_Values': str(combo.get('mean_values', [])),
                            'Cluster_ID': combo.get('cluster_id', 0)
                        })

                if ml_optimization_data:
                    ml_opt_df = pd.DataFrame(ml_optimization_data)
                    ml_opt_df.to_excel(writer, sheet_name='ML_Optimization_Results', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['Indicator', 'Combination_ID', 'Timeframes', 'Confidence_Score'])
                    empty_df.to_excel(writer, sheet_name='ML_Optimization_Results', index=False)

                # Sheet 5: Multi-Timeframe Confirmations
                confirmed_signals = signal_results.get('confirmed_signals', [])
                confirmation_data = []

                for signal in confirmed_signals:
                    confirmations = signal.get('confirmations', {})
                    confirmation_data.append({
                        'Indicator': signal.get('indicator', ''),
                        'Signal_Type': signal.get('signal_type', ''),
                        'Primary_Timeframe': signal.get('primary_timeframe', ''),
                        'Entry_Time': signal.get('entry_time', ''),
                        'Confirmation_Strength': signal.get('confirmation_strength', 0),
                        'Confirming_Timeframes': ', '.join(confirmations.get('confirming_timeframes', [])),
                        'Total_Confirmations': len(confirmations.get('confirming_timeframes', [])),
                        'Reversal_Strength': signal.get('reversal_strength', 0),
                        'Peak_Value': signal.get('peak_value', 0),
                        'Entry_Value': signal.get('entry_value', 0)
                    })

                if confirmation_data:
                    conf_df = pd.DataFrame(confirmation_data)
                    conf_df = conf_df.sort_values(['Confirmation_Strength', 'Total_Confirmations'], ascending=False)
                    conf_df.to_excel(writer, sheet_name='Multi_Timeframe_Confirmations', index=False)
                else:
                    empty_df = pd.DataFrame(columns=['Indicator', 'Signal_Type', 'Primary_Timeframe', 'Confirmation_Strength'])
                    empty_df.to_excel(writer, sheet_name='Multi_Timeframe_Confirmations', index=False)

                # Sheet 6: Professional Thresholds
                threshold_data = []
                from advanced_multi_timeframe_confirmation_system import AdvancedMultiTimeframeConfirmationSystem
                system = AdvancedMultiTimeframeConfirmationSystem()

                for timeframe, indicators_dict in system.professional_timeframe_thresholds.items():
                    for indicator, thresholds in indicators_dict.items():
                        threshold_data.append({
                            'Timeframe': timeframe,
                            'Indicator': indicator,
                            'Reversal_Detection': thresholds['reversal_detection'],
                            'Reversal_Confirmation': thresholds['reversal_confirmation'],
                            'Breakout_Avoidance': thresholds['breakout_avoidance'],
                            'Overbought_Detection': thresholds['overbought_reversal_detection'],
                            'Overbought_Confirmation': thresholds['overbought_reversal_confirmation'],
                            'Overbought_Breakout_Avoidance': thresholds['overbought_breakout_avoidance'],
                            'Min_Reversal_Strength': thresholds['min_reversal_strength'],
                            'Stop_Loss_Pct': thresholds['stop_loss_pct'],
                            'Target_Pct': thresholds['target_pct']
                        })

                threshold_df = pd.DataFrame(threshold_data)
                threshold_df.to_excel(writer, sheet_name='Professional_Thresholds', index=False)

                # Sheet 7: Summary Statistics
                accuracy_metrics = signal_results.get('accuracy_metrics', {})
                summary_data = {
                    'Metric': [
                        'Total Confirmed Signals',
                        'ML Enhanced Signals',
                        'Reversal Signals',
                        'Breakout Signals',
                        'Accuracy',
                        'Precision',
                        'Recall',
                        'High Confidence Rate'
                    ],
                    'Value': [
                        signal_results.get('total_confirmed_signals', 0),
                        len(ml_enhanced_signals),
                        len(reversal_signals),
                        len(breakout_signals),
                        f"{accuracy_metrics.get('accuracy', 0):.1%}",
                        f"{accuracy_metrics.get('precision', 0):.1%}",
                        f"{accuracy_metrics.get('recall', 0):.1%}",
                        f"{accuracy_metrics.get('high_confidence_rate', 0):.1%}"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            print(f"✅ Comprehensive Excel file created: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""

    def _export_professional_analysis_to_excel_old(self, signal_results: Dict[str, Any],
                                             inputs: Dict[str, Any]) -> str:
        """Export professional reversal analysis results to Excel"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"professional_reversal_analysis_{inputs['ticker']}_{inputs['exchange']}_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # Sheet 1: Professional Reversal Patterns
                signals_df = pd.DataFrame(signal_results['signals'])
                if not signals_df.empty:
                    signals_df = signals_df.sort_values(['peak_time', 'entry_time'])
                signals_df.to_excel(writer, sheet_name='Professional_Reversal_Patterns', index=False)

                # Sheet 2: Pattern Analysis Summary
                pattern_analysis = []
                for key, analysis in signal_results['pattern_analysis'].items():
                    pattern_analysis.append({
                        'Indicator_Timeframe': key,
                        'Total_Patterns': analysis['total_patterns'],
                        'Validated_Patterns': analysis['validated_patterns'],
                        'Validation_Rate': f"{analysis['validation_rate']:.1%}"
                    })

                if pattern_analysis:
                    analysis_df = pd.DataFrame(pattern_analysis)
                    analysis_df.to_excel(writer, sheet_name='Pattern_Analysis', index=False)

                # Sheet 3: Threshold Configuration
                threshold_data = []
                from professional_reversal_breakout_detector import ProfessionalReversalBreakoutDetector
                detector = ProfessionalReversalBreakoutDetector()

                for indicator, thresholds in detector.professional_thresholds.items():
                    threshold_data.append({
                        'Indicator': indicator,
                        'Reversal_Detection': thresholds['reversal_detection'],
                        'Reversal_Confirmation': thresholds['reversal_confirmation'],
                        'Breakout_Avoidance': thresholds['breakout_avoidance'],
                        'Overbought_Detection': thresholds['overbought_reversal_detection'],
                        'Overbought_Confirmation': thresholds['overbought_reversal_confirmation'],
                        'Overbought_Breakout_Avoidance': thresholds['overbought_breakout_avoidance'],
                        'Min_Reversal_Strength': thresholds['min_reversal_strength'],
                        'Stop_Loss_Pct': thresholds['stop_loss_pct'],
                        'Target_Pct': thresholds['target_pct']
                    })

                threshold_df = pd.DataFrame(threshold_data)
                threshold_df.to_excel(writer, sheet_name='Professional_Thresholds', index=False)

                # Sheet 4: Summary Statistics
                summary_data = {
                    'Metric': ['Total Patterns', 'Buy Patterns', 'Sell Patterns', 'Avg Validation Rate'],
                    'Value': [
                        signal_results['total_signals'],
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'BUY']),
                        len([s for s in signal_results['signals'] if s['signal_type'] == 'SELL']),
                        f"{np.mean([a['validation_rate'] for a in signal_results['pattern_analysis'].values()]):.1%}"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            print(f"✅ Professional Excel file created: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            return ""

    def _print_professional_summary(self, signal_results: Dict[str, Any]):
        """Print professional summary of reversal patterns"""

        print(f"\n📊 PROFESSIONAL REVERSAL PATTERN ANALYSIS SUMMARY")
        print("=" * 80)

        signals = signal_results['signals']

        print(f"🎯 PATTERN SUMMARY:")
        print(f"   📊 Total Professional Patterns: {len(signals)}")
        print(f"   🟢 Buy Patterns (Oversold Reversals): {len([s for s in signals if s['signal_type'] == 'BUY'])}")
        print(f"   🔴 Sell Patterns (Overbought Reversals): {len([s for s in signals if s['signal_type'] == 'SELL'])}")

        # Show top patterns with professional details
        print(f"\n🏆 TOP PROFESSIONAL REVERSAL PATTERNS:")

        # Sort by validation score and reversal strength
        sorted_signals = sorted(signals, key=lambda x: (x.get('validation_score', 0) + x.get('confirmation_strength', 0)), reverse=True)[:10]

        for i, signal in enumerate(sorted_signals, 1):
            print(f"\n   {i:2d}. {signal['indicator']} - {signal['signal_type']} ({signal['pattern_type']})")
            print(f"       🔍 Peak: {signal['peak_value']:.2f} at {signal['peak_time']}")
            print(f"       ✅ Entry: {signal['entry_value']:.2f} at {signal['entry_time']}")
            print(f"       💪 Reversal Strength: {signal['reversal_strength']:.2f}")
            print(f"       📊 Confirmation: {signal['confirmation_strength']:.1%}")
            print(f"       💰 Entry Price: {signal.get('entry_price', 'N/A')}")
            print(f"       🛑 Stop Loss: {signal.get('stop_loss', 'N/A')} ({signal['stop_loss_pct']}%)")
            print(f"       🎯 Target: {signal.get('target', 'N/A')} ({signal['target_pct']}%)")
            print(f"       ⚖️  Risk/Reward: 1:{signal.get('risk_reward_ratio', 'N/A')}")

            # Show thresholds used
            thresholds = signal['thresholds_used']
            print(f"       📏 Thresholds: Detection {thresholds['detection']}, Confirmation {thresholds['confirmation']}, Breakout Avoidance {thresholds['breakout_avoidance']}")

        # Show pattern analysis
        print(f"\n📈 PATTERN VALIDATION ANALYSIS:")
        for indicator_tf, analysis in signal_results['pattern_analysis'].items():
            print(f"   📊 {indicator_tf}:")
            print(f"      🔍 Total Patterns: {analysis['total_patterns']}")
            print(f"      ✅ Validated: {analysis['validated_patterns']}")
            print(f"      📊 Validation Rate: {analysis['validation_rate']:.1%}")

        print(f"\n💡 PROFESSIONAL TRADING INSIGHTS:")
        print(f"   🔄 Entry only on confirmed reversals (not initial signals)")
        print(f"   🛑 No entry if breakout thresholds reached")
        print(f"   📊 Peak confirmation required before entry")
        print(f"   💪 Minimum reversal strength validation")
        print(f"   ⚖️  Professional risk/reward ratios")

    def _print_comprehensive_summary(self, signal_results: Dict[str, Any]):
        """Print comprehensive summary of multi-timeframe analysis"""

        print(f"\n📊 COMPREHENSIVE MULTI-TIMEFRAME ANALYSIS SUMMARY")
        print("=" * 80)

        # Overall statistics
        total_confirmed = signal_results.get('total_confirmed_signals', 0)
        ml_enhanced = len(signal_results.get('ml_enhanced_signals', []))
        reversal_signals = len(signal_results.get('reversal_signals', []))
        breakout_signals = len(signal_results.get('breakout_signals', []))

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   📊 Total Confirmed Signals: {total_confirmed}")
        print(f"   🤖 ML Enhanced Signals: {ml_enhanced}")
        print(f"   🔄 Reversal Signals: {reversal_signals}")
        print(f"   📈 Breakout Signals: {breakout_signals}")

        # Accuracy metrics
        accuracy_metrics = signal_results.get('accuracy_metrics', {})
        print(f"\n📈 ACCURACY METRICS:")
        print(f"   🎯 Accuracy: {accuracy_metrics.get('accuracy', 0):.1%}")
        print(f"   📊 Precision: {accuracy_metrics.get('precision', 0):.1%}")
        print(f"   🔍 Recall: {accuracy_metrics.get('recall', 0):.1%}")
        print(f"   💪 High Confidence Rate: {accuracy_metrics.get('high_confidence_rate', 0):.1%}")

        # Top ML enhanced signals
        ml_signals = signal_results.get('ml_enhanced_signals', [])
        if ml_signals:
            print(f"\n🏆 TOP ML ENHANCED SIGNALS:")

            # Sort by ML enhancement score
            sorted_ml_signals = sorted(ml_signals, key=lambda x: x.get('ml_enhancement_score', 0), reverse=True)[:5]

            for i, signal in enumerate(sorted_ml_signals, 1):
                print(f"\n   {i:2d}. {signal.get('indicator', 'N/A')} - {signal.get('signal_type', 'N/A')}")
                print(f"       ⏰ Entry Time: {signal.get('entry_time', 'N/A')}")
                print(f"       🎯 Primary Timeframe: {signal.get('primary_timeframe', 'N/A')}")
                print(f"       🤖 ML Score: {signal.get('ml_enhancement_score', 0):.3f}")
                print(f"       ✅ Confirmation Strength: {signal.get('confirmation_strength', 0):.1%}")
                print(f"       💪 Reversal Strength: {signal.get('reversal_strength', 0):.2f}")

                # Show confirming timeframes
                confirming_tfs = signal.get('confirmed_by_timeframes', [])
                if confirming_tfs:
                    print(f"       📊 Confirmed by: {', '.join(confirming_tfs)}")

        # ML optimization results
        optimized_combinations = signal_results.get('optimized_combinations', {})
        if optimized_combinations:
            print(f"\n🤖 ML OPTIMIZATION RESULTS:")
            for indicator, combinations in optimized_combinations.items():
                print(f"\n   📊 {indicator}:")
                for i, combo in enumerate(combinations[:3], 1):  # Show top 3
                    confidence = combo.get('confidence_score', 0)
                    timeframes = combo.get('timeframes', [])
                    sample_count = combo.get('sample_count', 0)

                    print(f"      {i}. Combination {combo.get('cluster_id', i)}:")
                    print(f"         📈 Timeframes: {', '.join(timeframes)}")
                    print(f"         🎯 Confidence: {confidence:.1%}")
                    print(f"         📊 Samples: {sample_count}")

        # Multi-timeframe confirmation analysis
        confirmed_signals = signal_results.get('confirmed_signals', [])
        if confirmed_signals:
            print(f"\n📊 MULTI-TIMEFRAME CONFIRMATION ANALYSIS:")

            # Group by indicator
            by_indicator = {}
            for signal in confirmed_signals:
                indicator = signal.get('indicator', 'Unknown')
                if indicator not in by_indicator:
                    by_indicator[indicator] = []
                by_indicator[indicator].append(signal)

            for indicator, signals in by_indicator.items():
                avg_confirmation = np.mean([s.get('confirmation_strength', 0) for s in signals])
                avg_confirmations_count = np.mean([len(s.get('confirmed_by_timeframes', [])) for s in signals])

                print(f"   📊 {indicator}:")
                print(f"      🔍 Total Signals: {len(signals)}")
                print(f"      ✅ Avg Confirmation Strength: {avg_confirmation:.1%}")
                print(f"      📈 Avg Confirming Timeframes: {avg_confirmations_count:.1f}")

        print(f"\n💡 PROFESSIONAL INSIGHTS:")
        print(f"   📊 Higher timeframe confirmation reduces false signals")
        print(f"   🤖 ML enhancement identifies highest probability setups")
        print(f"   🔄 Separate reversal/breakout analysis prevents strategy confusion")
        print(f"   🎯 Professional timeframe-specific thresholds improve accuracy")
        print(f"   ⚖️  Multi-timeframe validation ensures signal quality")

    def _print_professional_signals_summary(self, signal_results: Dict[str, Any]):
        """Print professional summary of signals with higher timeframe confirmation"""

        print(f"\n📊 PROFESSIONAL SIGNALS ANALYSIS SUMMARY")
        print("=" * 80)

        # Overall statistics
        total_signals = signal_results.get('total_signals', 0)
        reversal_signals = signal_results.get('total_reversal_signals', 0)
        breakout_signals = signal_results.get('total_breakout_signals', 0)

        print(f"🎯 SIGNAL SUMMARY:")
        print(f"   📊 Total Confirmed Signals: {total_signals}")
        print(f"   🔄 Reversal Signals: {reversal_signals}")
        print(f"   📈 Breakout Signals: {breakout_signals}")

        # Show top signals
        all_signals = signal_results.get('signals', [])
        if all_signals:
            print(f"\n🏆 TOP PROFESSIONAL SIGNALS WITH HIGHER TIMEFRAME CONFIRMATION:")

            # Sort by confirmation strength and reversal strength
            sorted_signals = sorted(all_signals,
                                  key=lambda x: (x.get('confirmation_strength', 0) + x.get('reversal_strength', 0)),
                                  reverse=True)[:10]

            for i, signal in enumerate(sorted_signals, 1):
                print(f"\n   {i:2d}. {signal.get('indicator', 'N/A')} - {signal.get('signal_type', 'N/A')} ({signal.get('strategy', 'N/A')})")
                print(f"       🕐 Primary Timeframe: {signal.get('primary_timeframe', 'N/A')}")
                print(f"       🔍 Peak: {signal.get('peak_value', 0):.2f} at {signal.get('peak_time', 'N/A')}")
                print(f"       ✅ Entry: {signal.get('entry_value', 0):.2f} at {signal.get('entry_time', 'N/A')}")
                print(f"       💪 Reversal Strength: {signal.get('reversal_strength', 0):.2f}")
                print(f"       📊 Confirmation Strength: {signal.get('confirmation_strength', 0):.1%}")

                # Show higher timeframe confirmations
                htf_confirmations = signal.get('confirmed_by_higher_timeframes', [])
                if htf_confirmations:
                    print(f"       ⬆️ Confirmed by: {', '.join(htf_confirmations)}")

                # Show trading parameters
                entry_price = signal.get('entry_price', 0)
                stop_loss = signal.get('stop_loss', 0)
                target = signal.get('target', 0)
                if entry_price and stop_loss and target:
                    print(f"       💰 Entry: {entry_price}, SL: {stop_loss}, Target: {target}")
                    print(f"       ⚖️ Risk/Reward: 1:{signal.get('risk_reward_ratio', 0):.2f}")

        # Pattern analysis summary
        pattern_analysis = signal_results.get('pattern_analysis', {})
        if pattern_analysis:
            print(f"\n📈 PATTERN ANALYSIS BY TIMEFRAME:")
            for indicator_tf, analysis in pattern_analysis.items():
                total_patterns = analysis.get('total_patterns', 0)
                confirmed_patterns = analysis.get('confirmed_patterns', 0)
                confirmation_rate = analysis.get('confirmation_rate', 0)
                htf_used = analysis.get('higher_timeframes_used', [])

                print(f"   📊 {indicator_tf}:")
                print(f"      🔍 Total Patterns: {total_patterns}")
                print(f"      ✅ Confirmed: {confirmed_patterns}")
                print(f"      📊 Confirmation Rate: {confirmation_rate:.1%}")
                if htf_used:
                    print(f"      ⬆️ Higher TFs Used: {', '.join(htf_used)}")

        print(f"\n💡 PROFESSIONAL INSIGHTS:")
        print(f"   📊 Higher timeframe confirmation filters false signals")
        print(f"   🎯 Professional timeframe-specific thresholds improve accuracy")
        print(f"   🔄 Separate reversal/breakout analysis prevents strategy confusion")
        print(f"   💪 Reversal strength validation ensures meaningful patterns")
        print(f"   ⚖️ Risk/reward ratios calculated for position sizing")

    def run_complete_workflow(self) -> bool:
        """Run the complete enhanced multi-interval workflow"""
        
        print("🚀 ENHANCED MULTI-INTERVAL PROFESSIONAL ANALYZER")
        print("=" * 80)
        print("📊 Step 1: Get user inputs")
        print("🔄 Step 2: Generate separate files for each interval")
        print("🎯 Step 3: Advanced professional signal analysis")
        print("📄 Step 4: Comprehensive Excel reports")
        
        # Step 1: Get user inputs
        inputs = self.get_user_inputs()
        
        if not inputs:
            print("❌ Invalid inputs provided")
            return False
        
        # Step 2: Generate interval data
        interval_files = self.generate_interval_data(inputs)
        
        if not interval_files:
            print("❌ No interval files generated")
            return False
        
        # Step 3: Advanced analysis (if requested)
        if inputs['run_advanced_analysis']:
            success = self.run_advanced_professional_analysis(inputs, interval_files)
            
            if success:
                print(f"\n✅ ENHANCED MULTI-INTERVAL WORKFLOW COMPLETED!")
                print("=" * 80)
                print("🎯 WORKFLOW SUMMARY:")
                print(f"   📊 Step 1: ✅ User inputs collected")
                print(f"   🔄 Step 2: ✅ {len(interval_files)} separate interval files generated")
                print(f"   🎯 Step 3: ✅ Advanced professional signal analysis completed")
                print(f"   📄 Step 4: ✅ Comprehensive Excel reports generated")
                
                print(f"\n🏆 KEY ACHIEVEMENTS:")
                print("   📈 Separate files for each interval (no sampling)")
                print("   ⏰ Proper interval-specific data for each timeframe")
                print("   🔄 PGO-style reversal detection with real data")
                print("   📊 Data-driven threshold optimization per interval")
                print("   🎯 Professional signal analysis with timing")
                
                return True
            else:
                print(f"\n⚠️  Interval files generated but advanced analysis failed")
                return False
        else:
            print(f"\n✅ INTERVAL DATA GENERATION COMPLETED!")
            print("=" * 60)
            print(f"📊 Generated {len(interval_files)} separate interval files")
            print("💡 Run advanced analysis separately if needed")
            return True


def main():
    """Main execution function"""
    
    analyzer = EnhancedMultiIntervalProfessionalAnalyzer()
    success = analyzer.run_complete_workflow()
    
    if success:
        print(f"\n🎉 SUCCESS! Enhanced Multi-Interval Analysis completed!")
    else:
        print(f"\n❌ Analysis failed.")


if __name__ == "__main__":
    main()
